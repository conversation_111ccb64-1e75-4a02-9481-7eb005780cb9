/// <reference types="vitest" />
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';
import { fileURLToPath } from 'url';

const __dirname = path.dirname(fileURLToPath(import.meta.url));

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      'react-router-dom',
      'lucide-react',
      '@radix-ui/react-slot',
      '@radix-ui/react-toast',
      '@radix-ui/react-dialog',
      '@radix-ui/react-dropdown-menu',
      '@radix-ui/react-select',
      'framer-motion',
      'class-variance-authority',
      'tailwind-merge',
      'clsx',
      'axios',
      'react-intl',
      'date-fns',
    ],
    force: true,
  },
  server: {
    fs: {
      strict: false,
    },
  },
  build: {
    rollupOptions: {
      output: {
        manualChunks: id => {
          // // Vendor chunks for better caching
          // if (id.includes('node_modules')) {
          //   // Large UI libraries
          //   if (id.includes('@radix-ui')) {
          //     return 'radix-ui';
          //   }
          //   if (id.includes('framer-motion')) {
          //     return 'framer-motion';
          //   }
          //   if (id.includes('lucide-react')) {
          //     return 'lucide-icons';
          //   }
          //   if (id.includes('react-intl')) {
          //     return 'react-intl';
          //   }
          //   // React ecosystem
          //   if (id.includes('react') || id.includes('react-dom') || id.includes('react-router')) {
          //     return 'react-vendor';
          //   }
          //   // Utility libraries
          //   if (id.includes('axios') || id.includes('date-fns') || id.includes('clsx') || id.includes('tailwind-merge')) {
          //     return 'utils-vendor';
          //   }
          //   // Other vendor libraries
          //   return 'vendor';
          // }

          // App chunks for better code splitting
          if (id.includes('src/components/common/markdown-renderer')) {
            return 'markdown-renderer';
          }

          if (id.includes('src/pages/demo')) {
            return 'demo-pages';
          }

          if (id.includes('src/pages/public')) {
            return 'public-pages';
          }

          if (id.includes('src/pages/user')) {
            return 'user-pages';
          }

          if (id.includes('src/pages/auth')) {
            return 'auth-pages';
          }

          if (id.includes('src/data/public')) {
            return 'public-data';
          }

          if (id.includes('src/utils') && id.includes('localization')) {
            return 'localization';
          }

          if (id.includes('src/utils') && id.includes('error-reporting')) {
            return 'error-reporting';
          }

          if (id.includes('src/services')) {
            return 'api-services';
          }

          if (id.includes('src/components/examples')) {
            return 'examples';
          }

          if (id.includes('src/utils')) {
            return 'app-utils';
          }
        },
      },
    },
    // Optimize chunk size warning limit - increased for vendor chunks
    chunkSizeWarningLimit: 800,
    // Enable CSS code splitting
    cssCodeSplit: true,
  },
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: ['./src/test/setup.ts'],
    css: true,
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      exclude: [
        'node_modules/',
        'src/test/',
        '**/*.d.ts',
        '**/*.test.{ts,tsx}',
        '**/*.spec.{ts,tsx}',
      ],
    },
  },
});

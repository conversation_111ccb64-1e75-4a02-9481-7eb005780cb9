import { defineConfig } from 'vite';
import { resolve } from 'path';
import { fileURLToPath } from 'url';
import react from '@vitejs/plugin-react';

const __dirname = fileURLToPath(new URL('.', import.meta.url));

/**
 * Bundle analyzer configuration for Vite
 */
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': resolve(__dirname, './src'),
    },
  },
  build: {
    rollupOptions: {
      output: {
        manualChunks: id => {
          // Vendor node_modules
          if (id.includes('node_modules')) {
            // Core React libraries
            if (id.includes('react') || id.includes('react-dom')) {
              return 'react-core';
            }

            // Router
            if (id.includes('react-router-dom')) {
              return 'react-router';
            }

            // Radix UI components
            if (id.includes('@radix-ui')) {
              return 'ui-radix';
            }

            // Framer Motion
            if (id.includes('framer-motion') || id.includes('motion')) {
              return 'animations';
            }

            // Markdown processing (large dependency)
            if (
              id.includes('remark') ||
              id.includes('rehype') ||
              id.includes('unified') ||
              id.includes('micromark') ||
              id.includes('mdast')
            ) {
              return 'markdown';
            }

            // Icons
            if (
              id.includes('lucide-react') ||
              id.includes('@tabler/icons-react')
            ) {
              return 'icons';
            }

            // Utilities
            if (
              id.includes('clsx') ||
              id.includes('class-variance-authority') ||
              id.includes('tailwind-merge')
            ) {
              return 'utils';
            }

            // Date/Time
            if (id.includes('date-fns')) {
              return 'date-fns';
            }

            // Charts
            if (id.includes('recharts')) {
              return 'charts';
            }

            // Other large vendor libraries
            if (
              id.includes('axios') ||
              id.includes('lodash') ||
              id.includes('uuid') ||
              id.includes('jwt-decode')
            ) {
              return 'vendor';
            }

            // All other node_modules
            return 'vendor-misc';
          }

          // App chunks
          if (id.includes('src/components/common/markdown-renderer')) {
            return 'markdown-renderer';
          }

          if (id.includes('src/pages/public')) {
            return 'public-pages';
          }

          if (id.includes('src/pages/user')) {
            return 'user-pages';
          }

          if (id.includes('src/pages/auth')) {
            return 'auth-pages';
          }

          if (id.includes('src/data/public')) {
            return 'public-data';
          }

          if (id.includes('src/utils')) {
            return 'app-utils';
          }
        },
      },
    },
    // Generate source maps for production debugging
    sourcemap: true,

    // Optimize chunks
    chunkSizeWarningLimit: 1000,

    // Enable CSS code splitting
    cssCodeSplit: true,
  },

  // Optimize dependencies
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      'react-router-dom',
      '@radix-ui/react-accordion',
      '@radix-ui/react-alert-dialog',
      '@radix-ui/react-avatar',
      '@radix-ui/react-checkbox',
      '@radix-ui/react-dialog',
      '@radix-ui/react-dropdown-menu',
      'lucide-react',
      'clsx',
      'tailwind-merge',
    ],
    exclude: ['@sentry/react'], // Exclude problematic deps from pre-bundling
  },
});

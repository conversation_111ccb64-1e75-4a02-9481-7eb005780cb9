{"name": "base-portal-dashboard", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "dev:clean": "rm -rf node_modules/.vite && vite", "build": "tsc -b && vite build", "lint": "tsc --noEmit && eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "preview": "vite preview", "update-browsers": "npx update-browserslist-db@latest", "postinstall": "npx update-browserslist-db@latest", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "test:watch": "vitest --watch", "type-check": "tsc --noEmit", "analyze": "npx vite-bundle-analyzer", "prepare": "husky"}, "dependencies": {"@mui/material": "^7.0.2", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-collapsible": "^1.1.1", "@radix-ui/react-context-menu": "^2.2.2", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-hover-card": "^1.1.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.2", "@radix-ui/react-navigation-menu": "^1.2.1", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-scroll-area": "^1.2.1", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.1", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.4", "@reduxjs/toolkit": "^1.9.1", "@tabler/icons-react": "^3.22.0", "axios": "^1.6.8", "chance": "^1.1.12", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "embla-carousel-react": "^8.3.1", "formik": "^2.2.9", "framer-motion": "^8.5.5", "lucide-react": "^0.456.0", "moment": "^2.30.1", "motion": "^12.9.2", "react": "^18.3.1", "react-countup": "^6.5.3", "react-day-picker": "^9.4.0", "react-dom": "^18.3.1", "react-helmet": "^6.1.0", "react-hook-form": "^7.53.2", "react-intl": "^6.2.5", "react-markdown": "^10.1.0", "react-redux": "^8.0.5", "react-resizable-panels": "^2.1.7", "react-router-dom": "^6.28.0", "recharts": "^2.13.3", "redux": "^4.2.0", "redux-persist": "^6.0.0", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.1", "sonner": "^1.7.0", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.1", "yup": "^0.32.11"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@eslint/js": "^9.13.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/chance": "^1.1.3", "@types/node": "^22.9.0", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@types/react-helmet": "^6.1.11", "@typescript-eslint/parser": "^8.34.0", "@vitejs/plugin-react": "^4.3.3", "@vitest/coverage-v8": "^2.1.9", "@vitest/ui": "^2.1.9", "autoprefixer": "^10.4.20", "eslint": "^9.13.0", "eslint-import-resolver-typescript": "^4.4.3", "eslint-plugin-import": "^2.31.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "globals": "^15.11.0", "gridstack": "^11.0.1", "husky": "^9.1.7", "input-otp": "^1.4.1", "jsdom": "^25.0.1", "jwt-decode": "^3.1.2", "lint-staged": "^16.1.2", "postcss": "^8.4.49", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.12", "rollup-plugin-visualizer": "^6.0.3", "tailwindcss": "^3.4.15", "typescript": "~5.6.2", "typescript-eslint": "^8.11.0", "vite": "^5.4.10", "vite-bundle-analyzer": "^0.22.3", "vitest": "^2.1.9"}, "lint-staged": {"*.{ts,tsx,js,jsx}": ["eslint --fix", "prettier --write"], "*.{json,css,md}": ["prettier --write"]}, "engines": {"node": ">=20.0.0", "npm": ">=10.0.0"}}
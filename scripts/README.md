# Portal Dashboard - Scripts

This directory contains utility scripts for the React/TypeScript portal dashboard project.

## 📂 Directory Structure

```
scripts/
├── dev/                        # Development and maintenance scripts
│   └── translation-key-manager.sh  # Translation key management tool
└── README.md                   # This file
```

## 🔧 Development Scripts (`scripts/dev/`)

### `translation-key-manager.sh`

**Purpose**: Translation key analysis and management tool for React/TypeScript project
**Usage**: `./scripts/dev/translation-key-manager.sh [command] [options]`
**Environment**: Development/Maintenance
**Description**: Manages translation keys across the localization system in `src/utils/localization/locales/`

**Commands:**

- `duplicates [--remove]` - Find (and optionally remove) duplicate keys within each language
- `unused [--remove]` - Find (and optionally remove) unused keys not referenced in code
- `missing` - Find missing keys used in code but not in translation files
- `all` - Run all checks (duplicates, unused, missing)

**Options:**

- `--remove` - Apply changes (remove duplicates/unused keys)
- `--verbose` - Show detailed output
- `--help` - Show help message

**Examples:**

```bash
# Find duplicate keys
./scripts/dev/translation-key-manager.sh duplicates

# Remove duplicate keys
./scripts/dev/translation-key-manager.sh duplicates --remove

# Find unused keys with details
./scripts/dev/translation-key-manager.sh unused --verbose

# Remove unused keys
./scripts/dev/translation-key-manager.sh unused --remove

# Find missing keys (never added automatically)
./scripts/dev/translation-key-manager.sh missing

# Run all checks
./scripts/dev/translation-key-manager.sh all
```

**Key Features:**

- **React/TypeScript Support**: Scans `.tsx`, `.ts`, `.jsx`, `.js` files for `intl.formatMessage({ id: '...' })` patterns
- **Multi-language**: Supports English (en), Simplified Chinese (zh), Traditional Chinese (zh-TW)
- **Safe**: Missing keys are never added automatically - manual addition required
- **Accurate**: Handles the project's translation key structure in `src/utils/localization/locales/`
- **Flexible**: Can find issues or apply fixes with `--remove` flag

**Project Structure:**

- Scans: `src/components/` and `src/pages/` for translation key usage
- Manages: `src/utils/localization/locales/{en,zh,zh-TW}/*.json` files

**Requirements:** `jq` (JSON processor)

- macOS: `brew install jq`
- Ubuntu: `apt-get install jq`

---

## 🚀 Quick Start

### Translation Key Management

```bash
# Make script executable
chmod +x scripts/dev/translation-key-manager.sh

# Check for duplicate keys across all languages
./scripts/dev/translation-key-manager.sh duplicates

# Find unused translation keys
./scripts/dev/translation-key-manager.sh unused --verbose

# Find missing translation keys
./scripts/dev/translation-key-manager.sh missing

# Run all checks
./scripts/dev/translation-key-manager.sh all
```

### Development Workflow

```bash
# 1. Start development server
npm run dev

# 2. Add new translation keys to:
#    - src/utils/localization/locales/en/*.json
#    - src/utils/localization/locales/zh/*.json
#    - src/utils/localization/locales/zh-TW/*.json

# 3. Use in components:
#    intl.formatMessage({ id: 'your.translation.key' })

# 4. Check for issues
./scripts/dev/translation-key-manager.sh all
```

---

## 📋 Dependencies

### Required:

- **Bash shell** - For running the translation manager script
- **jq** - JSON processor for parsing translation files
  - macOS: `brew install jq`
  - Ubuntu: `apt-get install jq`
- **Node.js & npm** - For the React/TypeScript project

### Project Structure:

- **Translation files**: `src/utils/localization/locales/{en,zh,zh-TW}/*.json`
- **Source files**: `src/components/` and `src/pages/` (scanned for translation usage)

---

## ⚠️ Important Notes

### Translation Management

- **Safe by design**: Missing keys are never added automatically
- **Manual review required**: Always review changes before applying `--remove` flag
- **Backup recommended**: Consider backing up translation files before bulk operations
- **Multi-language support**: Handles English, Simplified Chinese, and Traditional Chinese

### Best Practices

- Use descriptive translation key names (e.g., `page.home.title` instead of `title`)
- Group related keys by feature/page (e.g., `page.help.*`, `component.button.*`)
- Keep translations consistent across all supported languages
- Test translation changes in all language modes

---

## 🐛 Troubleshooting

### Common Issues

**Permission Denied**

```bash
chmod +x scripts/dev/translation-key-manager.sh
```

**jq not found**

```bash
# macOS
brew install jq

# Ubuntu/Debian
sudo apt-get install jq
```

**Translation keys not found**

```bash
# Verify project structure
ls -la src/utils/localization/locales/
ls -la src/components/ src/pages/

# Check if files contain intl.formatMessage patterns
grep -r "intl.formatMessage" src/components/ src/pages/
```

---

**Last Updated**: January 27, 2025
**Version**: 1.0.0
**Project**: React/TypeScript Portal Dashboard

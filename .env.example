# Development Environment Variables
# Copy this file to .env.local and update the values

# Backend API URL
VITE_API_URL=http://localhost:3001

# Sentry Configuration (Error Tracking)
VITE_SENTRY_DSN=your-sentry-dsn-here

# Firebase Configuration (Optional)
VITE_FIREBASE_API_KEY=your-firebase-api-key
VITE_FIREBASE_AUTH_DOMAIN=your-firebase-auth-domain
VITE_FIREBASE_PROJECT_ID=your-firebase-project-id
VITE_FIREBASE_STORAGE_BUCKET=your-firebase-storage-bucket
VITE_FIREBASE_MESSAGING_SENDER_ID=your-firebase-messaging-sender-id
VITE_FIREBASE_APP_ID=your-firebase-app-id
VITE_FIREBASE_MEASUREMENT_ID=your-firebase-measurement-id

# AWS Configuration (Optional)
VITE_AWS_POOL_ID=your-aws-pool-id
VITE_AWS_APP_CLIENT_ID=your-aws-app-client-id

# Auth0 Configuration (Optional)
VITE_AUTH0_CLIENT_ID=your-auth0-client-id
VITE_AUTH0_DOMAIN=your-auth0-domain

# Feature Flags
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_ERROR_TRACKING=true
VITE_ENABLE_PERFORMANCE_MONITORING=true

# Debug Settings
VITE_DEBUG_MODE=false
VITE_LOG_LEVEL=info

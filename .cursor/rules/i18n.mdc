---
description: 
globs: *.tsx
alwaysApply: false
---
# Internationalization (i18n) Rules

## Core Requirements
- **NEVER use hardcoded strings** for any user-facing text. All text must be internationalized.
- **Always update all language files** when adding new text. The project supports:
  - English (default)
  - Chinese Simplified (zh)
  - Chinese Traditional (zh-TW)

## Dual Translation Architecture

This project uses a **dual translation approach** that follows real-world best practices:

### 1. Static UI Translations (React-Intl)
**Use `intl.formatMessage()` for static UI elements**
- Buttons, labels, form fields, navigation, error messages
- Fixed interface text that doesn't change based on data
- Centralized in translation files under `src/utils/localization/locales/`

### 2. Dynamic Content Translations (Data-Driven)
**Use `getTranslatedText()` for dynamic content**
- Blog posts, career listings, support articles, FAQ items
- Content that would typically come from a CMS or database
- Embedded within data objects using the `Translation` interface

## Translation Methods

### Method 1: Static UI Translations
**For:** Interface elements, buttons, forms, navigation, error messages

```typescript
// Use useIntl hook and formatMessage
const intl = useIntl();
const title = intl.formatMessage({ id: 'page.home.title' });
```

**Translation files organized by category:**
```
src/utils/localization/locales/
├── en/
│   ├── common.json      # General/shared translations
│   ├── navigation.json  # Menu and navigation elements
│   ├── pages.json       # Page names and titles
│   ├── auth.json        # Authentication-related text
│   └── public.json      # Public-facing content
├── zh/                  # Chinese (Simplified)
└── zh-TW/              # Chinese (Traditional)
```

### Method 2: Dynamic Content Translations
**For:** Content-driven data (blog posts, careers, support articles)

```typescript
// Use getTranslatedText utility
import { getTranslatedText } from '@/utils/translation';

// In data files - define multilingual objects
export const blogPosts: BlogPost[] = [
  {
    id: 1,
    title: {
      en: "Understanding React",
      zh: "理解React",
      "zh-TW": "理解React"
    },
    // ... other fields
  }
];

// In components - extract translated text
const translatedTitle = getTranslatedText(post.title, currentLocale);
```

**Translation Interface:**
```typescript
interface Translation {
  en: string;           // Required English fallback
  zh?: string;          // Optional Chinese Simplified
  'zh-TW'?: string;     // Optional Chinese Traditional
}
```

## When to Use Each Method

### Use `intl.formatMessage()` when:
- Creating UI components with static text
- Building forms with labels and validation
- Adding navigation elements
- Implementing error handling
- Working with fixed interface elements

### Use `getTranslatedText()` when:
- Working with data from `src/data/` files
- Building content-driven pages (blog, careers, support)
- Handling dynamic content that would come from CMS
- Creating FAQ or documentation content

## Implementation Guidelines

### Static UI Implementation
1. Define translation keys in appropriate category files
2. Use hierarchical naming: `section.component.element`
3. Access via `useIntl()` hook and `formatMessage()`
4. Example: `page.register.title`, `form.login.emailLabel`

### Dynamic Content Implementation
1. Define content in data files with `Translation` objects
2. Always provide English (`en`) as fallback
3. Use `getTranslatedText()` utility to extract current language
4. Organize data files by content type: `blog.ts`, `careers.ts`, `support.ts`

## File Organization

### Static Translations Structure
```
src/utils/localization/locales/
├── en/common.json          # Buttons, general UI text
├── en/navigation.json      # Menu items, breadcrumbs
├── en/pages.json          # Page titles, descriptions
├── en/auth.json           # Login, registration forms
├── en/public.json         # Public-facing content
└── [zh/zh-TW]/...         # Corresponding files
```

### Dynamic Content Structure
```
src/data/
├── public/
│   ├── blog.ts            # Blog posts with Translation objects
│   ├── careers.ts         # Job listings with Translation objects
│   └── support.ts         # Support articles with Translation objects
└── private/               # Admin/internal content
```

## Change Management Process

### Adding Static UI Text
1. Identify appropriate category file (`common.json`, `pages.json`, etc.)
2. Add translation key with hierarchical naming
3. Update ALL language files (`en/`, `zh/`, `zh-TW/`)
4. Use `intl.formatMessage({ id: 'your.key' })` in component

### Adding Dynamic Content
1. Add to appropriate data file (`blog.ts`, `careers.ts`, etc.)
2. Create `Translation` object with all language variants
3. Ensure English (`en`) is always provided
4. Use `getTranslatedText(translationObject, locale)` in component

## Best Practices

### General
- Always provide English fallback
- Test in all supported languages
- Consider text expansion across languages
- Use descriptive, hierarchical naming conventions

### Static Translations
- Group related keys in same category file
- Use ICU message syntax for variables and pluralization
- Keep keys descriptive and maintainable

### Dynamic Content
- Always include English (`en`) in Translation objects
- Organize content by logical groupings in data files
- Use consistent Translation interface structure

## Testing Requirements
- Verify all static UI elements display correctly in all languages
- Test dynamic content rendering in all supported locales
- Check for missing translations or fallback behavior
- Validate text fits properly in UI components
- Ensure proper locale switching functionality

This dual approach ensures proper separation of concerns: static UI elements are managed centrally while dynamic content maintains multilingual capabilities at the data level, preparing the system for future CMS integration.

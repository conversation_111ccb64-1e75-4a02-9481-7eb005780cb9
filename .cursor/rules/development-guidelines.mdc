---
description: 
globs: 
alwaysApply: true
---
# Development Guidelines & Templates

## Overview
This guide provides templates and standards for creating components, integrating APIs, and modifying logic. **Always prioritize simplicity over complexity** - avoid over-engineering solutions.

## Component Creation Template

### 1. File Structure & Naming
- Use **kebab-case** for all file and directory names
- Follow the [component-structure.mdc](mdc:component-structure.mdc) categorization rules
- File naming pattern: `component-name.tsx` (not ComponentName.tsx)

```typescript
// File: src/components/features/user/user-settings-form.tsx
import { useState, useEffect } from "react";
import { useIntl } from "react-intl";
// ... other imports

/**
 * Component Documentation
 * 
 * Brief description of what this component does.
 * Include key features and data sources.
 */
export default function UserSettingsForm() {
  // Component implementation
}
```

### 2. Component Structure Template
```typescript
// 1. Imports (organized by category - ALWAYS follow this order)
import { useState, useEffect } from "react";           // React hooks
import { SomeIcon } from "lucide-react";               // Icons
import { useIntl } from "react-intl";                 // i18n
import { apiFunction } from "@/services/api";          // API services
import type { DataType } from "@/types/module";       // Types
import { Button } from "@/components/ui/shadcn/button"; // UI components

// 2. Types & Interfaces (prefer interfaces over types)
interface ComponentProps {
  // Use descriptive names with proper casing
  // PascalCase for interfaces/types, camelCase for properties
  isVisible: boolean;        // Prefix booleans with is/has/should
  userData: UserProfile;     // Use existing types from @/types
  onSave: () => void;        // Event handlers with 'on' prefix
}

interface LocalState {
  // Define local state structure with TypeScript
  data: DataType | null;
  isLoading: boolean;
  error: string | null;
}

/**
 * Component Name
 * 
 * Brief description of component purpose and functionality.
 * @param props - Component properties
 * @returns JSX component
 */
export default function ComponentName({ isVisible, userData, onSave }: ComponentProps) {
  // 4. Hooks & State (in order)
  const intl = useIntl();
  const [data, setData] = useState<DataType | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 5. API Integration (see API template below)
  
  // 6. Event Handlers (prefix with 'handle')
  const handleAction = async () => {
    try {
      // Implementation with proper error handling
      await someApiCall();
      onSave?.(); // Optional chaining for optional props
    } catch (err) {
      console.error('Action failed:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
    }
  };

  // 7. Effects (document dependencies)
  useEffect(() => {
    // Load data - always include dependencies
    loadInitialData();
  }, []); // Empty dependency array for mount-only effect

  // 8. Loading & Error States (always handle these)
  if (isLoading) return <LoadingComponent />;
  if (error) return <ErrorComponent message={error} />;

  // 9. Main Render
  return (
    <div className="space-y-6">
      {/* Always use intl.formatMessage for text */}
      <h1>{intl.formatMessage({ id: 'component.title' })}</h1>
      {/* Component content */}
    </div>
  );
}
```

## API Integration Template

### 1. Service Function Structure
```typescript
// File: src/services/user.ts
import type { ApiResponse, UserData } from "@/types/api";

export async function getUserSettings(): Promise<UserData> {
  try {
    // API call implementation
    const response = await fetch('/api/user/settings');
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('Failed to fetch user settings:', error);
    throw error;
  }
}
```

### 2. Component API Integration Pattern
```typescript
// In component
const [data, setData] = useState<DataType | null>(null);
const [isLoading, setIsLoading] = useState(true);
const [error, setError] = useState<string | null>(null);

useEffect(() => {
  const loadData = async () => {
    try {
      setIsLoading(true);
      setError(null);
      const result = await apiFunction();
      setData(result);
    } catch (err) {
      console.error('Failed to load data:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setIsLoading(false);
    }
  };

  loadData();
}, []);

// Save/Update pattern
const handleSave = async () => {
  if (!data) return;

  try {
    setIsSaving(true);
    await saveApiFunction(data);
    
    // Show success message using i18n
    alert(intl.formatMessage({ id: 'actions.saved.success' }));
  } catch (error) {
    console.error('Failed to save:', error);
    alert(intl.formatMessage({ id: 'actions.saved.error' }));
  } finally {
    setIsSaving(false);
  }
};
```

## Responsive Design & Theme Standards

### 1. Mobile-First Responsive Design
```typescript
// ✅ DO: Follow mobile-first approach with Tailwind breakpoints
<div className="
  flex flex-col          // Mobile: stack vertically
  sm:flex-row           // Small screens: horizontal
  gap-4                 // Consistent spacing
  p-4 sm:p-6 lg:p-8     // Progressive padding
">
  <aside className="
    w-full              // Mobile: full width
    sm:w-64             // Small+: fixed sidebar width
    lg:w-80             // Large: wider sidebar
  ">
    Sidebar content
  </aside>
  <main className="flex-1 min-w-0"> {/* min-w-0 prevents overflow */}
    Main content
  </main>
</div>

// ✅ DO: Use responsive text and spacing
<h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold mb-4 lg:mb-6">
<p className="text-sm sm:text-base leading-relaxed">
```

### 2. Dark/Light Mode Implementation
```typescript
// ✅ DO: Use theme-aware styling with ConfigContext
import { useContext } from 'react';
import { ConfigContext } from '@/contexts/config/ConfigContext';

function Component() {
  const { theme, onChangeTheme } = useContext(ConfigContext);
  
  return (
    <div className="
      bg-white dark:bg-gray-900           // Background colors
      text-gray-900 dark:text-white       // Text colors
      border border-gray-200 dark:border-gray-700  // Borders
      shadow-sm dark:shadow-gray-800      // Shadows
    ">
      <Button 
        variant={theme === 'dark' ? 'secondary' : 'default'}
        onClick={() => onChangeTheme(theme === 'dark' ? 'light' : 'dark')}
      >
        Toggle Theme
      </Button>
    </div>
  );
}

// ✅ DO: Use CSS variables for complex theming
<div className="bg-background text-foreground border-border">
```

### 3. Layout Consistency Patterns
```typescript
// ✅ DO: Follow existing page layout patterns
function PageComponent() {
  return (
    <div className="flex min-h-[calc(100vh-4rem)] flex-1 flex-col gap-6 p-6">
      {/* Page Header */}
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold">
            {intl.formatMessage({ id: 'pages.title' })}
          </h1>
          <Badge variant={isStale ? 'secondary' : 'default'}>
            Status
          </Badge>
        </div>
        <p className="text-muted-foreground">
          {intl.formatMessage({ id: 'pages.description' })}
        </p>
      </div>

      {/* Page Content */}
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Section Title</CardTitle>
            <div className="text-sm text-muted-foreground space-y-1">
              <p><strong>Purpose:</strong> What this section does</p>
              <p><strong>Features:</strong> Key functionality</p>
              <p><strong>Try:</strong> User instructions</p>
            </div>
          </CardHeader>
          <CardContent>
            {/* Section content */}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
```

### 4. Error Boundary Integration
```typescript
// ✅ DO: Apply appropriate error boundary levels
import { ErrorBoundary } from '@/components/base/boundary/error-boundary';
import { ApiErrorBoundary } from '@/components/base/boundary/api-error-boundary';

// Page level - wrap entire page content
<ErrorBoundary level="page" showDetails={import.meta.env.MODE === 'development'}>
  <PageContent />
</ErrorBoundary>

// API level - wrap API-dependent components
<ApiErrorBoundary onRetry={refetchData}>
  <DataDisplayComponent />
</ApiErrorBoundary>

// Component level - wrap individual features
<ErrorBoundary level="component">
  <ComplexFeatureComponent />
</ErrorBoundary>

// Critical level - app-wide (already in App.tsx)
<ErrorBoundary level="critical" showDetails={import.meta.env.MODE === 'development'}>
  <App />
</ErrorBoundary>
```

## Code Style Standards

### 1. TypeScript Guidelines
```typescript
// ✅ DO: Prefer interfaces over types for object definitions
interface UserData {
  id: string;
  name: string;
  isActive: boolean;
}

// ✅ DO: Use union types for multiple possible types
type Status = 'loading' | 'success' | 'error';

// ✅ DO: Properly type all function parameters and returns
function processUser(user: UserData): Promise<Status> {
  // Implementation
}

// ❌ DON'T: Use 'any' type
const data: any = fetchData(); // NEVER
```

### 2. Naming Conventions
```typescript
// ✅ DO: Follow consistent case conventions
const API_BASE_URL = 'https://api.example.com';     // UPPER_SNAKE_CASE for constants
const userName = 'john_doe';                         // camelCase for variables
const isUserActive = true;                           // Boolean prefixes: is/has/should
const hasPermission = false;
const shouldShowModal = true;

interface UserProfile {}                             // PascalCase for interfaces/types
class UserService {}                                 // PascalCase for classes
function handleUserLogin() {}                        // camelCase for functions

// File names: user-profile.tsx                      // kebab-case for files
// Translation keys: 'user.profile.title'           // dot.separated.lowercase
```

### 3. File Organization Rules
- **File Length**: Aim for under 300 lines per file
- **Import Order**: External → Internal → Components → Utils → Types → Styles
- **Component Organization**: Group related functions together
- **Function Order**: Hooks → Event handlers → Effects → Render helpers → Main render

### 4. Documentation Standards
```typescript
/**
 * Processes user login and returns authentication status
 * @param credentials - User login credentials
 * @param rememberMe - Whether to persist the session
 * @returns Promise resolving to authentication result
 */
async function loginUser(
  credentials: LoginCredentials, 
  rememberMe: boolean = false
): Promise<AuthResult> {
  // TODO: Add rate limiting
  // Implementation here
}
```

## Logic Modification Guidelines

### 1. State Updates
```typescript
// ✅ DO: Use functional updates for complex state
const updateSetting = (field: string, value: any) => {
  setData(prev => {
    if (!prev) return prev;
    
    // Handle nested field updates
    if (field.includes('.')) {
      const [parent, child] = field.split('.');
      return {
        ...prev,
        [parent]: {
          ...prev[parent],
          [child]: value
        }
      };
    }
    
    return { ...prev, [field]: value };
  });
};

// ❌ DON'T: Mutate state directly
data.field = value; // Never do this
```

### 2. Error Handling Pattern
```typescript
// ✅ DO: Consistent error handling
try {
  await riskyOperation();
} catch (error) {
  console.error('Operation failed:', error);
  setError(error instanceof Error ? error.message : 'Unknown error');
  
  // User-facing error message with i18n
  alert(intl.formatMessage({ id: 'error.operation.failed' }));
}

// ❌ DON'T: Silent failures or hardcoded messages
try {
  await riskyOperation();
} catch (error) {
  // Silent failure - BAD
}
```

## Translation Requirements (Critical!)

### 1. NO Hardcoded Text
```typescript
// ✅ DO: Always use i18n with flat key structure
const title = intl.formatMessage({ id: 'user.settings.title' });
const saveButton = intl.formatMessage({ id: 'actions.save' });
const errorMessage = intl.formatMessage({ id: 'errors.network.failed' });

<Button>{intl.formatMessage({ id: 'actions.save' })}</Button>
<h1>{intl.formatMessage({ id: 'pages.dashboard.title' })}</h1>

// ❌ DON'T: Hardcoded strings or nested objects
const title = "User Settings"; // NEVER
<Button>Save</Button>         // NEVER
const nested = intl.formatMessage({ id: 'user', values: { section: 'title' } }); // AVOID
```

### 2. Translation File Structure & Naming
```
/src/utils/localization/locales/
├── en/                    # English (default)
├── zh/                    # Chinese Simplified
└── zh-TW/                 # Chinese Traditional
    ├── auth.json          # Authentication: login, register, forgot password
    ├── common.json        # General: buttons, actions, status, validation
    ├── navigation.json    # Navigation: menus, breadcrumbs, footer links
    ├── pages.json         # Pages: titles, headers, meta descriptions
    ├── user.json          # User: profile, settings, notifications
    ├── components/        # Component-specific translations
    │   └── filters.json
    ├── public/            # Public page translations (organized by page)
    │   ├── home.json      # Home page content
    │   ├── about.json     # About page content
    │   ├── team.json      # Team page content
    │   ├── contact.json   # Contact page content
    │   ├── careers.json   # Careers page content
    │   ├── blog.json      # Blog page content
    │   ├── help.json      # Help center content
    │   ├── support.json   # Support page content
    │   ├── tickets.json   # Support tickets content
    │   ├── faq.json       # FAQ page content
    │   ├── resources.json # Resources page content
    │   ├── cookies.json   # Cookie policy content
    │   ├── gdpr.json      # GDPR policy content
    │   ├── security.json  # Security page content
    │   ├── changelog.json # Changelog page content
    │   └── integrations.json # Integrations page content
    └── [feature]/         # Feature-specific translations
        └── feature.json
```

### 3. Key Naming Conventions
```typescript
// ✅ DO: Use flat, dot-separated keys
'user.profile.title'           // User profile page title
'actions.save.success'         // Save action success message
'errors.validation.required'   // Required field validation
'navigation.menu.dashboard'    // Dashboard menu item
'pages.settings.description'   // Settings page description

// ❌ DON'T: Use nested objects or unclear keys
'user': { 'profile': { 'title': '...' } }  // Nested structure
'btn1', 'text2', 'msg'                     // Unclear naming
'userProfilePageTitle'                     // CamelCase keys
```

### 4. Update All Language Files
When adding new text, update ALL language files:
- `src/utils/localization/locales/en/[category].json`
- `src/utils/localization/locales/zh/[category].json`  
- `src/utils/localization/locales/zh-TW/[category].json`

Follow the [i18n.mdc](mdc:i18n.mdc) rules for proper translation implementation.

## API Service & Hook Patterns

### Service Creation Pattern
```typescript
// ✅ DO: Follow this service structure
export class FeatureService {
  static async getFeatureData(): Promise<FeatureData> {
    try {
      const isApiAvailable = await SystemService.getOrCheckApiAvailability();
      
      if (isApiAvailable) {
        const response = await axiosServices.get('/api/feature');
        return this.transformResponse(response.data);
      }
    } catch (error) {
      console.warn('API call failed, using mock data:', error);
    }
    
    return mockFeatureData; // Always provide fallback
  }
  
  private static transformResponse(apiData: any): FeatureData {
    return {
      id: apiData.id,
      name: apiData.name || apiData.display_name,
      // Transform fields as needed
    };
  }
}
```

### Custom Hook Pattern
```typescript
// ✅ DO: Follow this hook structure
export function useFeatureData(autoLoad: boolean = true) {
  const [data, setData] = useState<FeatureData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const loadData = async () => {
    try {
      setIsLoading(true);
      setError(null);
      const result = await FeatureService.getFeatureData();
      setData(result);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setIsLoading(false);
    }
  };
  
  useEffect(() => {
    if (autoLoad) loadData();
  }, [autoLoad]);
  
  return { data, isLoading, error, loadData, refreshData: loadData };
}
```

## Anti-Patterns (Avoid Over-Engineering)

### ❌ DON'T Over-Engineer
- Don't create complex abstractions for simple operations
- Don't add unnecessary optimization before measuring performance
- Don't create generic components until you have 3+ use cases
- Don't add features "just in case" - implement only what's needed

### ❌ DON'T Duplicate Code
```typescript
// BAD: Duplicate API call patterns
const loadUsers = async () => {
  setLoading(true);
  try {
    const data = await fetch('/api/users');
    setUsers(data);
  } catch (error) {
    setError(error);
  } finally {
    setLoading(false);
  }
};

// GOOD: Use shared hook or utility
const { data: users, isLoading, error } = useApiData('/api/users');
```

### ❌ DON'T Ignore Naming Conventions
- Always use kebab-case for files/directories
- Use descriptive variable names (isLoading, hasError, not loading, err)
- Follow the component categorization rules

## Follow Current Project Structure (Critical!)

### ❌ DON'T Create Duplicates - Use Existing Resources

**Animations**: Use existing [src/styles/animations.css](mdc:src/styles/animations.css)
```typescript
// ✅ DO: Use existing animation classes
<div className="animate-fadeIn hero-delay-1">
<Card className="stagger-card">
<Button className="shimmer-effect">

// ❌ DON'T: Create new animation CSS files or duplicate animations
// Never create new @keyframes or animation classes
```

**Types**: Use existing type definitions from [src/types/](mdc:src/types)
```typescript
// ✅ DO: Import from existing types
import { Translation } from '@/types/shared';
import { BlogPost } from '@/types/public/blog';
import { UserProfile } from '@/types/user-profile';

// ❌ DON'T: Create duplicate interfaces
interface MyTranslation { en: string; zh: string; } // NEVER - use Translation
```

**Mock Data**: Use existing data from [src/data/public/](mdc:src/data/public)
```typescript
// ✅ DO: Import from existing data files
import { blogPosts } from '@/data/public/blog';
import { jobOpenings } from '@/data/public/careers';
import { HELP_CATEGORIES } from '@/data/public/help-center';

// ❌ DON'T: Create new mock data files when existing ones exist
```

### 📁 Project Structure Reference

**Available Animations**: [src/styles/animations.css](mdc:src/styles/animations.css)
- Entrance: `animate-fadeIn`, `animate-slideUp`, `animate-scaleIn`
- Hero: `hero-element`, `hero-delay-1`, `hero-delay-2`
- Interactive: `stagger-card`, `shimmer-effect`, `scale-effect`, `pulse-effect`
- Text: `text-fade-in`, `text-slide-up`

**Available Types**: [src/types/](mdc:src/types)
- Shared: `Translation` from [src/types/shared.ts](mdc:src/types/shared.ts)
- Public: Blog, Careers, Support, Team, Resources types
- User: UserProfile, NotificationData, SecurityData types
- System: API responses, Config, Toast types

**Available Data**: [src/data/public/](mdc:src/data/public)
- [blog.ts](mdc:src/data/public/blog.ts): Blog posts with translations
- [careers.ts](mdc:src/data/public/careers.ts): Job openings and departments
- [help-center.ts](mdc:src/data/public/help-center.ts): Help categories and articles
- [policies.ts](mdc:src/data/public/policies.ts): Privacy, Terms, GDPR content
- [resources.ts](mdc:src/data/public/resources.ts): Download resources

## Code Quality Checklist

Before submitting code, verify:

- [ ] **Project Structure**: Used existing animations, types, and data (no duplicates)
- [ ] **i18n**: All user-facing text uses `intl.formatMessage()`
- [ ] **Naming**: Files use kebab-case, variables use camelCase, constants use UPPER_SNAKE_CASE
- [ ] **TypeScript**: Prefer interfaces over types, no 'any' types, proper function typing
- [ ] **Types**: Used existing TypeScript interfaces from `@/types`
- [ ] **Animations**: Used existing classes from `animations.css`
- [ ] **Data**: Used existing mock data from `@/data/public`
- [ ] **Documentation**: Added JSDoc comments for functions and components
- [ ] **Import Order**: External → Internal → Components → Utils → Types → Styles
- [ ] **File Length**: Under 300 lines per file (split if larger)
- [ ] **Loading States**: Loading, error, and success states are handled
- [ ] **Error Handling**: Try-catch blocks with proper error messages
- [ ] **No Duplication**: Reused logic is extracted to utilities/hooks
- [ ] **Performance**: No unnecessary re-renders or API calls
- [ ] **Responsive**: UI works on mobile and desktop
- [ ] **Accessibility**: Proper labels, ARIA attributes where needed

## Example Implementation Reference

See [src/pages/user/settings/page.tsx](mdc:src/pages/user/settings/page.tsx) for a complete example that follows these patterns:
- Proper component structure and organization
- API integration with loading/error states
- Internationalization throughout
- State management for complex forms
- Responsive design with shadcn components
- TypeScript interfaces and type safety

## Quick Start Commands

```bash
# Create new component with proper structure
mkdir -p src/components/features/[feature-name]
touch src/components/features/[feature-name]/[component-name].tsx

# Add translation keys to all language files
# Edit: src/utils/localization/locales/en/[category].json
# Edit: src/utils/localization/locales/zh/[category].json  
# Edit: src/utils/localization/locales/zh-TW/[category].json
```

Remember: **Simplicity is key**. Start with the minimal viable implementation and iterate based on actual needs, not hypothetical requirements.

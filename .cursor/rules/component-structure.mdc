---
description: 
globs: *.tsx
alwaysApply: false
---
# Component Structure Rules

## Recommended Directory Structure
```
/src/components/
├── /ui/                          # Third-party UI components only
│   ├── /shadcn/                 # All shadcn components
│   └── /magicui/                # Magic UI components
├── /base/                       # Foundation components (loader, loadable, etc.)
├── /layout/                     # Navigation & layout components (sidebars, navbar, footer)
├── /forms/                      # Form-related components
├── /features/                   # Feature-specific components
│   ├── /playground/             # Playground-specific components
│   ├── /ai-services/            # AI services components
│   └── /[feature]/              # Other features
├── /common/                     # Reusable business components
└── index.ts                     # Barrel exports
```

## Naming Conventions
- **Files**: Use kebab-case (e.g., `user-profile-card.tsx`)
- **Components**: Use PascalCase (e.g., `UserProfileCard`)
- **Props**: Use camelCase
- **Suffixes**: Add descriptive suffixes (`-form`, `-card`, `-dialog`, `-drawer`, `-area`, `-display`)

## Component Categorization Rules

### `/ui/` - Third-party UI libraries only
- shadcn components (`/ui/shadcn/`)
- magicui components (`/ui/magicui/`)
- External UI library components

### `/base/` - Foundation components
- Loading states, error boundaries
- Core utility components used app-wide
- Locale providers, theme providers

### `/layout/` - Structure & navigation
- Sidebars, headers, footers
- Navigation components
- Layout wrappers and containers

### `/forms/` - Form components
- Login forms, contact forms
- Form validation components
- Form-specific UI elements

### `/features/` - Domain-specific components
- Group by business feature (playground, ai-services, dashboard)
- Keep feature components isolated in their subdirectories
- Follow colocation principle - keep related files together

### `/common/` - Reusable business components
- Components used across multiple features
- Business logic components (not pure UI)
- Shared interactive elements
- **Examples**: Custom buttons (MacButton), cards, modals, data displays
- **Rule of thumb**: If it's custom-built AND used in 2+ features → `/common/`

## Component Placement Decision Tree
```
Is it from a third-party UI library? → `/ui/`
Is it a foundation/infrastructure component? → `/base/`
Is it layout/navigation related? → `/layout/`
Is it form-specific? → `/forms/`
Is it used only in one feature? → `/features/[feature-name]/`
Is it custom-built and reusable across features? → `/common/`
```

## Implementation Guidelines

### Component Structure Patterns
```typescript
// Simple component
component-name.tsx

// Complex component with co-location
/component-name/
├── index.ts              # Barrel export
├── component-name.tsx    # Main component
├── component-name.types.ts
└── component-name.test.tsx
```

### Import Organization
1. External libraries
2. Internal modules  
3. Component imports
4. Utility imports
5. Type imports
6. Style imports

### Best Practices

#### Core Development Principles
- **Single Responsibility**: Each component should do one thing well
- **TypeScript**: Use TypeScript for all component definitions
- **JSDoc**: Document component purpose, props, and usage
- **Lazy Loading**: Use React.lazy() for page components
- **Prop Validation**: Use TypeScript interfaces for props
- **Barrel Exports**: Use index.ts files for clean imports

#### Internationalization (i18n) Standards
- **Flat Key Structure**: Use dot-separated keys (e.g., `'user.profile.title'`)
- **Translation Files**: Organize by category in `/src/utils/localization/locales/`
  ```
  /locales/
  ├── en/auth.json          # Authentication translations
  ├── en/common.json        # General UI terms
  ├── en/navigation.json    # Menu and navigation
  ├── en/pages.json         # Page titles and headers
  ├── en/public.json        # Public-facing content
  ├── en/user.json          # User-specific content
  └── [zh, zh-TW]/          # Same structure for other languages
  ```
- **No Hardcoded Text**: Always use `intl.formatMessage({ id: 'key' })`
- **Key Naming**: Follow pattern `category.section.element` (e.g., `'user.settings.title'`)

#### Dark/Light Mode Handling
- **Config Context**: Use existing config context from `@/contexts/config/ConfigContext`
- **CSS Variables**: Leverage Tailwind's dark mode classes (`dark:bg-gray-900`)
- **Component Theming**: Apply theme-aware styles consistently
```typescript
// ✅ DO: Use theme-aware classes
<div className="bg-white dark:bg-gray-900 text-gray-900 dark:text-white">

// ✅ DO: Use config context for theme management
import { useContext } from 'react';
import { ConfigContext } from '@/contexts/config/ConfigContext';

const { theme, onChangeTheme } = useContext(ConfigContext);
```

#### Responsive Design Standards
- **Mobile-First**: Start with mobile design, enhance for larger screens
- **Tailwind Breakpoints**: Use `sm:`, `md:`, `lg:`, `xl:`, `2xl:` prefixes
- **Layout Patterns**: Follow existing page patterns for consistency
```typescript
// ✅ DO: Mobile-first responsive design
<div className="flex flex-col lg:flex-row gap-4 p-4 lg:p-6">
  <aside className="w-full lg:w-64">Sidebar</aside>
  <main className="flex-1">Content</main>
</div>
```

#### Error Boundary Application
- **Critical Level**: App-wide errors (App.tsx, main.tsx)
- **Page Level**: Route-specific errors (all layouts and pages)
- **Component Level**: Feature-specific errors
- **API Level**: API-related errors with retry functionality
```typescript
// ✅ DO: Apply appropriate error boundary level
<ErrorBoundary level="page" showDetails={import.meta.env.MODE === 'development'}>
  <PageContent />
</ErrorBoundary>

<ApiErrorBoundary onRetry={refetchData}>
  <DataComponent />
</ApiErrorBoundary>
```

### Migration Steps
1. **Phase 1**: Move shadcn components to `/ui/shadcn/`
2. **Phase 2**: Reorganize navigation components to `/layout/`
3. **Phase 3**: Group feature components under `/features/`
4. **Phase 4**: Update import paths and barrel exports

# API Integration Guide

This guide provides comprehensive patterns and best practices for integrating APIs into the Base Portal Dashboard template.

## Table of Contents

1. [Quick Start](#quick-start)
2. [Service Layer Architecture](#service-layer-architecture)
3. [Error Handling Patterns](#error-handling-patterns)
4. [Authentication Integration](#authentication-integration)
5. [Caching Strategies](#caching-strategies)
6. [React Integration](#react-integration)
7. [Testing API Services](#testing-api-services)
8. [Production Considerations](#production-considerations)

## Quick Start

### 1. Environment Configuration

Configure your API endpoints in `.env`:

```bash
# Backend API URL
VITE_API_URL=http://localhost:3001

# API Configuration
VITE_API_TIMEOUT=10000
VITE_API_RETRY_ATTEMPTS=3
VITE_API_RETRY_DELAY=1000

# Feature Flags
VITE_ENABLE_API_MOCKING=false
VITE_ENABLE_API_LOGGING=true
```

### 2. Basic Service Implementation

Create a new service file following the established pattern:

```typescript
// src/services/my-feature.ts
import axiosServices from '@/utils/api/axios';
import { PaginationParams, PaginatedResponse } from '@/types/api';

// Define your types
export interface MyFeatureItem {
  id: string;
  name: string;
  createdAt: string;
  status: 'active' | 'inactive';
}

// API endpoints
const ENDPOINTS = {
  ITEMS: '/api/my-feature',
  ITEM_BY_ID: '/api/my-feature/{id}',
} as const;

// Service functions
export const fetchMyFeatureItems = async (
  params: PaginationParams = {}
): Promise<PaginatedResponse<MyFeatureItem>> => {
  try {
    const response = await axiosServices.get(ENDPOINTS.ITEMS, { params });
    return response.data;
  } catch (error) {
    console.error('Error fetching items:', error);
    throw error;
  }
};

export const fetchMyFeatureItemById = async (
  id: string
): Promise<MyFeatureItem> => {
  try {
    const url = ENDPOINTS.ITEM_BY_ID.replace('{id}', id);
    const response = await axiosServices.get(url);
    return response.data;
  } catch (error) {
    console.error(`Error fetching item ${id}:`, error);
    throw error;
  }
};
```

### 3. React Hook Integration

Create custom hooks for React integration:

```typescript
// src/hooks/use-my-feature.ts
import { useState, useEffect } from 'react';
import { fetchMyFeatureItems, MyFeatureItem } from '@/services/my-feature';

export const useMyFeatureItems = () => {
  const [items, setItems] = useState<MyFeatureItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    const loadItems = async () => {
      try {
        setLoading(true);
        const response = await fetchMyFeatureItems();
        setItems(response.content);
        setError(null);
      } catch (err) {
        setError(err as Error);
      } finally {
        setLoading(false);
      }
    };

    loadItems();
  }, []);

  return { items, loading, error, refetch: loadItems };
};
```

## Service Layer Architecture

### File Structure

```
src/services/
├── example-api.ts          # Comprehensive example service
├── system.ts              # System health and monitoring
├── user.ts                # User-related operations
├── auth.ts                # Authentication services
├── my-feature.ts          # Your feature services
└── index.ts               # Service barrel exports
```

### Service Template

Use this template for new services:

```typescript
/**
 * [Feature Name] Service
 *
 * Description of what this service handles
 */

import axiosServices from '@/utils/api/axios';
import type {} from /* Import your types */ '@/types/[feature]';

// API endpoints - centralized management
const ENDPOINTS = {
  BASE: '/api/[feature]',
  BY_ID: '/api/[feature]/{id}',
  // Add more endpoints as needed
} as const;

// Cache management (if needed)
let cache: YourType[] = [];
let isLoading = false;
let loadingPromise: Promise<YourType[]> | null = null;

/**
 * Main service class or functions
 */
export class YourFeatureService {
  // Implement your methods here
}

// Export convenience functions
export const yourFunction = () => YourFeatureService.yourMethod();

// Default export with all functions
export default {
  // List all your functions here
};
```

### Error Handling Patterns

#### 1. Service-Level Error Handling

```typescript
export const fetchData = async (id: string): Promise<DataType> => {
  try {
    const response = await axiosServices.get(`/api/data/${id}`);
    return response.data;
  } catch (error) {
    // Log the error
    console.error(`Error fetching data for ID ${id}:`, error);

    // Transform API errors to application errors
    if (error.response?.status === 404) {
      throw new Error(`Data with ID ${id} not found`);
    }

    if (error.response?.status === 403) {
      throw new Error('Access denied to this resource');
    }

    // Re-throw for higher-level handling
    throw error;
  }
};
```

#### 2. Fallback to Mock Data

```typescript
import { mockData } from '@/data/mock-data';
import { SystemService } from './system';

export const getDataWithFallback = async (): Promise<DataType[]> => {
  try {
    // Check API availability
    const isApiAvailable = await SystemService.getOrCheckApiAvailability();

    if (isApiAvailable) {
      const response = await axiosServices.get('/api/data');
      return response.data;
    }
  } catch (error) {
    console.warn('API call failed, using mock data:', error);
  }

  // Return mock data as fallback
  return mockData;
};
```

#### 3. Retry Logic

```typescript
const MAX_RETRIES = 3;
const RETRY_DELAY = 1000;

export const fetchWithRetry = async <T>(
  url: string,
  retries = MAX_RETRIES
): Promise<T> => {
  try {
    const response = await axiosServices.get(url);
    return response.data;
  } catch (error) {
    if (retries > 0 && shouldRetry(error)) {
      console.warn(`Request failed, retrying... (${retries} attempts left)`);
      await delay(RETRY_DELAY);
      return fetchWithRetry(url, retries - 1);
    }
    throw error;
  }
};

const shouldRetry = (error: any): boolean => {
  // Retry on network errors or 5xx server errors
  return !error.response || error.response.status >= 500;
};

const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));
```

## Authentication Integration

### JWT Token Management

```typescript
// src/services/auth.ts
import axiosServices from '@/utils/api/axios';

interface AuthTokens {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
}

interface LoginCredentials {
  email: string;
  password: string;
}

export class AuthService {
  private static readonly TOKEN_KEY = 'auth_tokens';

  static async login(credentials: LoginCredentials): Promise<AuthTokens> {
    try {
      const response = await axiosServices.post('/api/auth/login', credentials);
      const tokens = response.data;

      // Store tokens securely
      this.storeTokens(tokens);

      // Set up automatic token refresh
      this.scheduleTokenRefresh(tokens.expiresIn);

      return tokens;
    } catch (error) {
      console.error('Login failed:', error);
      throw error;
    }
  }

  static async refreshToken(): Promise<AuthTokens> {
    const currentTokens = this.getStoredTokens();
    if (!currentTokens?.refreshToken) {
      throw new Error('No refresh token available');
    }

    try {
      const response = await axiosServices.post('/api/auth/refresh', {
        refreshToken: currentTokens.refreshToken,
      });

      const newTokens = response.data;
      this.storeTokens(newTokens);
      this.scheduleTokenRefresh(newTokens.expiresIn);

      return newTokens;
    } catch (error) {
      // If refresh fails, clear tokens and redirect to login
      this.clearTokens();
      window.location.href = '/login';
      throw error;
    }
  }

  private static storeTokens(tokens: AuthTokens): void {
    localStorage.setItem(this.TOKEN_KEY, JSON.stringify(tokens));
  }

  private static getStoredTokens(): AuthTokens | null {
    const stored = localStorage.getItem(this.TOKEN_KEY);
    return stored ? JSON.parse(stored) : null;
  }

  private static clearTokens(): void {
    localStorage.removeItem(this.TOKEN_KEY);
  }

  private static scheduleTokenRefresh(expiresIn: number): void {
    // Refresh token 5 minutes before expiry
    const refreshTime = (expiresIn - 300) * 1000;
    setTimeout(() => {
      this.refreshToken().catch(console.error);
    }, refreshTime);
  }
}
```

### Axios Interceptors Setup

```typescript
// src/utils/api/axios.ts
import axios from 'axios';
import { AuthService } from '@/services/auth';

const axiosServices = axios.create({
  baseURL: import.meta.env.VITE_API_URL,
  timeout: parseInt(import.meta.env.VITE_API_TIMEOUT) || 10000,
});

// Request interceptor - add auth token
axiosServices.interceptors.request.use(
  config => {
    const tokens = AuthService.getStoredTokens();
    if (tokens?.accessToken) {
      config.headers.Authorization = `Bearer ${tokens.accessToken}`;
    }
    return config;
  },
  error => Promise.reject(error)
);

// Response interceptor - handle token refresh
axiosServices.interceptors.response.use(
  response => response,
  async error => {
    const originalRequest = error.config;

    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        await AuthService.refreshToken();
        // Retry the original request with new token
        return axiosServices(originalRequest);
      } catch (refreshError) {
        // Refresh failed, redirect to login
        window.location.href = '/login';
        return Promise.reject(refreshError);
      }
    }

    return Promise.reject(error);
  }
);

export default axiosServices;
```

## Caching Strategies

### 1. Simple In-Memory Cache

```typescript
// Simple cache implementation
let cache: Map<string, { data: any; timestamp: number }> = new Map();
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

export const getCachedData = async <T>(
  key: string,
  fetcher: () => Promise<T>
): Promise<T> => {
  const cached = cache.get(key);
  const now = Date.now();

  // Return cached data if still valid
  if (cached && now - cached.timestamp < CACHE_DURATION) {
    return cached.data;
  }

  // Fetch fresh data
  const data = await fetcher();
  cache.set(key, { data, timestamp: now });

  return data;
};

export const clearCache = (key?: string): void => {
  if (key) {
    cache.delete(key);
  } else {
    cache.clear();
  }
};
```

### 2. Advanced Caching with Loading States

```typescript
// Advanced cache with loading state management
class CacheManager<T> {
  private cache = new Map<string, T>();
  private loadingPromises = new Map<string, Promise<T>>();
  private timestamps = new Map<string, number>();

  constructor(private cacheDuration = 5 * 60 * 1000) {}

  async get(key: string, fetcher: () => Promise<T>): Promise<T> {
    // Check if we have valid cached data
    if (this.isValid(key)) {
      return this.cache.get(key)!;
    }

    // Check if we're already loading this data
    if (this.loadingPromises.has(key)) {
      return this.loadingPromises.get(key)!;
    }

    // Start loading
    const promise = this.load(key, fetcher);
    this.loadingPromises.set(key, promise);

    try {
      const data = await promise;
      return data;
    } finally {
      this.loadingPromises.delete(key);
    }
  }

  private async load(key: string, fetcher: () => Promise<T>): Promise<T> {
    const data = await fetcher();
    this.cache.set(key, data);
    this.timestamps.set(key, Date.now());
    return data;
  }

  private isValid(key: string): boolean {
    if (!this.cache.has(key)) return false;

    const timestamp = this.timestamps.get(key);
    if (!timestamp) return false;

    return Date.now() - timestamp < this.cacheDuration;
  }

  clear(key?: string): void {
    if (key) {
      this.cache.delete(key);
      this.timestamps.delete(key);
      this.loadingPromises.delete(key);
    } else {
      this.cache.clear();
      this.timestamps.clear();
      this.loadingPromises.clear();
    }
  }
}

// Usage
const userCache = new CacheManager<UserData>(10 * 60 * 1000); // 10 minutes

export const getUserData = (id: string) =>
  userCache.get(`user_${id}`, () => fetchUserById(id));
```

## React Integration

### 1. Custom Hooks for API Data

```typescript
// src/hooks/use-api-data.ts
import { useState, useEffect, useCallback } from 'react';

interface UseApiDataOptions<T> {
  initialData?: T;
  dependencies?: any[];
  enabled?: boolean;
}

export function useApiData<T>(
  fetcher: () => Promise<T>,
  options: UseApiDataOptions<T> = {}
) {
  const { initialData, dependencies = [], enabled = true } = options;

  const [data, setData] = useState<T | undefined>(initialData);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchData = useCallback(async () => {
    if (!enabled) return;

    try {
      setLoading(true);
      setError(null);
      const result = await fetcher();
      setData(result);
    } catch (err) {
      setError(err as Error);
    } finally {
      setLoading(false);
    }
  }, [fetcher, enabled]);

  useEffect(() => {
    fetchData();
  }, [fetchData, ...dependencies]);

  const refetch = useCallback(() => {
    return fetchData();
  }, [fetchData]);

  return {
    data,
    loading,
    error,
    refetch,
  };
}
```

### 2. Mutation Hook for API Updates

```typescript
// src/hooks/use-api-mutation.ts
import { useState, useCallback } from 'react';

interface UseMutationOptions<TData, TVariables> {
  onSuccess?: (data: TData, variables: TVariables) => void;
  onError?: (error: Error, variables: TVariables) => void;
}

export function useMutation<TData, TVariables>(
  mutationFn: (variables: TVariables) => Promise<TData>,
  options: UseMutationOptions<TData, TVariables> = {}
) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [data, setData] = useState<TData | undefined>();

  const mutate = useCallback(
    async (variables: TVariables) => {
      try {
        setLoading(true);
        setError(null);
        const result = await mutationFn(variables);
        setData(result);
        options.onSuccess?.(result, variables);
        return result;
      } catch (err) {
        const error = err as Error;
        setError(error);
        options.onError?.(error, variables);
        throw error;
      } finally {
        setLoading(false);
      }
    },
    [mutationFn, options]
  );

  const reset = useCallback(() => {
    setData(undefined);
    setError(null);
    setLoading(false);
  }, []);

  return {
    mutate,
    data,
    loading,
    error,
    reset,
  };
}
```

### 3. Component Integration Example

```typescript
// Example component using the hooks
import React from 'react';
import { useApiData, useMutation } from '@/hooks';
import { fetchUsers, createUser, User } from '@/services/user';
import { Button } from '@/components/ui/button';
import { ApiErrorBoundary } from '@/components/base/api-error-boundary';

export function UserManagement() {
  const {
    data: users,
    loading,
    error,
    refetch
  } = useApiData(() => fetchUsers());

  const createUserMutation = useMutation(createUser, {
    onSuccess: () => {
      refetch(); // Refresh the user list
    },
  });

  const handleCreateUser = async () => {
    try {
      await createUserMutation.mutate({
        name: 'New User',
        email: '<EMAIL>',
      });
    } catch (error) {
      // Error is handled by the mutation hook
    }
  };

  if (loading) return <div>Loading...</div>;
  if (error) throw error; // Let error boundary handle it

  return (
    <ApiErrorBoundary onRetry={refetch}>
      <div>
        <Button
          onClick={handleCreateUser}
          disabled={createUserMutation.loading}
        >
          {createUserMutation.loading ? 'Creating...' : 'Create User'}
        </Button>

        {users?.map(user => (
          <div key={user.id}>{user.name}</div>
        ))}
      </div>
    </ApiErrorBoundary>
  );
}
```

## Testing API Services

### 1. Service Unit Tests

```typescript
// src/services/__tests__/user.test.ts
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { fetchUsers, createUser } from '../user';
import axiosServices from '@/utils/api/axios';

// Mock axios
vi.mock('@/utils/api/axios');
const mockedAxios = vi.mocked(axiosServices);

describe('User Service', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('fetchUsers', () => {
    it('should fetch users successfully', async () => {
      const mockUsers = [
        { id: '1', name: 'John Doe', email: '<EMAIL>' },
      ];

      mockedAxios.get.mockResolvedValue({
        data: { content: mockUsers, total: 1 },
      });

      const result = await fetchUsers();

      expect(mockedAxios.get).toHaveBeenCalledWith('/api/users', {
        params: {},
      });
      expect(result.content).toEqual(mockUsers);
    });

    it('should handle errors properly', async () => {
      const error = new Error('Network error');
      mockedAxios.get.mockRejectedValue(error);

      await expect(fetchUsers()).rejects.toThrow('Network error');
    });
  });

  describe('createUser', () => {
    it('should create user successfully', async () => {
      const newUser = { name: 'Jane Doe', email: '<EMAIL>' };
      const createdUser = { id: '2', ...newUser };

      mockedAxios.post.mockResolvedValue({ data: createdUser });

      const result = await createUser(newUser);

      expect(mockedAxios.post).toHaveBeenCalledWith('/api/users', newUser);
      expect(result).toEqual(createdUser);
    });
  });
});
```

### 2. Hook Testing

```typescript
// src/hooks/__tests__/use-api-data.test.ts
import { renderHook, waitFor } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import { useApiData } from '../use-api-data';

describe('useApiData', () => {
  it('should fetch data successfully', async () => {
    const mockData = { id: 1, name: 'Test' };
    const fetcher = vi.fn().mockResolvedValue(mockData);

    const { result } = renderHook(() => useApiData(fetcher));

    expect(result.current.loading).toBe(true);

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(result.current.data).toEqual(mockData);
    expect(result.current.error).toBeNull();
  });

  it('should handle errors', async () => {
    const error = new Error('Fetch failed');
    const fetcher = vi.fn().mockRejectedValue(error);

    const { result } = renderHook(() => useApiData(fetcher));

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(result.current.error).toEqual(error);
    expect(result.current.data).toBeUndefined();
  });
});
```

## Production Considerations

### 1. Environment-Specific Configuration

```typescript
// src/config/api.ts
interface ApiConfig {
  baseURL: string;
  timeout: number;
  retryAttempts: number;
  retryDelay: number;
  enableLogging: boolean;
  enableMocking: boolean;
}

const getApiConfig = (): ApiConfig => {
  const env = import.meta.env;

  return {
    baseURL: env.VITE_API_URL || 'http://localhost:3001',
    timeout: parseInt(env.VITE_API_TIMEOUT) || 10000,
    retryAttempts: parseInt(env.VITE_API_RETRY_ATTEMPTS) || 3,
    retryDelay: parseInt(env.VITE_API_RETRY_DELAY) || 1000,
    enableLogging: env.VITE_ENABLE_API_LOGGING === 'true',
    enableMocking: env.VITE_ENABLE_API_MOCKING === 'true',
  };
};

export const apiConfig = getApiConfig();
```

### 2. Error Reporting Integration

```typescript
// src/utils/error-reporting.ts
interface ErrorReport {
  message: string;
  stack?: string;
  url: string;
  userAgent: string;
  timestamp: string;
  userId?: string;
  apiEndpoint?: string;
  requestId?: string;
}

export class ErrorReporter {
  static report(error: Error, context: Partial<ErrorReport> = {}) {
    const report: ErrorReport = {
      message: error.message,
      stack: error.stack,
      url: window.location.href,
      userAgent: navigator.userAgent,
      timestamp: new Date().toISOString(),
      ...context,
    };

    // Send to your error reporting service
    if (process.env.NODE_ENV === 'production') {
      this.sendToService(report);
    } else {
      console.error('Error Report:', report);
    }
  }

  private static async sendToService(report: ErrorReport) {
    try {
      // Example: Send to Sentry, LogRocket, or your own service
      await fetch('/api/errors', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(report),
      });
    } catch (err) {
      console.error('Failed to report error:', err);
    }
  }
}
```

### 3. Performance Monitoring

```typescript
// src/utils/performance.ts
export class PerformanceMonitor {
  private static metrics = new Map<string, number>();

  static startTimer(key: string): void {
    this.metrics.set(key, performance.now());
  }

  static endTimer(key: string): number {
    const startTime = this.metrics.get(key);
    if (!startTime) return 0;

    const duration = performance.now() - startTime;
    this.metrics.delete(key);

    // Log slow operations
    if (duration > 1000) {
      console.warn(`Slow operation detected: ${key} took ${duration}ms`);
    }

    return duration;
  }

  static measureApiCall<T>(
    operation: string,
    apiCall: () => Promise<T>
  ): Promise<T> {
    return new Promise(async (resolve, reject) => {
      this.startTimer(operation);

      try {
        const result = await apiCall();
        const duration = this.endTimer(operation);

        // Report metrics in production
        if (process.env.NODE_ENV === 'production') {
          this.reportMetric(operation, duration);
        }

        resolve(result);
      } catch (error) {
        this.endTimer(operation);
        reject(error);
      }
    });
  }

  private static reportMetric(operation: string, duration: number): void {
    // Send to your analytics service
    console.log(`API Performance: ${operation} - ${duration}ms`);
  }
}
```

## Advanced Patterns

### 1. Error Boundary Integration

Use specialized error boundaries for different types of API errors:

```typescript
// Component with comprehensive error handling
import { ApiErrorBoundary } from '@/components/base/api-error-boundary';
import { DataErrorBoundary } from '@/components/base/data-error-boundary';
import { AsyncErrorBoundary } from '@/components/base/async-error-boundary';

function MyComponent() {
  return (
    <ApiErrorBoundary onRetry={refetchData}>
      <DataErrorBoundary
        dataSource="user-api"
        fallbackData={mockUserData}
        onClearCache={() => clearUserCache()}
      >
        <AsyncErrorBoundary
          maxRetries={3}
          retryDelay={1000}
          showProgress={true}
        >
          <UserDataDisplay />
        </AsyncErrorBoundary>
      </DataErrorBoundary>
    </ApiErrorBoundary>
  );
}
```

### 2. Service Layer Patterns

#### Repository Pattern

```typescript
// src/repositories/user-repository.ts
interface UserRepository {
  findAll(params?: PaginationParams): Promise<PaginatedResponse<User>>;
  findById(id: string): Promise<User>;
  create(data: CreateUserRequest): Promise<User>;
  update(id: string, data: UpdateUserRequest): Promise<User>;
  delete(id: string): Promise<void>;
}

class ApiUserRepository implements UserRepository {
  async findAll(params?: PaginationParams) {
    return fetchUsers(params);
  }

  async findById(id: string) {
    return fetchUserById(id);
  }

  // ... other methods
}

class MockUserRepository implements UserRepository {
  async findAll(params?: PaginationParams) {
    return mockUsers;
  }

  // ... other methods
}

// Usage
const userRepository: UserRepository =
  import.meta.env.VITE_USE_MOCK_DATA === 'true'
    ? new MockUserRepository()
    : new ApiUserRepository();
```

#### Service Factory Pattern

```typescript
// src/services/service-factory.ts
interface ServiceConfig {
  baseURL: string;
  timeout: number;
  retries: number;
  cache: boolean;
}

class ServiceFactory {
  private static services = new Map<string, any>();

  static create<T>(
    serviceClass: new (config: ServiceConfig) => T,
    config: ServiceConfig
  ): T {
    const key = serviceClass.name;

    if (!this.services.has(key)) {
      this.services.set(key, new serviceClass(config));
    }

    return this.services.get(key);
  }
}

// Usage
const userService = ServiceFactory.create(UserService, {
  baseURL: import.meta.env.VITE_API_URL,
  timeout: 10000,
  retries: 3,
  cache: true,
});
```

### 3. Advanced Caching Strategies

#### Cache with Tags

```typescript
class TaggedCache<T> {
  private cache = new Map<string, CacheEntry<T>>();
  private tags = new Map<string, Set<string>>();

  set(key: string, data: T, tags: string[] = [], ttl = 300000): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      expiresAt: Date.now() + ttl,
    });

    // Associate tags with this cache entry
    tags.forEach(tag => {
      if (!this.tags.has(tag)) {
        this.tags.set(tag, new Set());
      }
      this.tags.get(tag)!.add(key);
    });
  }

  invalidateByTag(tag: string): void {
    const keys = this.tags.get(tag);
    if (keys) {
      keys.forEach(key => this.cache.delete(key));
      this.tags.delete(tag);
    }
  }
}

// Usage
const cache = new TaggedCache<User>();
cache.set('user_123', userData, ['user', 'profile', 'user_123']);
cache.invalidateByTag('user'); // Invalidates all user-related cache
```

#### Background Refresh

```typescript
class BackgroundRefreshCache<T> {
  private cache = new Map<string, CacheEntry<T>>();
  private refreshPromises = new Map<string, Promise<T>>();

  async get(
    key: string,
    fetcher: () => Promise<T>,
    ttl = 300000,
    staleTime = 60000
  ): Promise<T> {
    const entry = this.cache.get(key);
    const now = Date.now();

    // Return fresh data
    if (entry && now - entry.timestamp < staleTime) {
      return entry.data;
    }

    // Return stale data but refresh in background
    if (entry && now - entry.timestamp < ttl) {
      this.refreshInBackground(key, fetcher, ttl);
      return entry.data;
    }

    // Fetch fresh data
    return this.fetchAndCache(key, fetcher, ttl);
  }

  private async refreshInBackground(
    key: string,
    fetcher: () => Promise<T>,
    ttl: number
  ): Promise<void> {
    if (this.refreshPromises.has(key)) return;

    const promise = this.fetchAndCache(key, fetcher, ttl);
    this.refreshPromises.set(key, promise);

    try {
      await promise;
    } finally {
      this.refreshPromises.delete(key);
    }
  }

  private async fetchAndCache(
    key: string,
    fetcher: () => Promise<T>,
    ttl: number
  ): Promise<T> {
    const data = await fetcher();
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      expiresAt: Date.now() + ttl,
    });
    return data;
  }
}
```

### 4. Real-time Data Integration

#### WebSocket Integration

```typescript
// src/services/websocket-service.ts
class WebSocketService {
  private ws: WebSocket | null = null;
  private listeners = new Map<string, Set<(data: any) => void>>();
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;

  connect(url: string): void {
    this.ws = new WebSocket(url);

    this.ws.onopen = () => {
      console.log('WebSocket connected');
      this.reconnectAttempts = 0;
    };

    this.ws.onmessage = event => {
      const message = JSON.parse(event.data);
      this.notifyListeners(message.type, message.data);
    };

    this.ws.onclose = () => {
      console.log('WebSocket disconnected');
      this.attemptReconnect(url);
    };

    this.ws.onerror = error => {
      console.error('WebSocket error:', error);
    };
  }

  subscribe(event: string, callback: (data: any) => void): () => void {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, new Set());
    }

    this.listeners.get(event)!.add(callback);

    // Return unsubscribe function
    return () => {
      this.listeners.get(event)?.delete(callback);
    };
  }

  private notifyListeners(event: string, data: any): void {
    const callbacks = this.listeners.get(event);
    if (callbacks) {
      callbacks.forEach(callback => callback(data));
    }
  }

  private attemptReconnect(url: string): void {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      const delay = Math.pow(2, this.reconnectAttempts) * 1000;

      setTimeout(() => {
        console.log(
          `Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})`
        );
        this.connect(url);
      }, delay);
    }
  }
}

// React hook for WebSocket
export function useWebSocket(url: string, event: string) {
  const [data, setData] = useState(null);
  const [connected, setConnected] = useState(false);

  useEffect(() => {
    const ws = new WebSocketService();
    ws.connect(url);

    const unsubscribe = ws.subscribe(event, newData => {
      setData(newData);
    });

    ws.subscribe('connection', status => {
      setConnected(status === 'connected');
    });

    return () => {
      unsubscribe();
      ws.disconnect();
    };
  }, [url, event]);

  return { data, connected };
}
```

### 5. API Mocking and Testing

#### MSW (Mock Service Worker) Integration

```typescript
// src/mocks/handlers.ts
import { rest } from 'msw';
import { mockUsers } from './data/users';

export const handlers = [
  rest.get('/api/users', (req, res, ctx) => {
    const page = Number(req.url.searchParams.get('page')) || 0;
    const size = Number(req.url.searchParams.get('size')) || 10;

    const start = page * size;
    const end = start + size;
    const content = mockUsers.slice(start, end);

    return res(
      ctx.json({
        content,
        totalElements: mockUsers.length,
        totalPages: Math.ceil(mockUsers.length / size),
        size,
        number: page,
        first: page === 0,
        last: end >= mockUsers.length,
      })
    );
  }),

  rest.post('/api/users', (req, res, ctx) => {
    const newUser = {
      id: Date.now().toString(),
      ...req.body,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    mockUsers.push(newUser);

    return res(ctx.json(newUser));
  }),
];

// src/mocks/browser.ts
import { setupWorker } from 'msw';
import { handlers } from './handlers';

export const worker = setupWorker(...handlers);

// src/main.tsx
if (import.meta.env.VITE_ENABLE_API_MOCKING === 'true') {
  const { worker } = await import('./mocks/browser');
  worker.start();
}
```

## Best Practices Summary

1. **Layered Error Handling**: Use specialized error boundaries for different error types (API, data, async, form)
2. **Consistent Service Patterns**: Implement repository or service factory patterns for maintainable code
3. **Advanced Caching**: Use tagged caching and background refresh for optimal performance
4. **Real-time Integration**: Implement WebSocket services with automatic reconnection
5. **Comprehensive Testing**: Use MSW for API mocking and write tests for all service layers
6. **Type Safety**: Define comprehensive TypeScript interfaces for all API interactions
7. **Performance Monitoring**: Track API performance and implement alerting for slow operations
8. **Security**: Implement proper authentication, authorization, and data sanitization
9. **Documentation**: Maintain up-to-date API documentation and service examples
10. **Fallback Strategies**: Always have fallback mechanisms for when APIs are unavailable

## Error Boundary Usage Guide

### When to Use Each Error Boundary

- **ErrorBoundary**: General component errors, rendering issues
- **ApiErrorBoundary**: Network errors, API failures, authentication issues
- **DataErrorBoundary**: Data parsing, validation, transformation errors
- **AsyncErrorBoundary**: Promise rejections, timeout errors, loading failures
- **FormErrorBoundary**: Form validation, submission errors, field rendering issues

### Nesting Error Boundaries

```typescript
function App() {
  return (
    <ErrorBoundary level="critical">
      <Router>
        <Routes>
          <Route path="/dashboard" element={
            <ApiErrorBoundary>
              <DataErrorBoundary dataSource="dashboard-api">
                <AsyncErrorBoundary maxRetries={3}>
                  <Dashboard />
                </AsyncErrorBoundary>
              </DataErrorBoundary>
            </ApiErrorBoundary>
          } />
        </Routes>
      </Router>
    </ErrorBoundary>
  );
}
```

This comprehensive guide provides enterprise-grade patterns for API integration while maintaining consistency with the Base Portal Dashboard template architecture.

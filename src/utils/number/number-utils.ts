/**
 * Number Utilities
 *
 * This module provides utility functions for number manipulation,
 * formatting, and mathematical operations
 */

// ============================================================================
// FORMATTING UTILITIES
// ============================================================================

/**
 * Format number with thousand separators
 *
 * @param num - Number to format
 * @param locale - Locale for formatting (default: 'en-US')
 * @returns Formatted number string
 */
export const formatNumber = (num: number, locale: string = 'en-US'): string => {
  if (typeof num !== 'number' || isNaN(num)) return '0';
  return new Intl.NumberFormat(locale).format(num);
};

/**
 * Format number with thousand separators using custom separator
 *
 * @param num - Number to format
 * @param separator - Separator character (default: ',')
 * @returns Formatted number string
 */
export const formatNumberWithSeparator = (
  num: number,
  separator: string = ','
): string => {
  return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, separator);
};

/**
 * Format number as currency
 *
 * @param amount - Amount to format
 * @param currency - Currency code (default: 'USD')
 * @param locale - Locale for formatting (default: 'en-US')
 * @returns Formatted currency string
 */
export const formatCurrency = (
  amount: number,
  currency: string = 'USD',
  locale: string = 'en-US'
): string => {
  if (typeof amount !== 'number' || isNaN(amount)) return '$0.00';

  try {
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency,
    }).format(amount);
  } catch (error) {
    console.error('Error formatting currency:', error);
    return `${currency} ${amount.toFixed(2)}`;
  }
};

/**
 * Format number as percentage
 *
 * @param num - Number to format (e.g., 0.5 for 50%)
 * @param decimals - Number of decimal places (default: 1)
 * @param locale - Locale for formatting (default: 'en-US')
 * @returns Formatted percentage string
 */
export const formatPercentage = (
  num: number,
  decimals: number = 1,
  locale: string = 'en-US'
): string => {
  if (typeof num !== 'number' || isNaN(num)) return '0%';

  try {
    return new Intl.NumberFormat(locale, {
      style: 'percent',
      minimumFractionDigits: decimals,
      maximumFractionDigits: decimals,
    }).format(num);
  } catch (error) {
    console.error('Error formatting percentage:', error);
    return `${(num * 100).toFixed(decimals)}%`;
  }
};

/**
 * Convert bytes to human readable format
 *
 * @param bytes - Number of bytes
 * @param decimals - Number of decimal places (default: 2)
 * @returns Formatted string (e.g., "1.5 MB")
 */
export const formatBytes = (bytes: number, decimals: number = 2): string => {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(decimals))} ${sizes[i]}`;
};

/**
 * Convert number to ordinal string (1st, 2nd, 3rd, etc.)
 *
 * @param num - Number to convert
 * @returns Ordinal string
 */
export const toOrdinal = (num: number): string => {
  const j = num % 10;
  const k = num % 100;

  if (j === 1 && k !== 11) return `${num}st`;
  if (j === 2 && k !== 12) return `${num}nd`;
  if (j === 3 && k !== 13) return `${num}rd`;

  return `${num}th`;
};

/**
 * Convert number to roman numerals
 *
 * @param num - Number to convert (1-3999)
 * @returns Roman numeral string
 */
export const toRoman = (num: number): string => {
  if (num < 1 || num > 3999) {
    throw new Error('Number must be between 1 and 3999');
  }

  const values = [1000, 900, 500, 400, 100, 90, 50, 40, 10, 9, 5, 4, 1];
  const symbols = [
    'M',
    'CM',
    'D',
    'CD',
    'C',
    'XC',
    'L',
    'XL',
    'X',
    'IX',
    'V',
    'IV',
    'I',
  ];

  let result = '';

  for (let i = 0; i < values.length; i++) {
    while (num >= values[i]) {
      result += symbols[i];
      num -= values[i];
    }
  }

  return result;
};

// ============================================================================
// MATHEMATICAL OPERATIONS
// ============================================================================

/**
 * Round number to specified decimal places
 *
 * @param num - Number to round
 * @param decimals - Number of decimal places
 * @returns Rounded number
 */
export const round = (num: number, decimals: number = 2): number => {
  if (typeof num !== 'number' || isNaN(num)) return 0;
  return Math.round(num * Math.pow(10, decimals)) / Math.pow(10, decimals);
};

/**
 * Round number to specified decimal places (alias for round)
 *
 * @param num - Number to round
 * @param decimals - Number of decimal places (default: 2)
 * @returns Rounded number
 */
export const roundTo = (num: number, decimals: number = 2): number => {
  return round(num, decimals);
};

/**
 * Clamp number between min and max values
 *
 * @param num - Number to clamp
 * @param min - Minimum value
 * @param max - Maximum value
 * @returns Clamped number
 */
export const clamp = (num: number, min: number, max: number): number => {
  return Math.min(Math.max(num, min), max);
};

/**
 * Check if number is in range (inclusive)
 *
 * @param num - Number to check
 * @param min - Minimum value
 * @param max - Maximum value
 * @returns True if number is in range
 */
export const inRange = (num: number, min: number, max: number): boolean => {
  return num >= min && num <= max;
};

/**
 * Generate random number between min and max
 *
 * @param min - Minimum value (inclusive)
 * @param max - Maximum value (exclusive)
 * @returns Random number
 */
export const randomBetween = (min: number, max: number): number => {
  return Math.random() * (max - min) + min;
};

/**
 * Generate random integer between min and max
 *
 * @param min - Minimum value (inclusive)
 * @param max - Maximum value (inclusive)
 * @returns Random integer
 */
export const randomInt = (min: number, max: number): number => {
  return Math.floor(Math.random() * (max - min + 1)) + min;
};

/**
 * Generate random integer between min and max (alias for randomInt)
 *
 * @param min - Minimum value (inclusive)
 * @param max - Maximum value (inclusive)
 * @returns Random integer
 */
export const randomIntBetween = (min: number, max: number): number => {
  return randomInt(min, max);
};

/**
 * Convert degrees to radians
 *
 * @param degrees - Degrees to convert
 * @returns Radians
 */
export const degreesToRadians = (degrees: number): number => {
  return degrees * (Math.PI / 180);
};

/**
 * Convert radians to degrees
 *
 * @param radians - Radians to convert
 * @returns Degrees
 */
export const radiansToDegrees = (radians: number): number => {
  return radians * (180 / Math.PI);
};

/**
 * Calculate distance between two points
 *
 * @param x1 - X coordinate of first point
 * @param y1 - Y coordinate of first point
 * @param x2 - X coordinate of second point
 * @param y2 - Y coordinate of second point
 * @returns Distance between points
 */
export const distance = (
  x1: number,
  y1: number,
  x2: number,
  y2: number
): number => {
  const dx = x2 - x1;
  const dy = y2 - y1;
  return Math.sqrt(dx * dx + dy * dy);
};

/**
 * Linear interpolation between two values
 *
 * @param start - Start value
 * @param end - End value
 * @param t - Interpolation factor (0-1)
 * @returns Interpolated value
 */
export const lerp = (start: number, end: number, t: number): number => {
  return start + (end - start) * t;
};

/**
 * Map a value from one range to another
 *
 * @param value - Value to map
 * @param fromMin - Source range minimum
 * @param fromMax - Source range maximum
 * @param toMin - Target range minimum
 * @param toMax - Target range maximum
 * @returns Mapped value
 */
export const mapRange = (
  value: number,
  fromMin: number,
  fromMax: number,
  toMin: number,
  toMax: number
): number => {
  return toMin + ((value - fromMin) * (toMax - toMin)) / (fromMax - fromMin);
};

// ============================================================================
// NUMBER PROPERTIES AND VALIDATION
// ============================================================================

/**
 * Check if number is even
 *
 * @param num - Number to check
 * @returns True if number is even
 */
export const isEven = (num: number): boolean => {
  return num % 2 === 0;
};

/**
 * Check if number is odd
 *
 * @param num - Number to check
 * @returns True if number is odd
 */
export const isOdd = (num: number): boolean => {
  return num % 2 !== 0;
};

/**
 * Check if number is prime
 *
 * @param num - Number to check
 * @returns True if number is prime
 */
export const isPrime = (num: number): boolean => {
  if (num < 2) return false;
  if (num === 2) return true;
  if (num % 2 === 0) return false;

  for (let i = 3; i <= Math.sqrt(num); i += 2) {
    if (num % i === 0) return false;
  }

  return true;
};

// ============================================================================
// ARRAY OPERATIONS
// ============================================================================

/**
 * Calculate average of numbers array
 *
 * @param numbers - Array of numbers
 * @returns Average value
 */
export const average = (numbers: number[]): number => {
  if (!numbers.length) return 0;
  return sum(numbers) / numbers.length;
};

/**
 * Calculate sum of numbers array
 *
 * @param numbers - Array of numbers
 * @returns Sum of numbers
 */
export const sum = (numbers: number[]): number => {
  return numbers.reduce((acc, num) => acc + num, 0);
};

/**
 * Find minimum value in numbers array
 *
 * @param numbers - Array of numbers
 * @returns Minimum value
 */
export const min = (numbers: number[]): number => {
  return Math.min(...numbers);
};

/**
 * Find maximum value in numbers array
 *
 * @param numbers - Array of numbers
 * @returns Maximum value
 */
export const max = (numbers: number[]): number => {
  return Math.max(...numbers);
};

// ============================================================================
// ADVANCED MATHEMATICAL FUNCTIONS
// ============================================================================

/**
 * Calculate factorial of a number
 *
 * @param num - Number to calculate factorial for
 * @returns Factorial result
 */
export const factorial = (num: number): number => {
  if (num < 0) throw new Error('Factorial is not defined for negative numbers');
  if (num === 0 || num === 1) return 1;

  let result = 1;
  for (let i = 2; i <= num; i++) {
    result *= i;
  }

  return result;
};

/**
 * Calculate greatest common divisor (GCD)
 *
 * @param a - First number
 * @param b - Second number
 * @returns Greatest common divisor
 */
export const gcd = (a: number, b: number): number => {
  while (b !== 0) {
    const temp = b;
    b = a % b;
    a = temp;
  }
  return Math.abs(a);
};

/**
 * Calculate least common multiple (LCM)
 *
 * @param a - First number
 * @param b - Second number
 * @returns Least common multiple
 */
export const lcm = (a: number, b: number): number => {
  return Math.abs(a * b) / gcd(a, b);
};

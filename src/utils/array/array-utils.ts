/**
 * Array Utilities
 *
 * This module provides utility functions for array manipulation and operations
 */

/**
 * Remove duplicate values from array
 *
 * @param arr - Array to remove duplicates from
 * @returns Array without duplicates
 */
export const removeDuplicates = <T>(arr: T[]): T[] => {
  return [...new Set(arr)];
};

/**
 * Remove duplicate objects from array based on a key
 *
 * @param arr - Array of objects
 * @param key - Key to use for comparison
 * @returns Array without duplicate objects
 */
export const removeDuplicatesByKey = <T, K extends keyof T>(
  arr: T[],
  key: K
): T[] => {
  const seen = new Set();
  return arr.filter(item => {
    const keyValue = item[key];
    if (seen.has(keyValue)) {
      return false;
    }
    seen.add(keyValue);
    return true;
  });
};

/**
 * Chunk array into smaller arrays of specified size
 *
 * @param arr - Array to chunk
 * @param size - Size of each chunk
 * @returns Array of chunks
 */
export const chunk = <T>(arr: T[], size: number): T[][] => {
  const chunks: T[][] = [];
  for (let i = 0; i < arr.length; i += size) {
    chunks.push(arr.slice(i, i + size));
  }
  return chunks;
};

/**
 * Flatten nested arrays
 *
 * @param arr - Nested array to flatten
 * @returns Flattened array
 */
export const flatten = <T>(arr: (T | T[])[]): T[] => {
  return arr.reduce<T[]>((acc, curr) => {
    return acc.concat(Array.isArray(curr) ? flatten(curr) : curr);
  }, []);
};

/**
 * Shuffle array randomly
 *
 * @param arr - Array to shuffle
 * @returns Shuffled array (new array)
 */
export const shuffle = <T>(arr: T[]): T[] => {
  const shuffled = [...arr];
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  return shuffled;
};

/**
 * Get random element from array
 *
 * @param arr - Array to get random element from
 * @returns Random element or undefined if array is empty
 */
export const randomElement = <T>(arr: T[]): T | undefined => {
  if (arr.length === 0) return undefined;
  return arr[Math.floor(Math.random() * arr.length)];
};

/**
 * Get multiple random elements from array
 *
 * @param arr - Array to get random elements from
 * @param count - Number of elements to get
 * @returns Array of random elements
 */
export const randomElements = <T>(arr: T[], count: number): T[] => {
  if (count >= arr.length) return shuffle(arr);

  const shuffled = shuffle(arr);
  return shuffled.slice(0, count);
};

/**
 * Group array elements by a key
 *
 * @param arr - Array to group
 * @param key - Key to group by
 * @returns Object with grouped elements
 */
export const groupBy = <T, K extends keyof T>(
  arr: T[],
  key: K
): Record<string, T[]> => {
  return arr.reduce(
    (groups, item) => {
      const groupKey = String(item[key]);
      if (!groups[groupKey]) {
        groups[groupKey] = [];
      }
      groups[groupKey].push(item);
      return groups;
    },
    {} as Record<string, T[]>
  );
};

/**
 * Sort array of objects by a key
 *
 * @param arr - Array to sort
 * @param key - Key to sort by
 * @param order - Sort order ('asc' or 'desc')
 * @returns Sorted array (new array)
 */
export const sortBy = <T, K extends keyof T>(
  arr: T[],
  key: K,
  order: 'asc' | 'desc' = 'asc'
): T[] => {
  return [...arr].sort((a, b) => {
    const aVal = a[key];
    const bVal = b[key];

    if (aVal < bVal) return order === 'asc' ? -1 : 1;
    if (aVal > bVal) return order === 'asc' ? 1 : -1;
    return 0;
  });
};

/**
 * Find the intersection of two arrays
 *
 * @param arr1 - First array
 * @param arr2 - Second array
 * @returns Array containing elements present in both arrays
 */
export const intersection = <T>(arr1: T[], arr2: T[]): T[] => {
  return arr1.filter(item => arr2.includes(item));
};

/**
 * Find the difference between two arrays
 *
 * @param arr1 - First array
 * @param arr2 - Second array
 * @returns Array containing elements in arr1 but not in arr2
 */
export const difference = <T>(arr1: T[], arr2: T[]): T[] => {
  return arr1.filter(item => !arr2.includes(item));
};

/**
 * Find the union of two arrays
 *
 * @param arr1 - First array
 * @param arr2 - Second array
 * @returns Array containing all unique elements from both arrays
 */
export const union = <T>(arr1: T[], arr2: T[]): T[] => {
  return removeDuplicates([...arr1, ...arr2]);
};

/**
 * Check if array is empty
 *
 * @param arr - Array to check
 * @returns True if array is empty
 */
export const isEmpty = <T>(arr: T[]): boolean => {
  return arr.length === 0;
};

/**
 * Get the last element of an array
 *
 * @param arr - Array to get last element from
 * @returns Last element or undefined if array is empty
 */
export const last = <T>(arr: T[]): T | undefined => {
  return arr[arr.length - 1];
};

/**
 * Get the first element of an array
 *
 * @param arr - Array to get first element from
 * @returns First element or undefined if array is empty
 */
export const first = <T>(arr: T[]): T | undefined => {
  return arr[0];
};

/**
 * Create array with range of numbers
 *
 * @param start - Start number
 * @param end - End number
 * @param step - Step size (default: 1)
 * @returns Array of numbers
 */
export const range = (
  start: number,
  end: number,
  step: number = 1
): number[] => {
  const result: number[] = [];
  for (let i = start; i < end; i += step) {
    result.push(i);
  }
  return result;
};

/**
 * Partition array into two arrays based on predicate
 *
 * @param arr - Array to partition
 * @param predicate - Function to test each element
 * @returns Array with two arrays: [truthy, falsy]
 */
export const partition = <T>(
  arr: T[],
  predicate: (_item: T) => boolean
): [T[], T[]] => {
  const truthy: T[] = [];
  const falsy: T[] = [];

  arr.forEach(item => {
    if (predicate(item)) {
      truthy.push(item);
    } else {
      falsy.push(item);
    }
  });

  return [truthy, falsy];
};

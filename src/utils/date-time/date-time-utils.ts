/**
 * Date and Time Utilities
 *
 * This module provides a comprehensive set of utility functions for date and time manipulation,
 * formatting, calculations, and transformations used throughout the application
 */
import moment from 'moment';

/**
 * Dictionary mapping month numbers to their abbreviated names
 */
const numberToMonthDict: { [month: number]: string } = {
  1: 'Jan',
  2: 'Feb',
  3: 'Mar',
  4: 'Apr',
  5: 'May',
  6: 'Jun',
  7: 'Jul',
  8: 'Aug',
  9: 'Sep',
  10: 'Oct',
  11: 'Nov',
  12: 'Dec',
};

/**
 * Standard number of digits to use when formatting dates
 */
const numberOfDigits = 2;

// ============================================================================
// RELATIVE TIME FORMATTING
// ============================================================================

/**
 * Format a timestamp in a human-readable relative format
 *
 * @param timestamp - Unix timestamp in milliseconds
 * @returns Formatted string (e.g., "just now", "5m ago", "2h ago", "3d ago")
 * @example
 * // Returns "2h ago" for a timestamp from 2 hours ago
 * formatRelativeTime(Date.now() - 2 * 60 * 60 * 1000)
 */
export const formatRelativeTime = (timestamp: number): string => {
  const now = Date.now();
  const diff = now - timestamp;

  // Convert difference to minutes, hours, days
  const minutes = Math.floor(diff / (1000 * 60));
  const hours = Math.floor(diff / (1000 * 60 * 60));
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));

  if (minutes < 1) return 'just now';
  if (minutes < 60) return `${minutes}m ago`;
  if (hours < 24) return `${hours}h ago`;
  if (days < 7) return `${days}d ago`;

  // For older dates, show the actual date
  return moment(timestamp).format('MMM DD, YYYY');
};

/**
 * Format duration in milliseconds to human readable format
 *
 * @param milliseconds - Duration in milliseconds
 * @returns Formatted string (e.g., "2h 30m", "45s", "1d 3h")
 */
export const formatDuration = (milliseconds: number): string => {
  const seconds = Math.floor(milliseconds / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);

  if (days > 0) {
    const remainingHours = hours % 24;
    return remainingHours > 0 ? `${days}d ${remainingHours}h` : `${days}d`;
  }

  if (hours > 0) {
    const remainingMinutes = minutes % 60;
    return remainingMinutes > 0
      ? `${hours}h ${remainingMinutes}m`
      : `${hours}h`;
  }

  if (minutes > 0) {
    const remainingSeconds = seconds % 60;
    return remainingSeconds > 0
      ? `${minutes}m ${remainingSeconds}s`
      : `${minutes}m`;
  }

  return `${seconds}s`;
};

/**
 * Get relative time string (e.g., "2 hours ago", "in 3 days")
 *
 * @param date - Date to compare
 * @param locale - Locale string (default: 'en-US')
 * @returns Relative time string
 */
export const getRelativeTime = (
  date: Date | number,
  locale: string = 'en-US'
): string => {
  try {
    const rtf = new Intl.RelativeTimeFormat(locale, { numeric: 'auto' });
    const diff = new Date(date).getTime() - Date.now();
    const absDiff = Math.abs(diff);

    const units: { unit: Intl.RelativeTimeFormatUnit; ms: number }[] = [
      { unit: 'year', ms: 365 * 24 * 60 * 60 * 1000 },
      { unit: 'month', ms: 30 * 24 * 60 * 60 * 1000 },
      { unit: 'day', ms: 24 * 60 * 60 * 1000 },
      { unit: 'hour', ms: 60 * 60 * 1000 },
      { unit: 'minute', ms: 60 * 1000 },
      { unit: 'second', ms: 1000 },
    ];

    for (const { unit, ms } of units) {
      if (absDiff >= ms) {
        return rtf.format(Math.round(diff / ms), unit);
      }
    }

    return rtf.format(0, 'second');
  } catch (error) {
    console.error('Error formatting relative time:', error);
    return 'Unknown time';
  }
};

// ============================================================================
// DATE FORMATTING
// ============================================================================

/**
 * Format date to display format (e.g., "Jan 15, 2024")
 *
 * @param date - Date object or timestamp
 * @returns Formatted date string
 */
export const formatDisplayDate = (date: Date | number): string => {
  return moment(date).format('MMM DD, YYYY');
};

/**
 * Format date to ISO format (YYYY-MM-DD)
 *
 * @param date - Date object or timestamp
 * @returns ISO formatted date string
 */
export const formatISODate = (date: Date | number): string => {
  return moment(date).format('YYYY-MM-DD');
};

/**
 * Format date and time to display format (e.g., "Jan 15, 2024 at 3:30 PM")
 *
 * @param date - Date object or timestamp
 * @returns Formatted date and time string
 */
export const formatDisplayDateTime = (date: Date | number): string => {
  return moment(date).format('MMM DD, YYYY [at] h:mm A');
};

/**
 * Format time to display format (e.g., "3:30 PM")
 *
 * @param date - Date object or timestamp
 * @returns Formatted time string
 */
export const formatDisplayTime = (date: Date | number): string => {
  return moment(date).format('h:mm A');
};

/**
 * Format date for API requests (ISO 8601 format)
 *
 * @param date - Date object or timestamp
 * @returns ISO 8601 formatted string
 */
export const formatAPIDate = (date: Date | number): string => {
  return moment(date).toISOString();
};

/**
 * Format date for specific locale
 *
 * @param date - Date to format
 * @param locale - Locale string (default: 'en-US')
 * @param options - Intl.DateTimeFormatOptions
 * @returns Formatted date string
 */
export const formatDateForLocale = (
  date: Date | number,
  locale: string = 'en-US',
  options?: Intl.DateTimeFormatOptions
): string => {
  try {
    return new Intl.DateTimeFormat(locale, options).format(new Date(date));
  } catch (error) {
    console.error('Error formatting date:', error);
    return new Date(date).toLocaleDateString();
  }
};

// ============================================================================
// SPECIFIC TIMESTAMP FORMATTERS (from second version)
// ============================================================================

/**
 * Convert timestamp to hour:minute string format
 *
 * @param timestamp - Unix timestamp in milliseconds
 * @returns Formatted time string (e.g., "14:00")
 */
export function toHourMinuteString(timestamp: number): string {
  const date = new Date(timestamp);
  const hours = `${date.getHours()}`.padStart(numberOfDigits, '0');
  return `${hours}:00`;
}

/**
 * Convert timestamp to full date and time string
 *
 * @param timestamp - Unix timestamp in milliseconds
 * @returns Formatted date string (e.g., "15 Jun 2025 14:30")
 */
export function toDayMonthNameHourMinuteString(timestamp: number): string {
  const date = new Date(timestamp);
  const day = `${date.getDate()}`.padStart(numberOfDigits, '0');
  const monthName = `${numberToMonthDict[date.getMonth() + 1]}`;
  const year = date.getFullYear();
  const hours = `${date.getHours()}`.padStart(numberOfDigits, '0');
  const minutes = `${date.getMinutes()}`.padStart(numberOfDigits, '0');
  return `${day} ${monthName} ${year} ${hours}:${minutes}`;
}

/**
 * Convert timestamp to date string with month name
 *
 * @param timestamp - Unix timestamp in milliseconds
 * @returns Formatted date string (e.g., "15 Jun 2025")
 */
export function toDayMonthNameString(timestamp: number): string {
  const date = new Date(timestamp);
  const day = `${date.getDate()}`.padStart(numberOfDigits, '0');
  const monthName = `${numberToMonthDict[date.getMonth() + 1]}`;
  const year = date.getFullYear();
  return `${day} ${monthName} ${year}`;
}

/**
 * Convert timestamp to short date string
 *
 * @param timestamp - Unix timestamp in milliseconds
 * @returns Formatted date string (e.g., "15/06")
 */
export function toDayMonthString(timestamp: number): string {
  const date = new Date(timestamp);
  const day = `${date.getDate()}`.padStart(numberOfDigits, '0');
  const month = `${date.getMonth() + 1}`.padStart(numberOfDigits, '0');
  return `${day}/${month} `;
}

/**
 * Convert timestamp to week number string
 *
 * @param timestamp - Unix timestamp in milliseconds
 * @returns Week number string (e.g., "Week 24")
 */
export function toWeekNumberString(timestamp: number): string {
  const weekNumber = `${moment(timestamp).isoWeek()}`.padStart(
    numberOfDigits,
    '0'
  );
  return `Week ${weekNumber}`;
}

/**
 * Convert timestamp to week range string
 *
 * @param timestamp - Unix timestamp in milliseconds
 * @returns Week range string (e.g., "Week 24: 15/06 - 21/06")
 */
export function toWeekStartEndDayMonthString(timestamp: number): string {
  const startDate = new Date(timestamp);
  const startDay = `${startDate.getDate()}`.padStart(numberOfDigits, '0');
  const startMonth = `${startDate.getMonth() + 1}`.padStart(
    numberOfDigits,
    '0'
  );
  const endDate = new Date(timestamp + 6 * 24 * 60 * 60 * 1000);
  const endDay = `${endDate.getDate()}`.padStart(numberOfDigits, '0');
  const endMonth = `${endDate.getMonth() + 1}`.padStart(numberOfDigits, '0');
  const weekNumber = `${moment(timestamp).isoWeek()}`.padStart(
    numberOfDigits,
    '0'
  );
  return `Week ${weekNumber}: ${startDay}/${startMonth} - ${endDay}/${endMonth}`;
}

/**
 * Convert timestamp to month name and year string
 *
 * @param timestamp - Unix timestamp in milliseconds
 * @returns Month and year string (e.g., "Jun 2025")
 */
export function toMonthNameString(timestamp: number): string {
  const date = new Date(timestamp);
  const monthName = `${numberToMonthDict[date.getMonth() + 1]}`;
  const year = date.getFullYear();
  return `${monthName} ${year}`;
}

/**
 * Convert timestamp to year string
 *
 * @param timestamp - Unix timestamp in milliseconds
 * @returns Year string (e.g., "2025")
 */
export function toYearString(timestamp: number): string {
  const date = new Date(timestamp);
  const year = date.getFullYear();
  return `${year}`;
}

// ============================================================================
// DATE CALCULATIONS
// ============================================================================

/**
 * Check if a date is today
 *
 * @param date - Date object or timestamp
 * @returns True if the date is today
 */
export const isToday = (date: Date | number): boolean => {
  const today = new Date();
  const checkDate = new Date(date);

  return (
    today.getDate() === checkDate.getDate() &&
    today.getMonth() === checkDate.getMonth() &&
    today.getFullYear() === checkDate.getFullYear()
  );
};

/**
 * Check if a date is yesterday
 *
 * @param date - Date object or timestamp
 * @returns True if the date is yesterday
 */
export const isYesterday = (date: Date | number): boolean => {
  const yesterday = new Date();
  yesterday.setDate(yesterday.getDate() - 1);
  const checkDate = new Date(date);

  return (
    yesterday.getDate() === checkDate.getDate() &&
    yesterday.getMonth() === checkDate.getMonth() &&
    yesterday.getFullYear() === checkDate.getFullYear()
  );
};

/**
 * Check if a date is in the current week
 *
 * @param date - Date object or timestamp
 * @returns True if the date is in the current week
 */
export const isThisWeek = (date: Date | number): boolean => {
  return moment(date).isSame(moment(), 'week');
};

/**
 * Get the start of day for a given date
 *
 * @param date - Date object or timestamp
 * @returns Date object representing start of day
 */
export const getStartOfDay = (date: Date | number): Date => {
  return moment(date).startOf('day').toDate();
};

/**
 * Get the end of day for a given date
 *
 * @param date - Date object or timestamp
 * @returns Date object representing end of day
 */
export const getEndOfDay = (date: Date | number): Date => {
  return moment(date).endOf('day').toDate();
};

/**
 * Add time to a date
 *
 * @param date - Date object or timestamp
 * @param amount - Amount to add
 * @param unit - Unit of time ('days', 'hours', 'minutes', etc.)
 * @returns New date with time added
 */
export const addTime = (
  date: Date | number,
  amount: number,
  unit: moment.unitOfTime.DurationConstructor
): Date => {
  return moment(date).add(amount, unit).toDate();
};

/**
 * Subtract time from a date
 *
 * @param date - Date object or timestamp
 * @param amount - Amount to subtract
 * @param unit - Unit of time ('days', 'hours', 'minutes', etc.)
 * @returns New date with time subtracted
 */
export const subtractTime = (
  date: Date | number,
  amount: number,
  unit: moment.unitOfTime.DurationConstructor
): Date => {
  return moment(date).subtract(amount, unit).toDate();
};

/**
 * Get difference between two dates
 *
 * @param date1 - First date
 * @param date2 - Second date
 * @param unit - Unit for the difference ('days', 'hours', 'minutes', etc.)
 * @returns Difference in the specified unit
 */
export const getDateDifference = (
  date1: Date | number,
  date2: Date | number,
  unit: moment.unitOfTime.Diff = 'milliseconds'
): number => {
  return moment(date1).diff(moment(date2), unit);
};

/**
 * Calculate number of days between two timestamps
 *
 * @param minX - Start timestamp
 * @param maxX - End timestamp
 * @returns Number of days
 */
export function getNumberOfDays({
  minX,
  maxX,
}: {
  minX: number;
  maxX: number;
}): number {
  return (maxX - minX) / (1000 * 3600 * 24);
}

/**
 * Calculate rounded number of days between two timestamps
 *
 * @param minX - Start timestamp
 * @param maxX - End timestamp
 * @returns Rounded number of days
 */
export function getRoundingNumberOfDays({
  minX,
  maxX,
}: {
  minX: number;
  maxX: number;
}): number {
  return Math.round(getNumberOfDays({ minX, maxX }));
}

/**
 * Calculate number of weeks between two timestamps
 *
 * @param minX - Start timestamp
 * @param maxX - End timestamp
 * @returns Number of weeks
 */
export function getNumberOfWeeks({
  minX,
  maxX,
}: {
  minX: number;
  maxX: number;
}): number {
  return moment(maxX).isoWeek() - moment(minX).isoWeek();
}

/**
 * Calculate number of months between two timestamps
 *
 * @param minX - Start timestamp
 * @param maxX - End timestamp
 * @returns Number of months
 */
export function getNumberOfMonths({
  minX,
  maxX,
}: {
  minX: number;
  maxX: number;
}): number {
  var date1 = new Date(minX);
  date1 = new Date(
    date1.getFullYear(),
    date1.getMonth() + (date1.getDate() > 1 ? 1 : 0),
    1,
    0,
    0,
    0,
    0
  );
  var date2 = new Date(maxX);
  date2 = new Date(date2.getFullYear(), date2.getMonth(), 1, 0, 0, 0, 0);
  return date2.getMonth() - date1.getMonth() + 1;
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Check if a date is valid
 *
 * @param date - Date to validate
 * @returns True if the date is valid
 */
export const isValidDate = (date: any): boolean => {
  return moment(date).isValid();
};

/**
 * Get current timestamp in milliseconds
 *
 * @returns Current timestamp
 */
export const getCurrentTimestamp = (): number => {
  return Date.now();
};

/**
 * Convert date to timestamp
 *
 * @param date - Date object
 * @returns Timestamp in milliseconds
 */
export const dateToTimestamp = (date: Date): number => {
  return date.getTime();
};

/**
 * Convert timestamp to date
 *
 * @param timestamp - Timestamp in milliseconds
 * @returns Date object
 */
export const timestampToDate = (timestamp: number): Date => {
  return new Date(timestamp);
};

/**
 * Get month name from number
 *
 * @param month - Month number (1-12)
 * @returns Month abbreviation
 */
export const getMonthName = (month: number): string => {
  return numberToMonthDict[month] || '';
};

/**
 * Get age from birth date
 *
 * @param birthDate - Birth date
 * @returns Age in years
 */
export const getAge = (birthDate: Date | number): number => {
  return moment().diff(moment(birthDate), 'years');
};

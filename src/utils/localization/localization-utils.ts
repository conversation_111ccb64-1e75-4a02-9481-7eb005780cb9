/**
 * Localization Utilities
 *
 * This module provides utility functions for internationalization,
 * locale handling, and translation management
 */

/**
 * Available locale codes
 */
export const AVAILABLE_LOCALES = ['en', 'zh', 'zh-TW'] as const;
export type LocaleCode = (typeof AVAILABLE_LOCALES)[number];

/**
 * Default locale
 */
export const DEFAULT_LOCALE: LocaleCode = 'en';

/**
 * Locale display names
 */
export const LOCALE_NAMES: Record<LocaleCode, string> = {
  en: 'English',
  zh: '中文 (简体)',
  'zh-TW': '中文 (繁體)',
};

// ============================================================================
// CONTENT TRANSLATION
// ============================================================================

/**
 * Get translated content based on the current locale
 *
 * @param content - Object containing translations for different locales
 * @param locale - Current locale code
 * @returns Translated content or fallback
 */
export const getTranslatedContent = (
  content: Record<string, string>,
  locale: string
): string => {
  if (locale in content && content[locale] !== undefined) {
    return content[locale];
  }

  // Fallback to English if translation not available
  if (content.en !== undefined) {
    return content.en;
  }

  // Return first available translation
  const firstKey = Object.keys(content)[0];
  return firstKey ? content[firstKey] : '';
};

/**
 * Interpolate variables in translation string
 *
 * @param template - Translation template with placeholders
 * @param variables - Variables to interpolate
 * @returns Interpolated string
 */
export const interpolate = (
  template: string,
  variables: Record<string, string | number> = {}
): string => {
  return template.replace(/\{\{(\w+)\}\}/g, (match, key) => {
    return variables[key]?.toString() || match;
  });
};

// ============================================================================
// LOCALE DETECTION AND MANAGEMENT
// ============================================================================

/**
 * Check if locale is supported
 *
 * @param locale - Locale code to check
 * @returns True if locale is supported
 */
export const isSupportedLocale = (locale: string): locale is LocaleCode => {
  return AVAILABLE_LOCALES.includes(locale as LocaleCode);
};

/**
 * Get browser locale
 *
 * @returns Browser locale or default locale
 */
export const getBrowserLocale = (): LocaleCode => {
  if (typeof navigator === 'undefined') return DEFAULT_LOCALE;

  const browserLang = navigator.language || navigator.languages?.[0];
  if (!browserLang) return DEFAULT_LOCALE;

  // Check exact match first
  if (isSupportedLocale(browserLang)) {
    return browserLang;
  }

  // Check language part only (e.g., 'zh' from 'zh-CN')
  const langCode = browserLang.split('-')[0];
  if (isSupportedLocale(langCode)) {
    return langCode;
  }

  return DEFAULT_LOCALE;
};

/**
 * Get locale from URL parameters
 *
 * @param paramName - Parameter name to look for (default: 'lang')
 * @returns Locale from URL or null if not found/supported
 */
export const getLocaleFromURL = (
  paramName: string = 'lang'
): LocaleCode | null => {
  if (typeof window === 'undefined') return null;

  const urlParams = new URLSearchParams(window.location.search);
  const locale = urlParams.get(paramName);

  return locale && isSupportedLocale(locale) ? locale : null;
};

/**
 * Get locale from localStorage
 *
 * @param key - Storage key (default: 'locale')
 * @returns Locale from storage or null if not found/supported
 */
export const getLocaleFromStorage = (
  key: string = 'locale'
): LocaleCode | null => {
  if (typeof localStorage === 'undefined') return null;

  try {
    const stored = localStorage.getItem(key);
    return stored && isSupportedLocale(stored) ? stored : null;
  } catch {
    return null;
  }
};

/**
 * Save locale to localStorage
 *
 * @param locale - Locale to save
 * @param key - Storage key (default: 'locale')
 */
export const saveLocaleToStorage = (
  locale: LocaleCode,
  key: string = 'locale'
): void => {
  if (typeof localStorage === 'undefined') return;

  try {
    localStorage.setItem(key, locale);
  } catch {
    // Ignore storage errors
  }
};

/**
 * Determine the best locale to use based on various sources
 *
 * @param options - Configuration options
 * @returns Best locale to use
 */
export const determineLocale = (
  options: {
    urlParam?: string;
    storageKey?: string;
    fallbackToBrowser?: boolean;
  } = {}
): LocaleCode => {
  const {
    urlParam = 'lang',
    storageKey = 'locale',
    fallbackToBrowser = true,
  } = options;

  // 1. Check URL parameter first (highest priority)
  const urlLocale = getLocaleFromURL(urlParam);
  if (urlLocale) return urlLocale;

  // 2. Check localStorage
  const storedLocale = getLocaleFromStorage(storageKey);
  if (storedLocale) return storedLocale;

  // 3. Check browser language
  if (fallbackToBrowser) {
    return getBrowserLocale();
  }

  // 4. Default fallback
  return DEFAULT_LOCALE;
};

/**
 * Get locale from URL or storage
 *
 * @returns Current locale
 */
export const getCurrentLocale = (): LocaleCode => {
  // Check URL parameter
  if (typeof window !== 'undefined') {
    const urlParams = new URLSearchParams(window.location.search);
    const urlLocale = urlParams.get('locale');
    if (urlLocale && isSupportedLocale(urlLocale)) {
      return urlLocale;
    }

    // Check localStorage
    const storedLocale = localStorage.getItem('locale');
    if (storedLocale && isSupportedLocale(storedLocale)) {
      return storedLocale;
    }
  }

  // Fallback to browser locale
  return getBrowserLocale();
};

/**
 * Set locale in storage and URL
 *
 * @param locale - Locale to set
 */
export const setCurrentLocale = (locale: LocaleCode): void => {
  if (typeof window === 'undefined') return;

  // Store in localStorage
  localStorage.setItem('locale', locale);

  // Update URL parameter
  const url = new URL(window.location.href);
  url.searchParams.set('locale', locale);
  window.history.replaceState({}, '', url.toString());
};

// ============================================================================
// FORMATTING UTILITIES
// ============================================================================

/**
 * Format date for specific locale
 *
 * @param date - Date to format
 * @param locale - Locale code
 * @param options - Intl.DateTimeFormatOptions
 * @returns Formatted date string
 */
export const formatDateForLocale = (
  date: Date | number,
  locale: string = DEFAULT_LOCALE,
  options?: Intl.DateTimeFormatOptions
): string => {
  try {
    const defaultOptions: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    };

    return new Intl.DateTimeFormat(locale, options || defaultOptions).format(
      new Date(date)
    );
  } catch (error) {
    console.error('Error formatting date for locale:', error);
    return new Date(date).toLocaleDateString();
  }
};

/**
 * Format number for specific locale
 *
 * @param number - Number to format
 * @param locale - Locale code
 * @param options - Intl.NumberFormatOptions
 * @returns Formatted number string
 */
export const formatNumberForLocale = (
  number: number,
  locale: string = DEFAULT_LOCALE,
  options?: Intl.NumberFormatOptions
): string => {
  try {
    return new Intl.NumberFormat(locale, options).format(number);
  } catch (error) {
    console.error('Error formatting number for locale:', error);
    return number.toString();
  }
};

/**
 * Format currency for specific locale
 *
 * @param amount - Amount to format
 * @param currency - Currency code
 * @param locale - Locale code
 * @returns Formatted currency string
 */
export const formatCurrencyForLocale = (
  amount: number,
  currency: string = 'USD',
  locale: string = DEFAULT_LOCALE
): string => {
  try {
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency,
    }).format(amount);
  } catch (error) {
    console.error('Error formatting currency for locale:', error);
    return `${currency} ${amount.toFixed(2)}`;
  }
};

/**
 * Get relative time for specific locale
 *
 * @param date - Date to compare
 * @param locale - Locale code
 * @returns Relative time string
 */
export const getRelativeTimeForLocale = (
  date: Date | number,
  locale: string = DEFAULT_LOCALE
): string => {
  try {
    const rtf = new Intl.RelativeTimeFormat(locale, { numeric: 'auto' });
    const diff = new Date(date).getTime() - Date.now();
    const absDiff = Math.abs(diff);

    const units: { unit: Intl.RelativeTimeFormatUnit; ms: number }[] = [
      { unit: 'year', ms: 365 * 24 * 60 * 60 * 1000 },
      { unit: 'month', ms: 30 * 24 * 60 * 60 * 1000 },
      { unit: 'day', ms: 24 * 60 * 60 * 1000 },
      { unit: 'hour', ms: 60 * 60 * 1000 },
      { unit: 'minute', ms: 60 * 1000 },
      { unit: 'second', ms: 1000 },
    ];

    for (const { unit, ms } of units) {
      if (absDiff >= ms) {
        return rtf.format(Math.round(diff / ms), unit);
      }
    }

    return rtf.format(0, 'second');
  } catch (error) {
    console.error('Error formatting relative time for locale:', error);
    return 'Unknown time';
  }
};

// ============================================================================
// LANGUAGE AND TEXT UTILITIES
// ============================================================================

/**
 * Get text direction for locale
 *
 * @param locale - Locale code
 * @returns Text direction ('ltr' or 'rtl')
 */
export const getTextDirection = (locale: string): 'ltr' | 'rtl' => {
  // RTL languages
  const rtlLanguages = ['ar', 'he', 'fa', 'ur'];
  const langCode = locale.split('-')[0];

  return rtlLanguages.includes(langCode) ? 'rtl' : 'ltr';
};

/**
 * Get language name in its native script
 *
 * @param locale - Locale code
 * @returns Language name in native script
 */
export const getNativeLanguageName = (locale: LocaleCode): string => {
  return LOCALE_NAMES[locale] || locale;
};

/**
 * Pluralize text based on count and locale
 *
 * @param count - Number to check
 * @param options - Pluralization options
 * @param locale - Locale code
 * @returns Pluralized text
 */
export const pluralize = (
  count: number,
  options: {
    zero?: string;
    one: string;
    few?: string;
    many?: string;
    other: string;
  },
  locale: string = DEFAULT_LOCALE
): string => {
  try {
    const pr = new Intl.PluralRules(locale);
    const rule = pr.select(count);

    if (count === 0 && options.zero) return options.zero;

    switch (rule) {
      case 'one':
        return options.one;
      case 'few':
        return options.few || options.other;
      case 'many':
        return options.many || options.other;
      default:
        return options.other;
    }
  } catch (error) {
    console.error('Error in pluralization:', error);
    return count === 1 ? options.one : options.other;
  }
};

// ============================================================================
// DYNAMIC TRANSLATION LOADING
// ============================================================================

/**
 * Load translation file dynamically
 *
 * @param locale - Locale code
 * @param namespace - Translation namespace (e.g., 'common', 'auth')
 * @returns Promise resolving to translation object
 */
export const loadTranslation = async (
  locale: LocaleCode,
  namespace: string = 'common'
): Promise<Record<string, any>> => {
  try {
    const translation = await import(`./locales/${locale}/${namespace}.json`);
    return translation.default || translation;
  } catch (error) {
    console.error(
      `Failed to load translation for ${locale}/${namespace}:`,
      error
    );

    // Fallback to English
    if (locale !== DEFAULT_LOCALE) {
      try {
        const fallbackTranslation = await import(
          `./locales/${DEFAULT_LOCALE}/${namespace}.json`
        );
        return fallbackTranslation.default || fallbackTranslation;
      } catch (fallbackError) {
        console.error('Failed to load fallback translation:', fallbackError);
        return {};
      }
    }

    return {};
  }
};

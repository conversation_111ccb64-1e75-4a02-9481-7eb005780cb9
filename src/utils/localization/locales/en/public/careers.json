{"// Careers Page": "", "public.careers.why.title": "Why Work With Us", "public.careers.why.meaningful.title": "Meaningful Work", "public.careers.why.meaningful.description": "Join a team that's making a real impact. Your work will directly contribute to solving important problems for our customers and industry.", "public.careers.why.growth.title": "Growth Opportunities", "public.careers.why.growth.description": "We invest in our team's professional development with learning resources, mentorship, and clear career paths for advancement.", "public.careers.why.culture.title": "Inclusive Culture", "public.careers.why.culture.description": "We foster a diverse and inclusive environment where everyone can thrive, contribute their unique perspectives, and feel valued.", "public.careers.benefits.title": "Benefits & Perks", "public.careers.benefits.health.title": "Health & Wellness", "public.careers.benefits.health.insurance": "Comprehensive health insurance", "public.careers.benefits.health.mental": "Mental health resources", "public.careers.benefits.health.wellness": "Wellness stipend", "public.careers.benefits.work.title": "Work-Life Balance", "public.careers.benefits.work.flexible": "Flexible work hours", "public.careers.benefits.work.remote": "Remote work options", "public.careers.benefits.work.pto": "Generous paid time off", "public.careers.benefits.financial.title": "Financial Benefits", "public.careers.benefits.financial.salary": "Competitive salary", "public.careers.benefits.financial.equity": "Equity compensation", "public.careers.benefits.financial.401k": "401(k) matching", "public.careers.benefits.development.title": "Professional Development", "public.careers.benefits.development.stipend": "Learning stipend", "public.careers.benefits.development.conference": "Conference attendance", "public.careers.benefits.development.mentorship": "Mentorship program", "public.careers.benefits.flexible": "Flexible work arrangements", "public.careers.benefits.health": "Comprehensive health benefits", "public.careers.benefits.learning": "Continuous learning opportunities", "public.careers.benefits.vacation": "Generous vacation policy", "public.careers.benefits.culture": "Inclusive and diverse culture", "public.careers.openings.title": "Current Openings", "public.careers.openings.responsibilities": "Responsibilities:", "public.careers.openings.requirements": "Requirements:", "public.careers.openings.apply": "Apply Now", "public.careers.openings.none": "No current openings in this department.", "public.careers.openings.contact": "Don't see a position that fits your skills? Send your resume to", "public.careers.apply.title": "How to Apply", "public.careers.apply.description": "Please send your resume and cover letter to {email} with the position title in the subject line.", "// Careers Application Pages": "", "public.careers.application.title": "Apply for {job}", "public.careers.application.subtitle": "Join {company} and make an impact", "public.careers.application.description": "Submit your application for {job}", "public.careers.application.jobDetails": "Job Details", "public.careers.application.backToCareers": "Back to Careers", "public.careers.application.andMore": "and {count} more...", "public.careers.application.equalOpportunity": "We are an equal opportunity employer committed to diversity and inclusion.", "public.careers.application.notFound.title": "Job Not Found", "public.careers.application.notFound.message": "The job position you're looking for could not be found or may no longer be available.", "// Job Application Form": "", "public.careers.application.personal.title": "Personal Information", "public.careers.application.firstName": "First Name", "public.careers.application.lastName": "Last Name", "public.careers.application.email": "Email Address", "public.careers.application.phone": "Phone Number", "public.careers.application.location": "Current Location", "public.careers.application.location.placeholder": "City, State/Country", "public.careers.application.documents.title": "Documents & Portfolio", "public.careers.application.resume": "Resume/CV", "public.careers.application.resume.help": "Upload your resume in PDF, DOC, or DOCX format (max 5MB)", "public.careers.application.coverLetter": "Cover Letter", "public.careers.application.coverLetter.help": "Optional: Upload your cover letter in PDF, DOC, or DOCX format", "public.careers.application.portfolio": "Portfolio/Website", "public.careers.application.portfolio.help": "Optional: Link to your portfolio, GitHub, or personal website", "public.careers.application.experience.title": "Experience & Background", "public.careers.application.yearsExperience": "Years of Experience", "public.careers.application.currentRole": "Current Role/Position", "public.careers.application.currentRole.placeholder": "e.g., Senior Software Engineer", "public.careers.application.expectedSalary": "Expected <PERSON><PERSON>", "public.careers.application.expectedSalary.placeholder": "e.g., $80,000 - $100,000", "public.careers.application.additional.title": "Additional Information", "public.careers.application.additionalInfo": "Additional Comments", "public.careers.application.additionalInfo.placeholder": "Tell us anything else you'd like us to know about your application, availability, or questions about the role...", "public.careers.application.submit": "Submit Application", "public.careers.application.success.title": "Application Submitted Successfully!", "public.careers.application.success.message": "Thank you for your interest in joining our team. We have received your application and will review it carefully. You can expect to hear from us within 1-2 weeks.", "// Job Openings": "", "public.careers.openings.description": "Job Description", "// File Upload": "", "public.careers.application.file.choose": "Choose file", "public.careers.application.file.noFile": "No file chosen"}
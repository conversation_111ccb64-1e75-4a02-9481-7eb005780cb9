{"// Profile Page": "", "user.profile.title": "Profile", "user.profile.subtitle": "Manage your personal information and profile details", "user.profile.errors.failedToLoad": "Failed to load profile data", "user.profile.actions.tryAgain": "Try Again", "user.profile.actions.editProfile": "Edit Profile", "user.profile.actions.cancel": "Cancel", "user.profile.actions.saving": "Saving...", "user.profile.actions.saveChanges": "Save Changes", "user.profile.tooltips.editProfile": "Click to edit your profile information", "user.profile.tooltips.cancel": "Cancel editing and discard changes", "user.profile.tooltips.saveChanges": "Save your profile changes", "user.profile.tooltips.changeProfilePicture": "Click to change your profile picture", "user.profile.tooltips.verified": "Your account is verified", "user.profile.sections.personalInformation": "Personal Information", "user.profile.sections.personalInformationDescription": "Your basic profile information that others can see", "user.profile.completion.complete": "Complete", "user.profile.completion.title": "Profile Completion", "user.profile.completion.description": "Complete your profile to help others learn more about you. A complete profile includes all personal information fields.", "user.profile.memberSince": "Member since {date}", "user.profile.status.verified": "Verified", "user.profile.fields.firstName": "First Name", "user.profile.fields.lastName": "Last Name", "user.profile.fields.email": "Email", "user.profile.fields.phone": "Phone", "user.profile.fields.jobTitle": "Job Title", "user.profile.fields.company": "Company", "user.profile.fields.location": "Location", "user.profile.fields.bio": "Bio", "user.profile.placeholders.firstName": "Enter your first name", "user.profile.placeholders.lastName": "Enter your last name", "user.profile.placeholders.email": "Enter your email address", "user.profile.placeholders.phone": "Enter your phone number", "user.profile.placeholders.jobTitle": "Enter your job title", "user.profile.placeholders.company": "Enter your company name", "user.profile.placeholders.location": "Enter your location", "user.profile.placeholders.bio": "Tell us about yourself", "user.profile.labels.required": "Required", "user.profile.labels.set": "Set", "user.profile.labels.noPhone": "No phone number", "user.profile.labels.locationNotSet": "Location not set", "user.profile.labels.jobTitleNotSet": "Job title not set", "user.profile.labels.companyNotSet": "Company not set", "user.profile.labels.noBio": "No bio added yet", "user.profile.labels.memberSince": "Member since", "user.profile.labels.lastLogin": "Last login", "user.profile.labels.profileStatus": "Profile status", "user.profile.status.complete": "Complete", "user.profile.status.inProgress": "In Progress", "user.profile.sections.contactInformation": "Contact Information", "user.profile.sections.professionalDetails": "Professional Details", "user.profile.tooltips.jobTitle": "Add your current job title", "user.profile.tooltips.addJobTitle": "Click to add your job title", "user.profile.tooltips.currentRole": "Your current professional role", "user.profile.tooltips.addCompany": "Click to add your company", "user.profile.tooltips.currentWorkplace": "Your current workplace", "user.profile.tooltips.addLocation": "Click to add your location", "user.profile.tooltips.locationHelps": "Your location helps others connect with you", "user.profile.tooltips.completeContact": "Complete your contact information", "user.profile.tooltips.completeProfessional": "Complete your professional details", "user.profile.completion.remaining": "Add {count} more fields to complete your profile", "// Notifications Page": "", "user.notifications.title": "Notifications", "user.notifications.subtitle": "Stay updated with your account activity and system alerts", "user.notifications.tabs.all": "All", "user.notifications.tabs.unread": "Unread", "user.notifications.tabs.important": "Important", "user.notifications.preferences.description": "Choose how you want to be notified about account activity", "user.notifications.preferences.email": "Email Notifications", "user.notifications.preferences.sms": "SMS Notifications", "user.notifications.preferences.push": "Push Notifications", "user.notifications.preferences.security": "Security Alerts", "user.notifications.preferences.marketing": "Marketing Updates", "user.notifications.analytics.engagementRate": "Engagement Rate", "// Settings Page": "", "user.settings.title": "Settings", "user.settings.subtitle": "Manage your account preferences and application settings", "user.settings.loading.failed": "Failed to load settings", "user.settings.tabs.appearance": "Appearance", "user.settings.tabs.language": "Language", "user.settings.tabs.integrations": "Integrations", "user.settings.appearance.title": "Appearance Settings", "user.settings.appearance.description": "Customize the look and feel of your interface", "user.settings.appearance.theme": "Theme", "user.settings.appearance.theme.light": "Light", "user.settings.appearance.theme.dark": "Dark", "user.settings.appearance.theme.system": "System", "user.settings.language.title": "Language & Region", "user.settings.language.description": "Set your preferred language and regional settings", "user.settings.language.interface": "Interface Language", "user.settings.language.timezone": "Timezone", "user.settings.integrations.title": "Connected Services", "user.settings.integrations.description": "Manage your connected accounts and integrations", "user.settings.integrations.google.description": "Professional profile synchronization", "user.settings.integrations.slack.description": "Team communication and notifications", "user.settings.integrations.github.description": "Code repository and project management", "user.settings.notifications.saved": "Setting<PERSON> saved successfully", "user.settings.notifications.failed": "Failed to save settings", "user.settings.saveTooltip": "Save all your settings changes", "user.settings.overview.title": "Settings Overview", "user.settings.overview.description": "Summary of your current configuration", "user.settings.language.selectLanguage": "Select a language", "user.settings.language.selectTimezone": "Select your timezone", "user.settings.appearance.theme.system.description": "Automatically match your system's theme preference", "user.settings.appearance.theme.light.description": "Clean and bright interface optimized for daylight", "user.settings.appearance.theme.dark.description": "Easy on the eyes in low light environments", "user.settings.integrations.linkedin.description": "Professional profile synchronization", "user.settings.integrations.connected": "Connected", "user.settings.integrations.connectedTooltip": "Successfully connected to {service}", "user.settings.integrations.connect": "Connect", "user.settings.integrations.disconnect": "Disconnect", "user.settings.integrations.connectedServices": "Connected Services", "user.settings.integrations.activeIntegrations": "Active Integrations", "user.settings.integrations.active": "Active", "user.settings.integrations.noConnected": "No connected services", "// Security Page": "", "user.security.title": "Security", "user.security.subtitle": "Manage your account security and privacy settings", "user.security.password.title": "Password", "user.security.password.description": "Manage your account password and security", "user.security.password.change": "Change Password", "user.security.password.lastChanged": "Last changed: {date}", "user.security.twoFactor.title": "Two-Factor Authentication", "user.security.twoFactor.description": "Add an extra layer of security to your account", "user.security.twoFactor.enable": "Enable 2FA", "user.security.twoFactor.disable": "Disable 2FA", "user.security.sessions.title": "Active Sessions", "user.security.sessions.description": "Manage devices that are currently signed in to your account", "user.security.sessions.current": "Current Session", "user.security.sessions.revoke": "Revoke", "user.security.sessions.revokeAll": "Revoke All Sessions", "user.security.loginHistory.title": "Login History", "user.security.loginHistory.description": "Recent login activity on your account", "user.security.notifications.saved": "Security settings updated successfully", "user.security.loading.failed": "Failed to load security data", "user.security.status.secure": "Secure", "user.security.tabs.password": "Password", "user.security.tabs.twoFactor": "Two-Factor", "user.security.tabs.sessions": "Sessions", "user.security.tabs.history": "History", "user.security.daysAgo": "days ago", "user.security.password.oldWarning": "It's been over 90 days since you last changed your password.", "user.security.password.updateRecommendation": "For better security, we recommend updating your password regularly.", "user.security.password.current": "Current Password", "user.security.password.new": "New Password", "user.security.password.confirm": "Confirm New Password", "user.security.password.requirements.description": "Password must be at least 8 characters long and include a mix of uppercase, lowercase, numbers, and special characters.", "user.security.twoFactor.recoveryCodes": "Recovery Codes", "user.security.twoFactor.recoveryCodesDescription": "Recovery codes can be used to access your account if you lose your device. You have {remaining} recovery codes remaining.", "user.security.twoFactor.viewCodes": "View Recovery Codes", "user.security.twoFactor.generateCodes": "Generate New Codes", "user.security.twoFactor.disableWarning.title": "Warning:", "user.security.twoFactor.disableWarning.description": "Disabling two-factor authentication will make your account less secure.", "user.security.twoFactor.recommendation.title": "Security Recommendation", "user.security.twoFactor.recommendation.description": "Two-factor authentication adds an extra layer of security to your account by requiring more than just a password to sign in.", "user.security.twoFactor.setupOptions": "Setup Options", "user.security.twoFactor.authenticatorApp": "Authenticator App", "user.security.twoFactor.authenticatorApp.description": "Use an application like Google Authenticator, Microsoft Authenticator, or Authy.", "user.security.twoFactor.sms": "Text Message (SMS)", "user.security.twoFactor.sms.description": "Receive a code via text message to verify your identity.", "user.security.sessions.ipAddress": "IP Address", "user.security.sessions.location": "Location", "user.security.sessions.lastActive": "Last active", "user.security.activity.time": "Time", "user.security.activity.device": "<PERSON><PERSON>", "user.security.activity.ip": "IP", "user.security.activity.location": "Location", "user.security.overview.title": "Security Overview", "user.security.overview.description": "Your account security status", "user.security.overview.passwordStrength": "Password Strength", "user.security.overview.strong": "Strong", "user.security.overview.devices": "devices", "user.security.overview.lastPasswordChange": "Last Password Change", "user.security.overview.securityScore": "Security Score", "user.security.overview.excellent": "Excellent", "user.security.overview.good": "Good", "user.security.overview.needsImprovement": "Needs Improvement", "user.notifications.actions.archive": "Archive", "user.notifications.actions.delete": "Delete", "user.notifications.actions.filter": "Filter", "user.notifications.actions.markAllRead": "<PERSON>", "user.notifications.actions.markAsRead": "<PERSON> <PERSON>", "user.notifications.actions.settings": "Settings", "user.notifications.analytics.quickStats": "Quick Stats", "user.notifications.analytics.readPercentage": "Read Rate", "user.notifications.empty.general": "No notifications to display", "user.notifications.empty.title": "No Notifications", "user.notifications.empty.unread": "No unread notifications", "user.notifications.filters.all": "All", "user.notifications.filters.allDescription": "Show all notifications", "user.notifications.filters.important": "Important", "user.notifications.filters.importantDescription": "Show important notifications only", "user.notifications.filters.title": "Filter Options", "user.notifications.filters.unread": "Unread", "user.notifications.filters.unreadDescription": "Show unread notifications only", "user.notifications.labels.critical": "Critical", "user.notifications.labels.highPriority": "High Priority", "user.notifications.labels.received": "Received", "user.notifications.labels.total": "Total", "user.notifications.labels.unread": "Unread", "user.notifications.preferences.contentTypes": "Content Types", "user.notifications.preferences.deliveryMethods": "Delivery Methods", "user.notifications.preferences.emailDescription": "Receive notifications via email", "user.notifications.preferences.marketingDescription": "Marketing updates and promotions", "user.notifications.preferences.pushDescription": "Browser push notifications", "user.notifications.preferences.securityDescription": "Security alerts and warnings", "user.notifications.preferences.smsDescription": "SMS text message notifications", "user.notifications.preferences.title": "Notification Preferences", "user.notifications.sections.recent": "Recent Notifications", "user.notifications.sections.recentDescription": "Your latest notifications and updates", "user.notifications.time.hoursAgo": "{hours} hours ago", "user.notifications.time.justNow": "Just now", "user.notifications.tooltips.emailNotifications": "Control email notification delivery", "user.notifications.tooltips.engagementRate": "How often you interact with notifications", "user.notifications.tooltips.highPriority": "This notification is marked as high priority", "user.notifications.tooltips.markAllRead": "Mark all {count} notifications as read", "user.notifications.tooltips.marketingCommunications": "Promotional content and updates", "user.notifications.tooltips.pushNotifications": "Browser notifications when app is closed", "user.notifications.tooltips.quickStats": "Overview of your notification activity", "user.notifications.tooltips.securityAlerts": "Important security-related notifications", "user.notifications.tooltips.settings": "Configure notification preferences", "user.notifications.tooltips.smsNotifications": "Text message alerts to your phone", "user.notifications.tooltips.totalNotifications": "Total notifications received", "user.notifications.tooltips.unreadCount": "You have {count} unread notifications", "user.notifications.tooltips.unreadNotifications": "Notifications you haven't read yet", "user.profile.sections.aboutYou": "About You", "user.profile.sections.accountInformation": "Account Information", "user.profile.summary.description": "Overview of your profile completion and key information", "user.profile.summary.title": "Profile Summary", "user.profile.tooltips.addPhone": "Add your phone number for better security", "user.profile.tooltips.bio": "Tell others about yourself and your interests", "user.profile.tooltips.company": "Where you currently work", "user.profile.tooltips.fieldRequired": "This field is required", "user.profile.tooltips.location": "Your current city or region", "user.profile.tooltips.phoneAvailable": "Phone number is available for contact", "user.profile.tooltips.phoneImproves": "Adding your phone improves account security"}
{"// Application": "", "// Navigation": "", "// Authentication": "", "// User": "", "// Common Actions": "", "actions.save": "Save", "actions.retry": "Retry", "actions.processing": "Processing...", "// Common UI Elements": "", "// Common States": "", "common.all": "All", "common.error": "Error", "common.enabled": "Enabled", "common.disabled": "Disabled", "// History & Prompts": "", "// Drawer": "", "// Validation": "", "// Error Messages": "", "// Notifications": "", "// System Pages": "", "// Error Pages": "", "error.404.title": "Page Not Found", "error.404.message": "The page you're looking for doesn't exist.", "error.404.check.title": "You might want to check if:", "error.404.check.url": "The URL was typed correctly", "error.404.check.moved": "The page might have been moved or deleted", "error.404.check.permissions": "You have the necessary permissions to access this page", "error.404.return.home": "Return Home", "error.404.contact.support": "Contact Support", "error.500.title": "Server Error", "error.500.technical.difficulties": "We're experiencing technical difficulties", "error.500.notification.message": "Our team has been notified of this issue and we're working to fix it as quickly as possible. Please try again later.", "error.500.return.home": "Return Home", "error.500.need.assistance": "Need immediate assistance?", "error.500.contact.support": "Contact Support", "maintenance.title": "We're Down for Maintenance", "maintenance.message": "We're currently performing scheduled maintenance on our servers. Please check back soon.", "maintenance.expected.back": "Expected to be back in:", "maintenance.time.hours": "Hours", "maintenance.time.minutes": "Minutes", "maintenance.time.seconds": "Seconds", "maintenance.why.title": "Why We're Down", "maintenance.why.message": "We're making improvements to our system to enhance your experience. This maintenance is planned and temporary.", "maintenance.assistance.title": "Need Assistance?", "maintenance.assistance.message": "If you need immediate assistance, please email us at", "maintenance.updates.title": "Stay Updated", "maintenance.updates.message": "Follow us on social media for updates:", "maintenance.rights.reserved": "All rights reserved.", "// Blog": "", "blog.featured.image": "Featured Image", "blog.article.image": "Article Image", "// Common Form Elements": "", "common.back": "Back", "common.cancel": "Cancel", "common.submitting": "Submitting...", "// Form Validation": "", "form.validation.required": "This field is required", "form.validation.email.invalid": "Please enter a valid email address", "form.validation.number.positive": "Must be a positive number", "form.error.submission": "There was an error submitting the form. Please try again.", "common.created": "Created", "common.updated": "Updated", "common.back.to.resources": "Back to Resources", "// Detail Pages": "", "detail.last.updated": "Last updated", "detail.reading.time": "Reading time", "// Related Content": "", "related.reading.time": "Reading time", "related.view.all": "View all", "// Wizard": "", "wizard.cancel": "Cancel", "wizard.next": "Next", "wizard.optional": "Optional", "wizard.previous": "Previous", "wizard.step.progress": "Step {current} of {total}", "wizard.submit": "Submit", "navigation.help": "Help", "navigation.support": "Support", "// Missing Common Translations": "", "common.readMore": "Read More", "common.filters": "Filters", "common.clearFilters": "Clear Filters", "common.share": "Share"}
{"// Careers Page": "", "public.careers.why.title": "为什么与我们一起工作", "public.careers.why.meaningful.title": "有意义的工作", "public.careers.why.meaningful.description": "加入一个产生真正影响的团队。您的工作将直接有助于解决客户和行业的重要问题。", "public.careers.why.growth.title": "成长机会", "public.careers.why.growth.description": "我们通过学习资源、指导和清晰的职业发展路径投资于团队的专业发展。", "public.careers.why.culture.title": "包容性文化", "public.careers.why.culture.description": "我们培养一个多元化和包容性的环境，让每个人都能茁壮成长，贡献他们独特的视角，并感到受到重视。", "public.careers.benefits.title": "福利与特权", "public.careers.benefits.health.title": "健康与福祉", "public.careers.benefits.health.insurance": "全面的健康保险", "public.careers.benefits.health.mental": "心理健康资源", "public.careers.benefits.health.wellness": "健康津贴", "public.careers.benefits.work.title": "工作生活平衡", "public.careers.benefits.work.flexible": "灵活工作时间", "public.careers.benefits.work.remote": "远程工作选项", "public.careers.benefits.work.pto": "慷慨的带薪休假", "public.careers.benefits.financial.title": "财务福利", "public.careers.benefits.financial.salary": "有竞争力的薪资", "public.careers.benefits.financial.equity": "股权薪酬", "public.careers.benefits.financial.401k": "401(k)匹配", "public.careers.benefits.development.title": "专业发展", "public.careers.benefits.development.stipend": "学习津贴", "public.careers.benefits.development.conference": "参加会议", "public.careers.benefits.development.mentorship": "导师计划", "public.careers.benefits.flexible": "灵活的工作安排", "public.careers.benefits.health": "全面的健康福利", "public.careers.benefits.learning": "持续学习机会", "public.careers.benefits.vacation": "慷慨的休假政策", "public.careers.benefits.culture": "包容和多元化的文化", "public.careers.openings.title": "当前职位", "public.careers.openings.responsibilities": "职责：", "public.careers.openings.requirements": "要求：", "public.careers.openings.apply": "立即申请", "public.careers.openings.none": "该部门目前没有职位空缺。", "public.careers.openings.contact": "没有符合您技能的职位？请将您的简历发送至", "public.careers.apply.title": "如何申请", "public.careers.apply.description": "请将您的简历和求职信发送至{email}，邮件主题请注明职位名称。", "// Careers Application Pages": "", "public.careers.application.title": "申请 {job}", "public.careers.application.subtitle": "加入 {company}，创造影响力", "public.careers.application.description": "提交您的 {job} 申请", "public.careers.application.jobDetails": "职位详情", "public.careers.application.backToCareers": "返回招聘页面", "public.careers.application.andMore": "还有 {count} 项...", "public.careers.application.equalOpportunity": "我们是一个致力于多元化和包容性的平等机会雇主。", "public.careers.application.notFound.title": "职位未找到", "public.careers.application.notFound.message": "您要查找的职位可能不存在或已不再开放。", "// Job Application Form": "", "public.careers.application.personal.title": "个人信息", "public.careers.application.firstName": "名", "public.careers.application.lastName": "姓", "public.careers.application.email": "电子邮箱", "public.careers.application.phone": "电话号码", "public.careers.application.location": "当前位置", "public.careers.application.location.placeholder": "城市，省份/国家", "public.careers.application.documents.title": "文档和作品集", "public.careers.application.resume": "简历", "public.careers.application.resume.help": "上传PDF、DOC或DOCX格式的简历（最大5MB）", "public.careers.application.coverLetter": "求职信", "public.careers.application.coverLetter.help": "可选：上传PDF、DOC或DOCX格式的求职信", "public.careers.application.portfolio": "作品集/网站", "public.careers.application.portfolio.help": "可选：您的作品集、GitHub或个人网站链接", "public.careers.application.experience.title": "经验和背景", "public.careers.application.yearsExperience": "工作年限", "public.careers.application.currentRole": "当前职位", "public.careers.application.currentRole.placeholder": "例如：高级软件工程师", "public.careers.application.expectedSalary": "期望薪资", "public.careers.application.expectedSalary.placeholder": "例如：80,000 - 100,000元", "public.careers.application.additional.title": "附加信息", "public.careers.application.additionalInfo": "其他说明", "public.careers.application.additionalInfo.placeholder": "请告诉我们您希望我们了解的其他信息，如可入职时间或对该职位的疑问...", "public.careers.application.submit": "提交申请", "public.careers.application.success.title": "申请提交成功！", "public.careers.application.success.message": "感谢您有兴趣加入我们的团队。我们已收到您的申请并将仔细审核。您可以期待在1-2周内收到我们的回复。", "// Job Openings": "", "public.careers.openings.description": "职位描述", "// File Upload": "", "public.careers.application.file.choose": "选择文件", "public.careers.application.file.noFile": "未选择文件"}
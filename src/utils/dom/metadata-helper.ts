/**
 * DOM Utilities
 *
 * This module provides utility functions for DOM manipulation,
 * metadata management, and document operations
 */

/**
 * Sets or updates a meta tag in the document head
 *
 * @param name - The name or property attribute of the meta tag
 * @param content - The content value of the meta tag
 * @param isProperty - Whether to use property attribute instead of name (for Open Graph)
 */
function setMetaTag(
  name: string,
  content: string,
  isProperty: boolean = false
): void {
  const attributeName = isProperty ? 'property' : 'name';
  const existingMeta = document.querySelector(
    `meta[${attributeName}="${name}"]`
  );

  if (existingMeta) {
    existingMeta.setAttribute('content', content);
  } else {
    const metaTag = document.createElement('meta');
    metaTag.setAttribute(attributeName, name);
    metaTag.setAttribute('content', content);
    document.head.appendChild(metaTag);
  }
}

/**
 * Updates the page metadata using the provided metadata object
 *
 * @param metadata - The metadata object containing title, description, keywords, and ogImage
 */
export function updatePageMetadata(metadata: {
  title: string;
  description: string;
  keywords: string[];
  ogImage: string;
}): void {
  // Set document title
  document.title = metadata.title;

  // Set meta description
  setMetaTag('description', metadata.description);

  // Set meta keywords
  setMetaTag('keywords', metadata.keywords.join(', '));

  // Set Open Graph tags
  setMetaTag('og:title', metadata.title, true);
  setMetaTag('og:description', metadata.description, true);
  setMetaTag('og:image', metadata.ogImage, true);
  setMetaTag('og:type', 'website', true);

  // Set Twitter card tags
  setMetaTag('twitter:card', 'summary_large_image', true);
  setMetaTag('twitter:title', metadata.title, true);
  setMetaTag('twitter:description', metadata.description, true);
  setMetaTag('twitter:image', metadata.ogImage, true);
}

/**
 * Set the document title
 *
 * @param title - The title to set
 */
export function setDocumentTitle(title: string): void {
  document.title = title;
}

/**
 * Get the current document title
 *
 * @returns Current document title
 */
export function getDocumentTitle(): string {
  return document.title;
}

/**
 * Add a CSS class to the document body
 *
 * @param className - CSS class name to add
 */
export function addBodyClass(className: string): void {
  document.body.classList.add(className);
}

/**
 * Remove a CSS class from the document body
 *
 * @param className - CSS class name to remove
 */
export function removeBodyClass(className: string): void {
  document.body.classList.remove(className);
}

/**
 * Toggle a CSS class on the document body
 *
 * @param className - CSS class name to toggle
 * @param force - Optional boolean to force add (true) or remove (false)
 */
export function toggleBodyClass(className: string, force?: boolean): void {
  document.body.classList.toggle(className, force);
}

// ============================================================================
// ELEMENT UTILITIES
// ============================================================================

/**
 * Get element by ID with type safety
 *
 * @param id - Element ID
 * @returns HTMLElement or null
 */
export const getElementById = <T extends HTMLElement = HTMLElement>(
  id: string
): T | null => {
  return document.getElementById(id) as T | null;
};

/**
 * Create element with attributes
 *
 * @param tagName - HTML tag name
 * @param attributes - Object containing element attributes
 * @param textContent - Text content for the element
 * @returns Created HTML element
 */
export const createElement = (
  tagName: string,
  attributes: Record<string, string> = {},
  textContent?: string
): HTMLElement => {
  const element = document.createElement(tagName);

  Object.entries(attributes).forEach(([key, value]) => {
    element.setAttribute(key, value);
  });

  if (textContent) {
    element.textContent = textContent;
  }

  return element;
};

/**
 * Check if element is in viewport
 *
 * @param element - HTML element to check
 * @param threshold - Intersection threshold (0-1)
 * @returns True if element is in viewport
 */
export const isInViewport = (
  element: HTMLElement,
  threshold: number = 0
): boolean => {
  const rect = element.getBoundingClientRect();
  const windowHeight =
    window.innerHeight || document.documentElement.clientHeight;
  const windowWidth = window.innerWidth || document.documentElement.clientWidth;

  const verticalVisible =
    rect.top + rect.height * threshold < windowHeight &&
    rect.bottom - rect.height * threshold > 0;
  const horizontalVisible =
    rect.left + rect.width * threshold < windowWidth &&
    rect.right - rect.width * threshold > 0;

  return verticalVisible && horizontalVisible;
};

/**
 * Smooth scroll to element
 *
 * @param element - Target element or selector
 * @param options - Scroll behavior options
 */
export const scrollToElement = (
  element: HTMLElement | string,
  options: {
    behavior?: 'auto' | 'smooth';
    block?: 'start' | 'center' | 'end' | 'nearest';
  } = { behavior: 'smooth', block: 'start' }
): void => {
  const targetElement =
    typeof element === 'string'
      ? (document.querySelector(element) as HTMLElement)
      : element;

  if (targetElement) {
    targetElement.scrollIntoView(options);
  }
};

/**
 * Add event listener with cleanup
 *
 * @param element - Target element
 * @param event - Event type
 * @param handler - Event handler function
 * @param options - Event listener options
 * @returns Cleanup function
 */
export const addEventListenerWithCleanup = (
  element: HTMLElement,
  event: string,
  handler: () => void,
  options?: boolean | { capture?: boolean; once?: boolean; passive?: boolean }
): (() => void) => {
  element.addEventListener(event, handler, options);
  return () => element.removeEventListener(event, handler, options);
};

/**
 * Get computed style property value
 *
 * @param element - HTML element
 * @param property - CSS property name
 * @returns Computed style value
 */
export const getComputedStyleProperty = (
  element: HTMLElement,
  property: string
): string => {
  return window.getComputedStyle(element).getPropertyValue(property);
};

/**
 * Check if element has class
 *
 * @param element - HTML element
 * @param className - Class name to check
 * @returns True if element has the class
 */
export const hasClass = (element: HTMLElement, className: string): boolean => {
  return element.classList.contains(className);
};

/**
 * Toggle class on element
 *
 * @param element - HTML element
 * @param className - Class name to toggle
 * @param force - Force add (true) or remove (false)
 * @returns True if class was added, false if removed
 */
export const toggleClass = (
  element: HTMLElement,
  className: string,
  force?: boolean
): boolean => {
  return element.classList.toggle(className, force);
};

// Export setMetaTag for backward compatibility
export { setMetaTag };

/**
 * Default export with all utility functions for backward compatibility
 */
const metadataHelper = {
  setMetaTag,
  updatePageMetadata,
  setDocumentTitle,
  getDocumentTitle,
  addBodyClass,
  removeBodyClass,
  toggleBodyClass,
};

export default metadataHelper;

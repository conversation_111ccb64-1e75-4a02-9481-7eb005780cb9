/**
 * Utils Barrel Exports
 *
 * This file serves as the main entry point for all utility functions.
 * It provides a clean interface for importing utilities throughout the application.
 */

// API utilities
export * from './api/axios';

// Array utilities (explicit exports to avoid conflicts)
export {
  removeDuplicates,
  removeDuplicatesByKey,
  chunk,
  flatten as flattenArray,
  shuffle,
  randomElement,
  randomElements,
  groupBy,
  sortBy,
  intersection,
  difference,
  union,
  isEmpty as isArrayEmpty,
  last,
  first,
  range,
  partition,
} from './array/array-utils';

// Authentication utilities
export * from './auth/auth-utils';

// Browser utilities (explicit exports to avoid conflicts)
export {
  useWindowSize,
  useIsMobile,
  useIsTablet,
  useIsDesktop,
  getDeviceType,
  getViewportSize,
  isBrowser,
  getUserAgent,
  isMobileDevice,
  isIOS,
  isAndroid,
  getScrollPosition,
  scrollToTop,
  scrollToElement as browserScrollToElement,
  copyToClipboard,
  downloadFile,
  prefersReducedMotion,
  getBrowserInfo,
} from './browser/browser-utils';

// Canvas utilities
export * from './canvas/canvas-utils';

// Date/Time utilities
export * from './date-time/date-time-utils';

// DOM utilities (explicit exports to avoid conflicts)
export {
  updatePageMetadata,
  setDocumentTitle,
  getDocumentTitle,
  addBodyClass,
  removeBodyClass,
  toggleBodyClass,
  getElementById,
  createElement,
  isInViewport,
  scrollToElement as domScrollToElement,
  addEventListenerWithCleanup,
  getComputedStyleProperty,
  hasClass,
  toggleClass,
  setMetaTag,
} from './dom/metadata-helper';
export { default as metadataHelper } from './dom/metadata-helper';

// File utilities (explicit exports to avoid conflicts)
export {
  getFileExtension as getFileExt,
  getFilenameWithoutExtension,
  formatFileSize,
  isImageFile,
  isVideoFile,
  isAudioFile,
  isDocumentFile,
  isCodeFile,
  getMimeType,
  isAllowedFileType,
  isValidFileSize,
  fileToBase64,
  base64ToBlob,
  downloadBlob,
  readFileAsText,
} from './file/file-utils';

// Number utilities
export * from './number/number-utils';

// Object utilities (explicit exports to avoid conflicts)
export {
  deepClone,
  deepMerge,
  isObject,
  isEmpty as isObjectEmpty,
  deepEqual,
  isEqual,
  getNestedValue,
  get,
  setNestedValue,
  set,
  removeNestedValue,
  unset,
  hasNestedProperty,
  has,
  pick,
  omit,
  getPropertyPaths,
  getAllKeys,
  flatten as flattenObject,
  unflatten,
} from './object/object-utils';

// String utilities
export * from './string/string-utils';

// URL utilities (recently updated)
export * from './url/url-utils';

// Validation utilities (recently added/updated)
export * from './validation/password-strength';
export * from './validation/validation-utils';

// Component preloading utilities - removed over-engineered preloader
// export * from './component-preloader'; // Removed

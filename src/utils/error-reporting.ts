/**
 * Error Reporting Utilities
 *
 * Centralized error reporting and logging system for production applications
 */

import { ErrorInfo } from 'react';

interface ErrorReport {
  id: string;
  message: string;
  stack?: string;
  url: string;
  userAgent: string;
  timestamp: string;
  userId?: string;
  sessionId?: string;
  apiEndpoint?: string;
  requestId?: string;
  level: 'error' | 'warning' | 'info';
  tags?: Record<string, string>;
  extra?: Record<string, any>;
}

export interface ErrorContext {
  userId?: string;
  sessionId?: string;
  apiEndpoint?: string;
  requestId?: string;
  tags?: Record<string, string>;
  extra?: Record<string, any>;
}

/**
 * Error Reporter Class
 * Handles error collection, processing, and reporting to external services
 */
export class ErrorReporter {
  private static instance: ErrorReporter;
  private static isEnabled = import.meta.env.PROD;
  private static queue: ErrorReport[] = [];
  private static maxQueueSize = 100;

  static getInstance(): ErrorReporter {
    if (!ErrorReporter.instance) {
      ErrorReporter.instance = new ErrorReporter();
    }
    return ErrorReporter.instance;
  }

  /**
   * Report an error with context
   */
  static report(
    error: Error,
    level: 'error' | 'warning' | 'info' = 'error',
    context: ErrorContext = {}
  ): void {
    const report = ErrorReporter.createReport(error, level, context);

    // Always log to console in development
    if (import.meta.env.DEV) {
      ErrorReporter.logToConsole(report);
    }

    // Queue for reporting in production
    if (ErrorReporter.isEnabled) {
      ErrorReporter.addToQueue(report);
      ErrorReporter.processQueue();
    }
  }

  /**
   * Report API-specific errors
   */
  static reportApiError(
    error: Error,
    endpoint: string,
    requestId?: string,
    context: ErrorContext = {}
  ): void {
    ErrorReporter.report(error, 'error', {
      ...context,
      apiEndpoint: endpoint,
      requestId,
      tags: {
        ...context.tags,
        errorType: 'api',
        endpoint,
      },
    });
  }

  /**
   * Report React component errors
   */
  static reportComponentError(
    error: Error,
    componentStack: string,
    context: ErrorContext = {}
  ): void {
    ErrorReporter.report(error, 'error', {
      ...context,
      extra: {
        ...context.extra,
        componentStack,
      },
      tags: {
        ...context.tags,
        errorType: 'component',
      },
    });
  }

  /**
   * Report performance issues
   */
  static reportPerformanceIssue(
    operation: string,
    duration: number,
    threshold: number,
    context: ErrorContext = {}
  ): void {
    const error = new Error(
      `Slow operation: ${operation} took ${duration}ms (threshold: ${threshold}ms)`
    );
    ErrorReporter.report(error, 'warning', {
      ...context,
      extra: {
        ...context.extra,
        operation,
        duration,
        threshold,
      },
      tags: {
        ...context.tags,
        errorType: 'performance',
      },
    });
  }

  private static createReport(
    error: Error,
    level: 'error' | 'warning' | 'info',
    context: ErrorContext
  ): ErrorReport {
    return {
      id: ErrorReporter.generateId(),
      message: error.message,
      stack: error.stack,
      url: window.location.href,
      userAgent: navigator.userAgent,
      timestamp: new Date().toISOString(),
      level,
      ...context,
    };
  }

  private static generateId(): string {
    return `error_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  private static logToConsole(report: ErrorReport): void {
    const logMethod =
      report.level === 'error'
        ? console.error
        : report.level === 'warning'
          ? console.warn
          : console.log;

    logMethod('Error Report:', {
      id: report.id,
      message: report.message,
      level: report.level,
      url: report.url,
      timestamp: report.timestamp,
      ...(report.tags && { tags: report.tags }),
      ...(report.extra && { extra: report.extra }),
      ...(report.stack && { stack: report.stack }),
    });
  }

  private static addToQueue(report: ErrorReport): void {
    ErrorReporter.queue.push(report);

    // Prevent queue from growing too large
    if (ErrorReporter.queue.length > ErrorReporter.maxQueueSize) {
      ErrorReporter.queue.shift();
    }
  }

  private static async processQueue(): Promise<void> {
    if (ErrorReporter.queue.length === 0) return;

    const reports = [...ErrorReporter.queue];
    ErrorReporter.queue = [];

    try {
      await ErrorReporter.sendReports(reports);
    } catch (error) {
      console.error('Failed to send error reports:', error);
      // Re-queue failed reports (up to max size)
      ErrorReporter.queue.unshift(
        ...reports.slice(0, ErrorReporter.maxQueueSize)
      );
    }
  }

  private static async sendReports(reports: ErrorReport[]): Promise<void> {
    // Example implementations for different services:

    // 1. Send to your own API
    await ErrorReporter.sendToApi(reports);

    // 2. Send to Sentry (if configured)
    if (import.meta.env.VITE_SENTRY_DSN) {
      await ErrorReporter.sendToSentry(reports);
    }

    // 3. Send to other services as needed
    // await ErrorReporter.sendToLogRocket(reports);
    // await ErrorReporter.sendToDatadog(reports);
  }

  private static async sendToApi(reports: ErrorReport[]): Promise<void> {
    try {
      const response = await fetch('/api/errors', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ reports }),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
    } catch (error) {
      console.error('Failed to send reports to API:', error);
      throw error;
    }
  }

  private static async sendToSentry(reports: ErrorReport[]): Promise<void> {
    // Example Sentry integration
    // This would require @sentry/browser to be installed
    try {
      // Check if Sentry is available in the global scope first
      if (typeof window !== 'undefined' && (window as any).Sentry) {
        const Sentry = (window as any).Sentry;
        ErrorReporter.processSentryReports(Sentry, reports);
        return;
      }

      // Dynamic import is commented out to avoid build errors when @sentry/browser is not installed
      // Uncomment and install @sentry/browser if you want to use Sentry
      // try {
      //   const sentryModule = await import('@sentry/browser');
      //   ErrorReporter.processSentryReports(sentryModule, reports);
      // } catch (importError) {
      //   console.warn('Sentry not available, skipping Sentry reporting');
      // }

      console.warn('Sentry not configured, skipping Sentry reporting');
    } catch (error) {
      console.error('Failed to send reports to Sentry:', error);
      // Don't throw error for optional Sentry integration
    }
  }

  private static processSentryReports(
    Sentry: any,
    reports: ErrorReport[]
  ): void {
    reports.forEach(report => {
      Sentry.withScope((scope: any) => {
        scope.setLevel(report.level as any);
        scope.setTag('errorId', report.id);
        scope.setContext('errorReport', {
          url: report.url,
          userAgent: report.userAgent,
          timestamp: report.timestamp,
          ...(report.apiEndpoint && { apiEndpoint: report.apiEndpoint }),
          ...(report.requestId && { requestId: report.requestId }),
        });

        if (report.tags) {
          Object.entries(report.tags).forEach(([key, value]) => {
            scope.setTag(key, value);
          });
        }

        if (report.extra) {
          Object.entries(report.extra).forEach(([key, value]) => {
            scope.setExtra(key, value);
          });
        }

        if (report.userId) {
          scope.setUser({ id: report.userId });
        }

        const error = new Error(report.message);
        if (report.stack) {
          error.stack = report.stack;
        }

        Sentry.captureException(error);
      });
    });
  }

  /**
   * Set global error context (e.g., user ID, session ID)
   */
  static setGlobalContext(context: Partial<ErrorContext>): void {
    ErrorReporter.globalContext = {
      ...ErrorReporter.globalContext,
      ...context,
    };
  }

  /**
   * Clear global error context
   */
  static clearGlobalContext(): void {
    ErrorReporter.globalContext = {};
  }

  private static globalContext: ErrorContext = {};

  /**
   * Get current global context
   */
  static getGlobalContext(): ErrorContext {
    return { ...ErrorReporter.globalContext };
  }
}

/**
 * Convenience functions for common error reporting scenarios
 */

export const reportError = (error: Error, context?: ErrorContext) =>
  ErrorReporter.report(error, 'error', context);

export const reportWarning = (error: Error, context?: ErrorContext) =>
  ErrorReporter.report(error, 'warning', context);

export const reportApiError = (
  error: Error,
  endpoint: string,
  requestId?: string,
  context?: ErrorContext
) => ErrorReporter.reportApiError(error, endpoint, requestId, context);

export const reportComponentError = (
  error: Error,
  componentStack: string,
  context?: ErrorContext
) => ErrorReporter.reportComponentError(error, componentStack, context);

export const reportPerformanceIssue = (
  operation: string,
  duration: number,
  threshold: number,
  context?: ErrorContext
) =>
  ErrorReporter.reportPerformanceIssue(operation, duration, threshold, context);

export const setErrorContext = (context: Partial<ErrorContext>) =>
  ErrorReporter.setGlobalContext(context);

export const clearErrorContext = () => ErrorReporter.clearGlobalContext();

/**
 * Error boundary integration helpers
 */
export const handleErrorBoundary = (error: Error, errorInfo: ErrorInfo) => {
  ErrorReporter.reportComponentError(
    error,
    errorInfo.componentStack || 'Unknown component stack'
  );
};

/**
 * Specialized error boundary reporting functions
 */
export const reportApiErrorBoundary = (
  error: Error,
  errorInfo: ErrorInfo,
  context?: ErrorContext
) => {
  ErrorReporter.reportComponentError(
    error,
    errorInfo.componentStack || 'Unknown component stack',
    {
      ...context,
      tags: {
        ...context?.tags,
        boundaryType: 'api',
        errorType: 'boundary',
      },
    }
  );
};

export const reportAsyncErrorBoundary = (
  error: Error,
  errorInfo: ErrorInfo,
  context?: ErrorContext
) => {
  ErrorReporter.reportComponentError(
    error,
    errorInfo.componentStack || 'Unknown component stack',
    {
      ...context,
      tags: {
        ...context?.tags,
        boundaryType: 'async',
        errorType: 'boundary',
      },
    }
  );
};

export const reportFormErrorBoundary = (
  error: Error,
  errorInfo: ErrorInfo,
  context?: ErrorContext
) => {
  ErrorReporter.reportComponentError(
    error,
    errorInfo.componentStack || 'Unknown component stack',
    {
      ...context,
      tags: {
        ...context?.tags,
        boundaryType: 'form',
        errorType: 'boundary',
      },
    }
  );
};

export const reportDataErrorBoundary = (
  error: Error,
  errorInfo: ErrorInfo,
  context?: ErrorContext
) => {
  ErrorReporter.reportComponentError(
    error,
    errorInfo.componentStack || 'Unknown component stack',
    {
      ...context,
      tags: {
        ...context?.tags,
        boundaryType: 'data',
        errorType: 'boundary',
      },
    }
  );
};

export const reportRouteErrorBoundary = (
  error: Error,
  errorInfo: ErrorInfo,
  context?: ErrorContext
) => {
  ErrorReporter.reportComponentError(
    error,
    errorInfo.componentStack || 'Unknown component stack',
    {
      ...context,
      tags: {
        ...context?.tags,
        boundaryType: 'route',
        errorType: 'boundary',
      },
    }
  );
};

export default ErrorReporter;

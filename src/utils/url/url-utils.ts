/**
 * URL Utilities
 *
 * This module provides utility functions for URL manipulation,
 * query parameter handling, and path operations
 */

// ================================
// SEARCH PARAMETERS UTILITIES
// ================================

/**
 * Parse URL search parameters into an object
 *
 * @param search - URL search string (e.g., "?foo=bar&baz=qux")
 * @returns Object with parsed parameters
 */
export const parseSearchParams = (search: string): Record<string, string> => {
  const params: Record<string, string> = {};

  if (!search) return params;

  const searchParams = new URLSearchParams(search);

  for (const [key, value] of searchParams.entries()) {
    params[key] = value;
  }

  return params;
};

/**
 * Parse URL parameters into an object
 *
 * @param url - URL to parse (optional, uses current URL if not provided)
 * @returns Object with URL parameters
 */
export const parseURLParams = (url?: string): Record<string, string> => {
  const urlToParse =
    url || (typeof window !== 'undefined' ? window.location.href : '');
  const urlObj = new URL(urlToParse);
  const params: Record<string, string> = {};

  urlObj.searchParams.forEach((value, key) => {
    params[key] = value;
  });

  return params;
};

/**
 * Build URL search string from object
 *
 * @param params - Object with parameters
 * @returns URL search string
 */
export const buildSearchParams = (
  params: Record<string, string | number | boolean | string[]>
): string => {
  const searchParams = new URLSearchParams();

  Object.entries(params).forEach(([key, value]) => {
    if (value !== null && value !== undefined && value !== '') {
      if (Array.isArray(value)) {
        value.forEach(item => searchParams.append(key, String(item)));
      } else {
        searchParams.set(key, String(value));
      }
    }
  });

  const search = searchParams.toString();
  return search ? `?${search}` : '';
};

/**
 * Build URL with parameters
 *
 * @param baseUrl - Base URL
 * @param params - Parameters to add
 * @returns URL with parameters
 */
export const buildURL = (
  baseUrl: string,
  params: Record<string, string | number | boolean>
): string => {
  const url = new URL(baseUrl);

  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      url.searchParams.set(key, String(value));
    }
  });

  return url.toString();
};

/**
 * Build URL with base and parameters
 *
 * @param base - Base URL
 * @param params - Query parameters
 * @returns Complete URL
 */
export const buildUrl = (
  base: string,
  params?: Record<string, string | number | boolean | string[]>
): string => {
  if (!params || Object.keys(params).length === 0) {
    return base;
  }

  const searchParams = buildSearchParams(params);
  return base + searchParams;
};

// ================================
// QUERY PARAMETER MANIPULATION
// ================================

/**
 * Add or update query parameters in URL
 *
 * @param url - Base URL
 * @param params - Parameters to add/update
 * @returns Updated URL
 */
export const addQueryParams = (
  url: string,
  params: Record<string, string | number | boolean | string[]>
): string => {
  try {
    const urlObj = new URL(url);

    Object.entries(params).forEach(([key, value]) => {
      if (value !== null && value !== undefined && value !== '') {
        if (Array.isArray(value)) {
          urlObj.searchParams.delete(key);
          value.forEach(item => urlObj.searchParams.append(key, String(item)));
        } else {
          urlObj.searchParams.set(key, String(value));
        }
      } else {
        urlObj.searchParams.delete(key);
      }
    });

    return urlObj.toString();
  } catch (error) {
    console.error('Invalid URL:', error);
    return url;
  }
};

/**
 * Get parameter value from URL
 *
 * @param paramName - Parameter name
 * @param url - URL to parse (optional, uses current URL if not provided)
 * @returns Parameter value or null if not found
 */
export const getURLParam = (paramName: string, url?: string): string | null => {
  const urlToParse =
    url || (typeof window !== 'undefined' ? window.location.href : '');
  const urlObj = new URL(urlToParse);
  return urlObj.searchParams.get(paramName);
};

/**
 * Get specific query parameter value from URL
 *
 * @param url - URL to parse
 * @param paramName - Parameter name to get
 * @returns Parameter value or null
 */
export const getQueryParam = (
  url: string,
  paramName: string
): string | null => {
  try {
    const urlObj = new URL(url);
    return urlObj.searchParams.get(paramName);
  } catch (error) {
    console.error('Invalid URL:', error);
    return null;
  }
};

/**
 * Remove parameter from URL
 *
 * @param paramName - Parameter name to remove
 * @param url - URL to modify (optional, uses current URL if not provided)
 * @returns URL without the parameter
 */
export const removeURLParam = (paramName: string, url?: string): string => {
  const urlToParse =
    url || (typeof window !== 'undefined' ? window.location.href : '');
  const urlObj = new URL(urlToParse);
  urlObj.searchParams.delete(paramName);
  return urlObj.toString();
};

/**
 * Remove query parameters from URL
 *
 * @param url - URL to modify
 * @param paramNames - Parameter names to remove
 * @returns URL without specified parameters
 */
export const removeQueryParams = (
  url: string,
  paramNames: string[]
): string => {
  try {
    const urlObj = new URL(url);

    paramNames.forEach(paramName => {
      urlObj.searchParams.delete(paramName);
    });

    return urlObj.toString();
  } catch (error) {
    console.error('Invalid URL:', error);
    return url;
  }
};

/**
 * Update URL parameter
 *
 * @param paramName - Parameter name
 * @param value - New value
 * @param url - URL to modify (optional, uses current URL if not provided)
 * @returns URL with updated parameter
 */
export const updateURLParam = (
  paramName: string,
  value: string,
  url?: string
): string => {
  const urlToParse =
    url || (typeof window !== 'undefined' ? window.location.href : '');
  const urlObj = new URL(urlToParse);
  urlObj.searchParams.set(paramName, value);
  return urlObj.toString();
};

// ================================
// URL VALIDATION AND TYPE CHECKING
// ================================

/**
 * Check if URL is absolute
 *
 * @param url - URL to check
 * @returns True if URL is absolute
 */
export const isAbsoluteURL = (url: string): boolean => {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
};

/**
 * Check if URL is absolute (alternative naming)
 *
 * @param url - URL to check
 * @returns True if URL is absolute
 */
export const isAbsoluteUrl = (url: string): boolean => {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
};

/**
 * Check if URL is relative
 *
 * @param url - URL to check
 * @returns True if URL is relative
 */
export const isRelativeURL = (url: string): boolean => {
  return !isAbsoluteURL(url);
};

/**
 * Check if URL is relative (alternative naming)
 *
 * @param url - URL to check
 * @returns True if URL is relative
 */
export const isRelativeUrl = (url: string): boolean => {
  return !isAbsoluteUrl(url);
};

/**
 * Validate URL format
 *
 * @param url - URL to validate
 * @returns True if URL is valid
 */
export const isValidUrl = (url: string): boolean => {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
};

/**
 * Check if URL is HTTPS
 *
 * @param url - URL to check
 * @returns True if URL uses HTTPS
 */
export const isHttps = (url: string): boolean => {
  return getProtocol(url) === 'https:';
};

// ================================
// URL COMPONENTS EXTRACTION
// ================================

/**
 * Get domain from URL
 *
 * @param url - URL to parse
 * @returns Domain or null if invalid URL
 */
export const getDomain = (url: string): string | null => {
  try {
    const urlObj = new URL(url);
    return urlObj.hostname;
  } catch (error) {
    console.error('Invalid URL:', error);
    return null;
  }
};

/**
 * Get protocol from URL
 *
 * @param url - URL to parse
 * @returns Protocol or null if invalid URL
 */
export const getProtocol = (url: string): string | null => {
  try {
    const urlObj = new URL(url);
    return urlObj.protocol;
  } catch (error) {
    console.error('Invalid URL:', error);
    return null;
  }
};

/**
 * Get path from URL
 *
 * @param url - URL to parse
 * @returns Path or null if invalid URL
 */
export const getPath = (url: string): string | null => {
  try {
    const urlObj = new URL(url);
    return urlObj.pathname;
  } catch {
    return null;
  }
};

// ================================
// PATH OPERATIONS
// ================================

/**
 * Join URL paths
 *
 * @param base - Base URL or path
 * @param paths - Paths to join
 * @returns Joined URL path
 */
export const joinPaths = (base: string, ...paths: string[]): string => {
  const cleanBase = base.replace(/\/+$/, '');
  const cleanPaths = paths
    .filter(path => path && path.trim() !== '')
    .map(path => path.replace(/^\/+|\/+$/g, ''));

  if (cleanPaths.length === 0) return cleanBase;

  return `${cleanBase}/${cleanPaths.join('/')}`;
};

/**
 * Convert relative URL to absolute URL
 *
 * @param relativeUrl - Relative URL
 * @param baseUrl - Base URL (defaults to current location)
 * @returns Absolute URL
 */
export const toAbsoluteUrl = (
  relativeUrl: string,
  baseUrl?: string
): string => {
  try {
    const base =
      baseUrl || (typeof window !== 'undefined' ? window.location.origin : '');
    return new URL(relativeUrl, base).toString();
  } catch (error) {
    console.error('Error converting to absolute URL:', error);
    return relativeUrl;
  }
};

// ================================
// FILE OPERATIONS
// ================================

/**
 * Extract file extension from URL
 *
 * @param url - URL to parse
 * @returns File extension or empty string if not found
 */
export const getFileExtension = (url: string): string => {
  try {
    const urlObj = new URL(url);
    const pathname = urlObj.pathname;
    const lastDot = pathname.lastIndexOf('.');

    if (lastDot === -1 || lastDot === pathname.length - 1) {
      return '';
    }

    return pathname.substring(lastDot + 1).toLowerCase();
  } catch (error) {
    console.error('Invalid URL:', error);
    return '';
  }
};

/**
 * Extract filename from URL
 *
 * @param url - URL to parse
 * @returns Filename or empty string if not found
 */
export const getFilename = (url: string): string => {
  try {
    const urlObj = new URL(url);
    const pathname = urlObj.pathname;
    const segments = pathname.split('/');
    return segments[segments.length - 1] || '';
  } catch (error) {
    console.error('Invalid URL:', error);
    return '';
  }
};

// ================================
// MEDIA TYPE DETECTION
// ================================

/**
 * Check if URL points to an image
 *
 * @param url - URL to check
 * @returns True if URL points to an image
 */
export const isImageURL = (url: string): boolean => {
  const imageExtensions = [
    'jpg',
    'jpeg',
    'png',
    'gif',
    'webp',
    'svg',
    'bmp',
    'ico',
  ];
  const extension = getFileExtension(url).toLowerCase();
  return imageExtensions.includes(extension);
};

/**
 * Check if URL points to a video
 *
 * @param url - URL to check
 * @returns True if URL points to a video
 */
export const isVideoURL = (url: string): boolean => {
  const videoExtensions = [
    'mp4',
    'avi',
    'mov',
    'wmv',
    'flv',
    'webm',
    'mkv',
    'm4v',
  ];
  const extension = getFileExtension(url).toLowerCase();
  return videoExtensions.includes(extension);
};

/**
 * Check if URL points to an audio file
 *
 * @param url - URL to check
 * @returns True if URL points to an audio file
 */
export const isAudioURL = (url: string): boolean => {
  const audioExtensions = ['mp3', 'wav', 'ogg', 'aac', 'flac', 'm4a', 'wma'];
  const extension = getFileExtension(url).toLowerCase();
  return audioExtensions.includes(extension);
};

// ================================
// ENCODING/DECODING UTILITIES
// ================================

/**
 * Encode URL component safely
 *
 * @param component - Component to encode
 * @returns Encoded component
 */
export const encodeURLComponent = (component: string): string => {
  return encodeURIComponent(component);
};

/**
 * Decode URL component safely
 *
 * @param component - Component to decode
 * @returns Decoded component
 */
export const decodeURLComponent = (component: string): string => {
  try {
    return decodeURIComponent(component);
  } catch {
    return component; // Return original if decoding fails
  }
};

// ================================
// SECURITY UTILITIES
// ================================

/**
 * Sanitize URL by removing potentially dangerous characters
 *
 * @param url - URL to sanitize
 * @returns Sanitized URL
 */
export const sanitizeUrl = (url: string): string => {
  if (!url) return '';

  // Remove potentially dangerous protocols
  const dangerousProtocols = ['javascript:', 'data:', 'vbscript:', 'file:'];
  const lowerUrl = url.toLowerCase().trim();

  for (const protocol of dangerousProtocols) {
    if (lowerUrl.startsWith(protocol)) {
      return '';
    }
  }

  return url.trim();
};

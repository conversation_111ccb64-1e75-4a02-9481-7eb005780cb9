/**
 * File Utilities
 *
 * This module provides utility functions for file operations and handling
 */

/**
 * Get file extension from filename
 *
 * @param filename - Filename to extract extension from
 * @returns File extension without dot, or empty string if none
 */
export const getFileExtension = (filename: string): string => {
  const lastDot = filename.lastIndexOf('.');
  return lastDot > 0 ? filename.substring(lastDot + 1).toLowerCase() : '';
};

/**
 * Get filename without extension
 *
 * @param filename - Full filename
 * @returns Filename without extension
 */
export const getFilenameWithoutExtension = (filename: string): string => {
  const lastDot = filename.lastIndexOf('.');
  return lastDot > 0 ? filename.substring(0, lastDot) : filename;
};

/**
 * Format file size in human readable format
 *
 * @param bytes - File size in bytes
 * @param decimals - Number of decimal places (default: 2)
 * @returns Formatted file size string
 */
export const formatFileSize = (bytes: number, decimals: number = 2): string => {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];

  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
};

/**
 * Check if file is an image based on extension
 *
 * @param filename - Filename to check
 * @returns True if file is an image
 */
export const isImageFile = (filename: string): boolean => {
  const imageExtensions = [
    'jpg',
    'jpeg',
    'png',
    'gif',
    'webp',
    'svg',
    'bmp',
    'ico',
    'tiff',
    'tif',
  ];
  const extension = getFileExtension(filename);
  return imageExtensions.includes(extension);
};

/**
 * Check if file is a video based on extension
 *
 * @param filename - Filename to check
 * @returns True if file is a video
 */
export const isVideoFile = (filename: string): boolean => {
  const videoExtensions = [
    'mp4',
    'avi',
    'mov',
    'wmv',
    'flv',
    'webm',
    'mkv',
    'm4v',
    'mpg',
    'mpeg',
    '3gp',
  ];
  const extension = getFileExtension(filename);
  return videoExtensions.includes(extension);
};

/**
 * Check if file is an audio file based on extension
 *
 * @param filename - Filename to check
 * @returns True if file is an audio file
 */
export const isAudioFile = (filename: string): boolean => {
  const audioExtensions = [
    'mp3',
    'wav',
    'ogg',
    'aac',
    'flac',
    'm4a',
    'wma',
    'opus',
    'aiff',
  ];
  const extension = getFileExtension(filename);
  return audioExtensions.includes(extension);
};

/**
 * Check if file is a document based on extension
 *
 * @param filename - Filename to check
 * @returns True if file is a document
 */
export const isDocumentFile = (filename: string): boolean => {
  const documentExtensions = [
    'pdf',
    'doc',
    'docx',
    'xls',
    'xlsx',
    'ppt',
    'pptx',
    'txt',
    'rtf',
    'odt',
    'ods',
    'odp',
  ];
  const extension = getFileExtension(filename);
  return documentExtensions.includes(extension);
};

/**
 * Check if file is a code file based on extension
 *
 * @param filename - Filename to check
 * @returns True if file is a code file
 */
export const isCodeFile = (filename: string): boolean => {
  const codeExtensions = [
    'js',
    'ts',
    'jsx',
    'tsx',
    'html',
    'css',
    'scss',
    'sass',
    'less',
    'py',
    'java',
    'cpp',
    'c',
    'h',
    'cs',
    'php',
    'rb',
    'go',
    'rs',
    'swift',
    'kt',
    'dart',
    'vue',
    'svelte',
    'json',
    'xml',
    'yaml',
    'yml',
  ];
  const extension = getFileExtension(filename);
  return codeExtensions.includes(extension);
};

/**
 * Get MIME type based on file extension
 *
 * @param filename - Filename to get MIME type for
 * @returns MIME type or 'application/octet-stream' if unknown
 */
export const getMimeType = (filename: string): string => {
  const extension = getFileExtension(filename);

  const mimeTypes: { [key: string]: string } = {
    // Images
    jpg: 'image/jpeg',
    jpeg: 'image/jpeg',
    png: 'image/png',
    gif: 'image/gif',
    webp: 'image/webp',
    svg: 'image/svg+xml',
    bmp: 'image/bmp',
    ico: 'image/x-icon',
    tiff: 'image/tiff',
    tif: 'image/tiff',

    // Videos
    mp4: 'video/mp4',
    avi: 'video/x-msvideo',
    mov: 'video/quicktime',
    wmv: 'video/x-ms-wmv',
    flv: 'video/x-flv',
    webm: 'video/webm',
    mkv: 'video/x-matroska',
    mpg: 'video/mpeg',
    mpeg: 'video/mpeg',
    '3gp': 'video/3gpp',

    // Audio
    mp3: 'audio/mpeg',
    wav: 'audio/wav',
    ogg: 'audio/ogg',
    aac: 'audio/aac',
    flac: 'audio/flac',
    m4a: 'audio/m4a',
    wma: 'audio/x-ms-wma',
    opus: 'audio/opus',
    aiff: 'audio/aiff',

    // Documents
    pdf: 'application/pdf',
    doc: 'application/msword',
    docx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    xls: 'application/vnd.ms-excel',
    xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    ppt: 'application/vnd.ms-powerpoint',
    pptx: 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    txt: 'text/plain',
    rtf: 'application/rtf',
    odt: 'application/vnd.oasis.opendocument.text',
    ods: 'application/vnd.oasis.opendocument.spreadsheet',
    odp: 'application/vnd.oasis.opendocument.presentation',

    // Web
    html: 'text/html',
    css: 'text/css',
    js: 'application/javascript',
    json: 'application/json',
    xml: 'application/xml',
    yaml: 'application/x-yaml',
    yml: 'application/x-yaml',

    // Archives
    zip: 'application/zip',
    rar: 'application/x-rar-compressed',
    '7z': 'application/x-7z-compressed',
    tar: 'application/x-tar',
    gz: 'application/gzip',
  };

  return mimeTypes[extension] || 'application/octet-stream';
};

/**
 * Validate file type against allowed extensions
 *
 * @param filename - Filename to validate
 * @param allowedExtensions - Array of allowed extensions
 * @returns True if file type is allowed
 */
export const isAllowedFileType = (
  filename: string,
  allowedExtensions: string[]
): boolean => {
  const extension = getFileExtension(filename);
  return allowedExtensions.map(ext => ext.toLowerCase()).includes(extension);
};

/**
 * Validate file size
 *
 * @param file - File object or size in bytes
 * @param maxSizeInBytes - Maximum allowed size in bytes
 * @returns True if file size is within limit
 */
export const isValidFileSize = (
  file: File | number,
  maxSizeInBytes: number
): boolean => {
  const size = typeof file === 'number' ? file : file.size;
  return size <= maxSizeInBytes;
};

/**
 * Convert file to base64 string
 *
 * @param file - File to convert
 * @returns Promise that resolves to base64 string
 */
export const fileToBase64 = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => {
      if (typeof reader.result === 'string') {
        resolve(reader.result);
      } else {
        reject(new Error('Failed to convert file to base64'));
      }
    };
    reader.onerror = error => reject(error);
  });
};

/**
 * Convert base64 string to blob
 *
 * @param base64 - Base64 string (with or without data URL prefix)
 * @param mimeType - MIME type for the blob
 * @returns Blob object
 */
export const base64ToBlob = (base64: string, mimeType: string): Blob => {
  // Remove data URL prefix if present
  const base64Data = base64.includes(',') ? base64.split(',')[1] : base64;

  const byteCharacters = atob(base64Data);
  const byteNumbers = new Array(byteCharacters.length);

  for (let i = 0; i < byteCharacters.length; i++) {
    byteNumbers[i] = byteCharacters.charCodeAt(i);
  }

  const byteArray = new Uint8Array(byteNumbers);
  return new Blob([byteArray], { type: mimeType });
};

/**
 * Create a download link for a file
 *
 * @param blob - Blob to download
 * @param filename - Name for the downloaded file
 */
export const downloadBlob = (blob: Blob, filename: string): void => {
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  link.style.display = 'none';

  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);

  URL.revokeObjectURL(url);
};

/**
 * Read file as text
 *
 * @param file - File to read
 * @returns Promise that resolves to file content as text
 */
export const readFileAsText = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsText(file);
    reader.onload = () => {
      if (typeof reader.result === 'string') {
        resolve(reader.result);
      } else {
        reject(new Error('Failed to read file as text'));
      }
    };
    reader.onerror = error => reject(error);
  });
};

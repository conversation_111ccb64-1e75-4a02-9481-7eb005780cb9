/**
 * String Utilities
 *
 * This module provides utility functions for string manipulation and formatting
 */

/**
 * Capitalize the first letter of a string
 *
 * @param str - String to capitalize
 * @returns String with first letter capitalized
 */
export const capitalize = (str: string): string => {
  if (!str) return str;
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
};

/**
 * Convert string to title case
 *
 * @param str - String to convert
 * @returns String in title case
 */
export const toTitleCase = (str: string): string => {
  if (!str) return str;
  return str.replace(
    /\w\S*/g,
    txt => txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
  );
};

/**
 * Convert string to camelCase
 *
 * @param str - String to convert
 * @returns String in camelCase
 */
export const toCamelCase = (str: string): string => {
  return str
    .replace(/(?:^\w|[A-Z]|\b\w)/g, (word, index) =>
      index === 0 ? word.toLowerCase() : word.toUpperCase()
    )
    .replace(/\s+/g, '');
};

/**
 * Convert string to kebab-case
 *
 * @param str - String to convert
 * @returns String in kebab-case
 */
export const toKebabCase = (str: string): string => {
  return str
    .replace(/([a-z])([A-Z])/g, '$1-$2')
    .replace(/[\s_]+/g, '-')
    .toLowerCase();
};

/**
 * Convert string to snake_case
 *
 * @param str - String to convert
 * @returns String in snake_case
 */
export const toSnakeCase = (str: string): string => {
  return str
    .replace(/([a-z])([A-Z])/g, '$1_$2')
    .replace(/[\s-]+/g, '_')
    .toLowerCase();
};

/**
 * Truncate string to specified length with ellipsis
 *
 * @param str - String to truncate
 * @param length - Maximum length
 * @param suffix - Suffix to add (default: '...')
 * @returns Truncated string
 */
export const truncate = (
  str: string,
  length: number,
  suffix: string = '...'
): string => {
  if (str.length <= length) return str;
  return str.substring(0, length - suffix.length) + suffix;
};

/**
 * Remove HTML tags from string
 *
 * @param str - String containing HTML
 * @returns String without HTML tags
 */
export const stripHTML = (str: string): string => {
  return str.replace(/<[^>]*>/g, '');
};

/**
 * Escape HTML special characters
 *
 * @param str - String to escape
 * @returns Escaped string
 */
export const escapeHTML = (str: string): string => {
  const htmlEscapes: { [key: string]: string } = {
    '&': '&amp;',
    '<': '&lt;',
    '>': '&gt;',
    '"': '&quot;',
    "'": '&#x27;',
    '/': '&#x2F;',
  };

  return str.replace(/[&<>"'/]/g, match => htmlEscapes[match]);
};

/**
 * Unescape HTML special characters
 *
 * @param str - String to unescape
 * @returns Unescaped string
 */
export const unescapeHTML = (str: string): string => {
  const htmlUnescapes: { [key: string]: string } = {
    '&amp;': '&',
    '&lt;': '<',
    '&gt;': '>',
    '&quot;': '"',
    '&#x27;': "'",
    '&#x2F;': '/',
  };

  return str.replace(
    /&amp;|&lt;|&gt;|&quot;|&#x27;|&#x2F;/g,
    match => htmlUnescapes[match]
  );
};

/**
 * Generate a random string
 *
 * @param length - Length of the random string
 * @param chars - Characters to use (default: alphanumeric)
 * @returns Random string
 */
export const randomString = (
  length: number,
  chars: string = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
): string => {
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
};

/**
 * Count words in a string
 *
 * @param str - String to count words in
 * @returns Number of words
 */
export const wordCount = (str: string): number => {
  return str
    .trim()
    .split(/\s+/)
    .filter(word => word.length > 0).length;
};

/**
 * Extract words from string
 *
 * @param str - String to extract words from
 * @returns Array of words
 */
export const extractWords = (str: string): string[] => {
  return str
    .trim()
    .split(/\s+/)
    .filter(word => word.length > 0);
};

/**
 * Pluralize a word based on count
 *
 * @param word - Word to pluralize
 * @param count - Count to determine plural/singular
 * @param pluralForm - Custom plural form (optional)
 * @returns Pluralized word
 */
export const pluralize = (
  word: string,
  count: number,
  pluralForm?: string
): string => {
  if (count === 1) return word;

  if (pluralForm) return pluralForm;

  // Simple pluralization rules
  if (word.endsWith('y')) {
    return word.slice(0, -1) + 'ies';
  }
  if (
    word.endsWith('s') ||
    word.endsWith('sh') ||
    word.endsWith('ch') ||
    word.endsWith('x') ||
    word.endsWith('z')
  ) {
    return word + 'es';
  }

  return word + 's';
};

/**
 * Create a slug from a string
 *
 * @param str - String to convert to slug
 * @returns Slug string
 */
export const createSlug = (str: string): string => {
  return str
    .toLowerCase()
    .trim()
    .replace(/[^\w\s-]/g, '') // Remove special characters
    .replace(/[\s_-]+/g, '-') // Replace spaces and underscores with hyphens
    .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens
};

/**
 * Pad string to specified length
 *
 * @param str - String to pad
 * @param length - Target length
 * @param padChar - Character to pad with (default: ' ')
 * @param padLeft - Whether to pad left or right (default: false - right)
 * @returns Padded string
 */
export const padString = (
  str: string,
  length: number,
  padChar: string = ' ',
  padLeft: boolean = false
): string => {
  if (str.length >= length) return str;

  const padLength = length - str.length;
  const padding = padChar.repeat(padLength);

  return padLeft ? padding + str : str + padding;
};

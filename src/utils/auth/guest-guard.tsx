import { useNavigate } from 'react-router-dom';
import { useEffect } from 'react';

// project imports
import useAuth from '@/hooks/use-auth';
import { DASHBOARD_PATH } from '@/config';
import { GuardProps } from '@/types';

// ==============================|| GUEST GUARD ||============================== //

/**
 * Guest guard for routes having no auth required
 * Redirects authenticated users to dashboard
 * @param {GuardProps} props - Component props containing children
 */
const GuestGuard = ({ children }: GuardProps) => {
  const { isLoggedIn } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    if (isLoggedIn) {
      navigate(DASHBOARD_PATH, { replace: true });
    }
  }, [isLoggedIn, navigate]);

  return children;
};

export default GuestGuard;

import { useNavigate } from 'react-router-dom';
import { PropsWithChildren, useEffect } from 'react';

// project imports
import useAuth from '@/hooks/use-auth';

// ==============================|| AUTH GUARD ||============================== //

/**
 * Authentication guard for routes
 * Redirects unauthenticated users to login page
 * @param {PropsWithChildren} props - Component props containing children
 */
const AuthGuard = ({ children }: PropsWithChildren) => {
  const { isLoggedIn } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    if (!isLoggedIn) {
      navigate('login', { replace: true });
    }
  }, [isLoggedIn, navigate]);

  return <>{children}</>;
};

export default AuthGuard;

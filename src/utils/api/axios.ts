/**
 * API Utilities
 *
 * This module provides HTTP client utilities and API service configurations
 */

import axios from 'axios';

// API URL from environment variable with fallback
const API_BASE_URL =
  import.meta.env.VITE_API_URL || 'https://demo.genieland.ai/';

/**
 * Axios instance configured with base URL and interceptors
 *
 * @description Main HTTP client for API requests with error handling
 */
const axiosServices = axios.create({
  baseURL: API_BASE_URL,
});

// Request interceptor for authentication and error handling
axiosServices.interceptors.request.use(
  config => {
    // Add authentication token if available
    const token = localStorage.getItem('serviceToken');
    if (token) {
      config.headers = config.headers || {};
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  error => Promise.reject(error)
);

// Response interceptor for error handling
axiosServices.interceptors.response.use(
  response => response,
  error =>
    Promise.reject((error.response && error.response.data) || 'Wrong Services')
);

export default axiosServices;

/**
 * API Integration Demo Page
 *
 * Demonstrates best practices for API integration using error boundaries,
 * custom hooks, and service patterns.
 */

import { useState } from 'react';
import { Button } from '@/components/ui/shadcn/button';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/shadcn/card';
import { Input } from '@/components/ui/shadcn/input';
import { Alert, AlertDescription } from '@/components/ui/shadcn/alert';
import { Badge } from '@/components/ui/shadcn/badge';
import { Loader2, RefreshCw, Plus, Trash2, AlertCircle } from 'lucide-react';

// Import our new hooks and error boundaries
import {
  useApiData,
  useMutation,
  useInfiniteQuery,
} from '@/hooks/use-api-data';
import { ApiErrorBoundary } from '@/components/base/boundary/api-error-boundary';
import { ErrorBoundary } from '@/components/base/boundary/error-boundary';

// Import example service - this is allowed in pages
import {
  fetchExampleItems,
  createExampleItem,
  deleteExampleItem,
  CreateExampleRequest,
} from '@/services/example-api';

/**
 * API Integration Demo Page Component
 */
export default function ApiIntegrationDemo() {
  const [newItemName, setNewItemName] = useState('');
  const [deletingItemId, setDeletingItemId] = useState<string | null>(null);

  // Example 1: Basic data fetching with error boundary
  const {
    data: items,
    loading: itemsLoading,
    error: itemsError,
    refetch: refetchItems,
    isStale,
  } = useApiData(() => fetchExampleItems({ size: 10 }));

  // Example 2: Mutation with optimistic updates
  const createItemMutation = useMutation(
    (request: CreateExampleRequest) => createExampleItem(request),
    {
      onSuccess: () => {
        setNewItemName('');
        refetchItems(); // Refresh the list
      },
    }
  );

  // Example 3: Delete mutation
  const deleteItemMutation = useMutation(
    (id: string) => deleteExampleItem(id),
    {
      onSuccess: () => {
        setDeletingItemId(null);
        refetchItems(); // Refresh the list
      },
      onError: () => {
        setDeletingItemId(null);
      },
    }
  );

  // Example 4: Infinite query for pagination
  const {
    data: infiniteItems,
    loading: infiniteLoading,
    hasNextPage,
    fetchNextPage,
    isFetchingNextPage,
  } = useInfiniteQuery(
    (pageParam = 0) => fetchExampleItems({ page: pageParam, size: 5 }),
    {
      getNextPageParam: (lastPage, allPages) => {
        return lastPage.last ? undefined : allPages.length;
      },
    }
  );

  const handleCreateItem = async () => {
    if (!newItemName.trim()) return;

    try {
      await createItemMutation.mutate({
        name: newItemName,
        description: `Created item: ${newItemName}`,
        category: 'example',
      });
    } catch {
      // Error is handled by the mutation hook
    }
  };

  const handleDeleteItem = async (id: string) => {
    if (!confirm('Are you sure you want to delete this item?')) return;

    setDeletingItemId(id);
    try {
      await deleteItemMutation.mutate(id);
    } catch (error) {
      console.error('Failed to delete item:', error);
      setDeletingItemId(null);
    }
  };

  return (
    <div className="flex min-h-[calc(100vh-4rem)] flex-1 flex-col gap-6 p-6">
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold">API Integration Demo</h1>
          <Badge variant={isStale ? 'secondary' : 'default'}>
            {isStale ? 'Data may be stale' : 'Data is fresh'}
          </Badge>
        </div>
        <p className="text-muted-foreground">
          This page demonstrates best practices for API integration using error
          boundaries, custom hooks, and service patterns.
        </p>
      </div>

      <div className="space-y-6">
        {/* Example 1: Basic Data Fetching with Error Boundary */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              Example 1: Basic Data Fetching
              <Button
                variant="outline"
                size="sm"
                onClick={refetchItems}
                disabled={itemsLoading}
              >
                {itemsLoading ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <RefreshCw className="h-4 w-4" />
                )}
                Refresh
              </Button>
            </CardTitle>
            <div className="space-y-1 text-sm text-muted-foreground">
              <p>
                <strong>Purpose:</strong> Demonstrates basic API data fetching
                with the useApiData hook
              </p>
              <p>
                <strong>Features:</strong> Loading states, error handling, data
                display, manual refresh
              </p>
              <p>
                <strong>Try:</strong> Click "Refresh" to reload data, delete
                items to see list updates
              </p>
            </div>
          </CardHeader>
          <CardContent>
            <ApiErrorBoundary onRetry={refetchItems}>
              {itemsLoading && !items && (
                <div className="flex items-center gap-2 text-muted-foreground">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  Loading items...
                </div>
              )}

              {itemsError && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    Failed to load items: {itemsError.message}
                  </AlertDescription>
                </Alert>
              )}

              {items && (
                <div className="space-y-2">
                  <p className="text-sm text-muted-foreground">
                    Showing {items.content.length} of {items.totalElements}{' '}
                    items
                  </p>
                  <div className="grid gap-2">
                    {items.content.map(item => (
                      <div
                        key={item.id}
                        className="flex items-center justify-between rounded border p-3"
                      >
                        <div>
                          <h4 className="font-medium">{item.name}</h4>
                          <p className="text-sm text-muted-foreground">
                            {item.description}
                          </p>
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDeleteItem(item.id)}
                          disabled={deletingItemId === item.id}
                        >
                          {deletingItemId === item.id ? (
                            <Loader2 className="h-4 w-4 animate-spin" />
                          ) : (
                            <Trash2 className="h-4 w-4" />
                          )}
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </ApiErrorBoundary>
          </CardContent>
        </Card>

        {/* Example 2: Create Item Mutation */}
        <Card>
          <CardHeader>
            <CardTitle>Example 2: Create Item Mutation</CardTitle>
            <div className="space-y-1 text-sm text-muted-foreground">
              <p>
                <strong>Purpose:</strong> Demonstrates API mutations
                (POST/PUT/DELETE) with the useMutation hook
              </p>
              <p>
                <strong>Features:</strong> Form handling, loading states,
                success/error feedback, automatic list refresh
              </p>
              <p>
                <strong>Try:</strong> Enter a name and click "Create" to add a
                new item to the list above
              </p>
            </div>
          </CardHeader>
          <CardContent>
            <ErrorBoundary level="component">
              <div className="flex gap-2">
                <Input
                  placeholder="Enter item name"
                  value={newItemName}
                  onChange={e => setNewItemName(e.target.value)}
                  onKeyDown={e => e.key === 'Enter' && handleCreateItem()}
                />
                <Button
                  onClick={handleCreateItem}
                  disabled={createItemMutation.loading || !newItemName.trim()}
                >
                  {createItemMutation.loading ? (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  ) : (
                    <Plus className="mr-2 h-4 w-4" />
                  )}
                  Create
                </Button>
              </div>

              {createItemMutation.error && (
                <Alert variant="destructive" className="mt-3">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    Failed to create item: {createItemMutation.error.message}
                  </AlertDescription>
                </Alert>
              )}

              {createItemMutation.data && (
                <Alert className="mt-3">
                  <AlertDescription>
                    Successfully created: {createItemMutation.data.name}
                  </AlertDescription>
                </Alert>
              )}
            </ErrorBoundary>
          </CardContent>
        </Card>

        {/* Example 3: Infinite Query */}
        <Card>
          <CardHeader>
            <CardTitle>Example 3: Infinite Scroll / Pagination</CardTitle>
            <div className="space-y-1 text-sm text-muted-foreground">
              <p>
                <strong>Purpose:</strong> Demonstrates paginated data loading
                with the useInfiniteQuery hook
              </p>
              <p>
                <strong>Features:</strong> Load more functionality, page
                management, loading states for pagination
              </p>
              <p>
                <strong>Try:</strong> Click "Load More" to fetch additional
                pages of data (5 items per page)
              </p>
            </div>
          </CardHeader>
          <CardContent>
            <ApiErrorBoundary>
              {infiniteLoading && infiniteItems.length === 0 && (
                <div className="flex items-center gap-2 text-muted-foreground">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  Loading paginated items...
                </div>
              )}

              <div className="space-y-2">
                {infiniteItems.map((page, pageIndex) =>
                  page.content.map(item => (
                    <div
                      key={`${pageIndex}-${item.id}`}
                      className="rounded border p-3"
                    >
                      <h4 className="font-medium">{item.name}</h4>
                      <p className="text-sm text-muted-foreground">
                        Page {pageIndex + 1} - {item.description}
                      </p>
                    </div>
                  ))
                )}
              </div>

              {hasNextPage && (
                <Button
                  onClick={fetchNextPage}
                  disabled={isFetchingNextPage}
                  className="mt-4 w-full"
                  variant="outline"
                >
                  {isFetchingNextPage ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Loading more...
                    </>
                  ) : (
                    'Load More'
                  )}
                </Button>
              )}

              {!hasNextPage && infiniteItems.length > 0 && (
                <p className="mt-4 text-center text-muted-foreground">
                  No more items to load
                </p>
              )}
            </ApiErrorBoundary>
          </CardContent>
        </Card>

        {/* Example 4: Error Boundary Levels */}
        <Card>
          <CardHeader>
            <CardTitle>Example 4: Error Boundary Levels</CardTitle>
            <div className="space-y-1 text-sm text-muted-foreground">
              <p>
                <strong>Purpose:</strong> Demonstrates different error boundary
                levels and error handling strategies
              </p>
              <p>
                <strong>Features:</strong> Component-level, page-level, and
                API-specific error boundaries
              </p>
              <p>
                <strong>Try:</strong> Click the "Trigger Error" buttons to see
                how different error boundaries handle failures
              </p>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h4 className="mb-2 font-medium">Component Level Error</h4>
              <ErrorBoundary level="component">
                <ComponentThatMightFail level="component" />
              </ErrorBoundary>
            </div>

            <div>
              <h4 className="mb-2 font-medium">Page Level Error</h4>
              <ErrorBoundary level="page">
                <ComponentThatMightFail level="page" />
              </ErrorBoundary>
            </div>

            <div>
              <h4 className="mb-2 font-medium">API Error Boundary</h4>
              <ApiErrorBoundary>
                <ComponentWithApiError />
              </ApiErrorBoundary>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

/**
 * Example component that demonstrates different error levels
 */
function ComponentThatMightFail({ level }: { level: string }) {
  const [shouldError, setShouldError] = useState(false);

  if (shouldError) {
    throw new Error(`This is a ${level} level error for demonstration`);
  }

  return (
    <div className="rounded border p-3">
      <p className="mb-2 text-sm">This component is working normally.</p>
      <Button
        variant="destructive"
        size="sm"
        onClick={() => setShouldError(true)}
      >
        Trigger {level} Error
      </Button>
    </div>
  );
}

/**
 * Example component that simulates API errors
 */
function ComponentWithApiError() {
  const [hasTriggered, setHasTriggered] = useState(false);

  const handleTriggerError = () => {
    setHasTriggered(true);
    // This will throw an error that the API error boundary should catch
    throw new Error(
      'Simulated API error - This demonstrates how API error boundaries handle failures'
    );
  };

  if (hasTriggered) {
    handleTriggerError();
  }

  return (
    <div className="rounded border p-3">
      <p className="mb-2 text-sm text-muted-foreground">
        This component will throw an API error when you click the button below.
        The API Error Boundary should catch it and display an error UI.
      </p>

      <Button
        variant="destructive"
        size="sm"
        onClick={() => setHasTriggered(true)}
      >
        Trigger API Error
      </Button>
    </div>
  );
}

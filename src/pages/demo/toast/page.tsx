// Toast demonstration component
import { useToast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/shadcn/button';
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
  CardFooter,
} from '@/components/ui/shadcn/card';

/**
 * Toast Demonstration Component
 *
 * Displays a dashboard with different toast notification examples
 * that users can trigger to see the different toast styles.
 */
export default function ToastDemo() {
  const { toast } = useToast();

  const showSuccessToast = () => {
    toast({
      title: 'Success',
      description: 'Your action was completed successfully',
      duration: 3000,
    });
  };

  const showErrorToast = () => {
    toast({
      variant: 'destructive',
      title: 'Error',
      description: 'Something went wrong. Please try again.',
      duration: 3000,
    });
  };

  const showWarningToast = () => {
    toast({
      title: 'Warning',
      description: 'Please review your information before proceeding.',
      className: 'bg-orange-500 text-white',
      duration: 3000,
    });
  };

  const showInfoToast = () => {
    toast({
      title: 'Information',
      description: 'Your data is being processed.',
      className: 'bg-blue-500 text-white',
      duration: 3000,
    });
  };

  return (
    <div className="flex min-h-[calc(100vh-4rem)] flex-1 flex-col gap-6 p-6">
      <Card className="flex-1">
        <CardHeader className="border-b">
          <CardTitle>Toast Notifications Demo</CardTitle>
          <CardDescription>
            Welcome to the toast notifications demo. Explore different toast
            notifications below.
          </CardDescription>
        </CardHeader>
        <CardContent className="flex-1 p-6">
          <div className="grid h-full gap-6 md:grid-cols-2 lg:grid-cols-4">
            {/* Success Toast Card */}
            <Card>
              <CardHeader>
                <CardTitle className="text-xl">Success Toast</CardTitle>
                <CardDescription>
                  Display a success notification
                </CardDescription>
              </CardHeader>
              <CardFooter>
                <Button
                  onClick={showSuccessToast}
                  className="w-full bg-green-600 hover:bg-green-700"
                >
                  Show Success Toast
                </Button>
              </CardFooter>
            </Card>

            {/* Error Toast Card */}
            <Card>
              <CardHeader>
                <CardTitle className="text-xl">Error Toast</CardTitle>
                <CardDescription>Display an error notification</CardDescription>
              </CardHeader>
              <CardFooter>
                <Button
                  onClick={showErrorToast}
                  variant="destructive"
                  className="w-full"
                >
                  Show Error Toast
                </Button>
              </CardFooter>
            </Card>

            {/* Warning Toast Card */}
            <Card>
              <CardHeader>
                <CardTitle className="text-xl">Warning Toast</CardTitle>
                <CardDescription>
                  Display a warning notification
                </CardDescription>
              </CardHeader>
              <CardFooter>
                <Button
                  onClick={showWarningToast}
                  className="w-full bg-orange-500 hover:bg-orange-600"
                >
                  Show Warning Toast
                </Button>
              </CardFooter>
            </Card>

            {/* Info Toast Card */}
            <Card>
              <CardHeader>
                <CardTitle className="text-xl">Info Toast</CardTitle>
                <CardDescription>Display an info notification</CardDescription>
              </CardHeader>
              <CardFooter>
                <Button
                  onClick={showInfoToast}
                  className="w-full bg-primary hover:bg-primary/80"
                >
                  Show Info Toast
                </Button>
              </CardFooter>
            </Card>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

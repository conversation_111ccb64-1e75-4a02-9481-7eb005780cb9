/**
 * Error Boundaries Demo Page
 *
 * This page demonstrates all the specialized error boundary components
 * and how they handle different types of errors.
 */

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/shadcn/button';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from '@/components/ui/shadcn/card';
import {
  <PERSON><PERSON>,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/shadcn/tabs';
import { Input } from '@/components/ui/shadcn/input';
import { Label } from '@/components/ui/shadcn/label';
import { AlertTriangle, Bug, Database, FileText, Globe } from 'lucide-react';

// Import error boundaries
import { ErrorBoundary } from '@/components/base/boundary/error-boundary';
import { ApiErrorBoundary } from '@/components/base/boundary/api-error-boundary';
import { FormErrorBoundary } from '@/components/base/boundary/form-error-boundary';
import { AsyncErrorBoundary } from '@/components/base/boundary/async-error-boundary';
import { DataErrorBoundary } from '@/components/base/boundary/data-error-boundary';

/**
 * Main demo page component
 */
export default function ErrorBoundariesDemo() {
  return (
    <div className="flex min-h-[calc(100vh-4rem)] flex-1 flex-col gap-6 p-6">
      <div className="space-y-2">
        <h1 className="text-3xl font-bold">Error Boundaries Demo</h1>
        <p className="text-muted-foreground">
          This page demonstrates the specialized error boundary components and
          how they handle different types of errors.
        </p>
      </div>

      <Tabs defaultValue="general">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="general">
            <AlertTriangle className="mr-2 h-4 w-4" />
            General
          </TabsTrigger>
          <TabsTrigger value="api">
            <Globe className="mr-2 h-4 w-4" />
            API
          </TabsTrigger>
          <TabsTrigger value="form">
            <FileText className="mr-2 h-4 w-4" />
            Form
          </TabsTrigger>
          <TabsTrigger value="async">
            <Bug className="mr-2 h-4 w-4" />
            Async
          </TabsTrigger>
          <TabsTrigger value="data">
            <Database className="mr-2 h-4 w-4" />
            Data
          </TabsTrigger>
        </TabsList>

        <TabsContent value="general" className="mt-6">
          <GeneralErrorBoundaryDemo />
        </TabsContent>

        <TabsContent value="api" className="mt-6">
          <ApiErrorBoundaryDemo />
        </TabsContent>

        <TabsContent value="form" className="mt-6">
          <FormErrorBoundaryDemo />
        </TabsContent>

        <TabsContent value="async" className="mt-6">
          <AsyncErrorBoundaryDemo />
        </TabsContent>

        <TabsContent value="data" className="mt-6">
          <DataErrorBoundaryDemo />
        </TabsContent>
      </Tabs>
    </div>
  );
}

/**
 * General Error Boundary Demo
 */
function GeneralErrorBoundaryDemo() {
  return (
    <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
      <DemoCard
        title="Component Level Error"
        description="Demonstrates how component-level errors are handled with minimal UI disruption"
      >
        <ErrorBoundary level="component">
          <ComponentErrorTrigger />
        </ErrorBoundary>
      </DemoCard>

      <DemoCard
        title="Page Level Error"
        description="Shows how page-level errors are handled with a more prominent error UI"
      >
        <ErrorBoundary level="page">
          <PageErrorTrigger />
        </ErrorBoundary>
      </DemoCard>

      <DemoCard
        title="Critical Error"
        description="Demonstrates how critical errors are handled with a full-page error UI"
      >
        <ErrorBoundary level="critical">
          <CriticalErrorTrigger />
        </ErrorBoundary>
      </DemoCard>
    </div>
  );
}

/**
 * API Error Boundary Demo
 */
function ApiErrorBoundaryDemo() {
  const handleRetry = () => {
    console.log('Retrying API call...');
    // In a real app, this would refresh the data
  };

  return (
    <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
      <DemoCard
        title="Network Error"
        description="Simulates a network connectivity issue"
      >
        <ApiErrorBoundary onRetry={handleRetry}>
          <ApiNetworkErrorTrigger />
        </ApiErrorBoundary>
      </DemoCard>

      <DemoCard
        title="API Unavailable"
        description="Simulates an API service being unavailable"
      >
        <ApiErrorBoundary onRetry={handleRetry}>
          <ApiUnavailableErrorTrigger />
        </ApiErrorBoundary>
      </DemoCard>
    </div>
  );
}

/**
 * Form Error Boundary Demo
 */
function FormErrorBoundaryDemo() {
  const handleReset = () => {
    console.log('Resetting form...');
    // In a real app, this would reset the form state
  };

  return (
    <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
      <DemoCard
        title="Validation Error"
        description="Demonstrates how form validation errors are handled"
      >
        <FormErrorBoundary onReset={handleReset}>
          <FormValidationErrorTrigger />
        </FormErrorBoundary>
      </DemoCard>

      <DemoCard
        title="Submission Error"
        description="Shows how form submission errors are handled"
      >
        <FormErrorBoundary onReset={handleReset}>
          <FormSubmissionErrorTrigger />
        </FormErrorBoundary>
      </DemoCard>
    </div>
  );
}

/**
 * Async Error Boundary Demo
 */
function AsyncErrorBoundaryDemo() {
  const handleRetry = async () => {
    console.log('Retrying async operation...');
    // In a real app, this would retry the async operation
    await new Promise(resolve => setTimeout(resolve, 1000));
  };

  return (
    <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
      <DemoCard
        title="Promise Rejection"
        description="Demonstrates how rejected promises are handled"
      >
        <AsyncErrorBoundary
          onRetry={handleRetry}
          maxRetries={3}
          showProgress={true}
        >
          <AsyncPromiseErrorTrigger />
        </AsyncErrorBoundary>
      </DemoCard>

      <DemoCard
        title="Timeout Error"
        description="Shows how timeout errors are handled with automatic retry"
      >
        <AsyncErrorBoundary
          onRetry={handleRetry}
          maxRetries={3}
          showProgress={true}
        >
          <AsyncTimeoutErrorTrigger />
        </AsyncErrorBoundary>
      </DemoCard>
    </div>
  );
}

/**
 * Data Error Boundary Demo
 */
function DataErrorBoundaryDemo() {
  const handleRetry = () => {
    console.log('Retrying data fetch...');
    // In a real app, this would refresh the data
  };

  const handleClearCache = () => {
    console.log('Clearing data cache...');
    // In a real app, this would clear the cache
  };

  const mockData = {
    id: 'fallback-123',
    name: 'Fallback Data',
    description: 'This is fallback data used when the main data fails to load',
  };

  return (
    <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
      <DemoCard
        title="Parsing Error"
        description="Demonstrates how data parsing errors are handled"
      >
        <DataErrorBoundary
          onRetry={handleRetry}
          onClearCache={handleClearCache}
          dataSource="user-api"
        >
          <DataParsingErrorTrigger />
        </DataErrorBoundary>
      </DemoCard>

      <DemoCard
        title="With Fallback Data"
        description="Shows how fallback data can be used when primary data fails"
      >
        <DataErrorBoundary
          onRetry={handleRetry}
          fallbackData={mockData}
          showFallback={true}
          dataSource="product-api"
        >
          <DataWithFallbackTrigger />
        </DataErrorBoundary>
      </DemoCard>
    </div>
  );
}

/**
 * Demo Card Component
 */
function DemoCard({
  title,
  description,
  children,
}: {
  title: string;
  description: string;
  children: React.ReactNode;
}) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent>{children}</CardContent>
    </Card>
  );
}

/**
 * Error Trigger Components
 */

// General Error Triggers
function ComponentErrorTrigger() {
  const [shouldError, setShouldError] = useState(false);

  if (shouldError) {
    throw new Error('This is a component-level error for demonstration');
  }

  return (
    <div className="space-y-4">
      <p className="text-sm">
        Click the button to trigger a component-level error.
      </p>
      <Button
        onClick={() => setShouldError(true)}
        variant="destructive"
        size="sm"
      >
        Trigger Component Error
      </Button>
    </div>
  );
}

function PageErrorTrigger() {
  const [shouldError, setShouldError] = useState(false);

  if (shouldError) {
    throw new Error('This is a page-level error for demonstration');
  }

  return (
    <div className="space-y-4">
      <p className="text-sm">Click the button to trigger a page-level error.</p>
      <Button
        onClick={() => setShouldError(true)}
        variant="destructive"
        size="sm"
      >
        Trigger Page Error
      </Button>
    </div>
  );
}

function CriticalErrorTrigger() {
  const [shouldError, setShouldError] = useState(false);

  if (shouldError) {
    throw new Error('This is a critical error for demonstration');
  }

  return (
    <div className="space-y-4">
      <p className="text-sm">Click the button to trigger a critical error.</p>
      <Button
        onClick={() => setShouldError(true)}
        variant="destructive"
        size="sm"
      >
        Trigger Critical Error
      </Button>
    </div>
  );
}

// API Error Triggers
function ApiNetworkErrorTrigger() {
  const [shouldError, setShouldError] = useState(false);

  if (shouldError) {
    throw new Error('Network error: Failed to fetch data from API');
  }

  return (
    <div className="space-y-4">
      <p className="text-sm">Click the button to simulate a network error.</p>
      <Button
        onClick={() => setShouldError(true)}
        variant="destructive"
        size="sm"
      >
        Trigger Network Error
      </Button>
    </div>
  );
}

function ApiUnavailableErrorTrigger() {
  const [shouldError, setShouldError] = useState(false);

  if (shouldError) {
    throw new Error(
      'API unavailable: Service returned 503 Service Unavailable'
    );
  }

  return (
    <div className="space-y-4">
      <p className="text-sm">
        Click the button to simulate an API unavailable error.
      </p>
      <Button
        onClick={() => setShouldError(true)}
        variant="destructive"
        size="sm"
      >
        Trigger API Unavailable
      </Button>
    </div>
  );
}

// Form Error Triggers
function FormValidationErrorTrigger() {
  const [shouldError, setShouldError] = useState(false);

  if (shouldError) {
    throw new Error('Form validation error: Email field is invalid');
  }

  return (
    <div className="space-y-4">
      <form className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="email">Email</Label>
          <Input id="email" type="email" placeholder="Enter your email" />
        </div>
        <div className="space-y-2">
          <Label htmlFor="password">Password</Label>
          <Input
            id="password"
            type="password"
            placeholder="Enter your password"
          />
        </div>
        <Button
          type="button"
          onClick={() => setShouldError(true)}
          variant="destructive"
          size="sm"
        >
          Trigger Validation Error
        </Button>
      </form>
    </div>
  );
}

function FormSubmissionErrorTrigger() {
  const [shouldError, setShouldError] = useState(false);

  if (shouldError) {
    throw new Error('Form submission error: Failed to submit form data');
  }

  return (
    <div className="space-y-4">
      <form className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="name">Name</Label>
          <Input id="name" placeholder="Enter your name" />
        </div>
        <div className="space-y-2">
          <Label htmlFor="message">Message</Label>
          <Input id="message" placeholder="Enter your message" />
        </div>
        <Button
          type="button"
          onClick={() => setShouldError(true)}
          variant="destructive"
          size="sm"
        >
          Trigger Submission Error
        </Button>
      </form>
    </div>
  );
}

// Async Error Triggers
function AsyncPromiseErrorTrigger() {
  const [shouldError, setShouldError] = useState(false);

  if (shouldError) {
    throw new Error('Promise rejected: Async operation failed');
  }

  return (
    <div className="space-y-4">
      <p className="text-sm">
        Click the button to simulate a rejected promise.
      </p>
      <Button
        onClick={() => setShouldError(true)}
        variant="destructive"
        size="sm"
      >
        Trigger Promise Error
      </Button>
    </div>
  );
}

function AsyncTimeoutErrorTrigger() {
  const [shouldError, setShouldError] = useState(false);

  if (shouldError) {
    throw new Error('Timeout error: Operation took too long to complete');
  }

  return (
    <div className="space-y-4">
      <p className="text-sm">Click the button to simulate a timeout error.</p>
      <Button
        onClick={() => setShouldError(true)}
        variant="destructive"
        size="sm"
      >
        Trigger Timeout Error
      </Button>
    </div>
  );
}

// Data Error Triggers
function DataParsingErrorTrigger() {
  const [shouldError, setShouldError] = useState(false);

  if (shouldError) {
    throw new Error(
      'JSON parsing error: Unexpected token in JSON at position 4'
    );
  }

  return (
    <div className="space-y-4">
      <p className="text-sm">
        Click the button to simulate a data parsing error.
      </p>
      <Button
        onClick={() => setShouldError(true)}
        variant="destructive"
        size="sm"
      >
        Trigger Parsing Error
      </Button>
    </div>
  );
}

function DataWithFallbackTrigger() {
  const [shouldError, setShouldError] = useState(false);

  if (shouldError) {
    throw new Error('Data validation error: Schema validation failed');
  }

  return (
    <div className="space-y-4">
      <p className="text-sm">
        Click the button to trigger an error and see fallback data.
      </p>
      <Button
        onClick={() => setShouldError(true)}
        variant="destructive"
        size="sm"
      >
        Trigger Error & Show Fallback
      </Button>
    </div>
  );
}

import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useIntl } from 'react-intl';
import { ErrorBoundary } from '@/components/base/boundary/error-boundary';

/**
 * SignupPage Component
 *
 * This component serves as a redirect to the registration page.
 * It automatically redirects users to the '/register' route when mounted.
 *
 * The purpose of having both '/signup' and '/register' routes is to provide
 * multiple entry points for user registration, improving user experience
 * and SEO by supporting different URL patterns that users might expect.
 */
export default function SignupPage() {
  const navigate = useNavigate();
  const intl = useIntl();

  useEffect(() => {
    // Redirect to register page immediately when component mounts
    navigate('/register', { replace: true });
  }, [navigate]);

  // This component won't actually render anything visible
  // as it will redirect before rendering, but we provide a fallback
  // in case there's any delay in the redirect
  return (
    <ErrorBoundary
      level="page"
      showDetails={import.meta.env.MODE === 'development'}
    >
      <div className="flex h-screen w-full items-center justify-center">
        <p>
          {intl.formatMessage({ id: 'page.signup' })}:{' '}
          {intl.formatMessage({ id: 'page.register.description' })}
        </p>
      </div>
    </ErrorBoundary>
  );
}

import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/shadcn/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/shadcn/card';
import { Input } from '@/components/ui/shadcn/input';
import { Label } from '@/components/ui/shadcn/label';
import useAuth from '@/hooks/use-auth';
import { useIntl } from 'react-intl';
import { FormErrorBoundary } from '@/components/base/boundary/form-error-boundary';

export default function ForgotPassword() {
  const intl = useIntl();
  const navigate = useNavigate();
  const { resetPassword } = useAuth();
  const [email, setEmail] = useState('');
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await resetPassword(email);
      setIsSubmitted(true);
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <div className="flex h-screen w-full items-center justify-center">
      <Card className="w-[350px]">
        <CardHeader>
          <CardTitle>
            {intl.formatMessage({ id: 'page.forgot.title' })}
          </CardTitle>
          <CardDescription>
            {intl.formatMessage({ id: 'page.forgot.description' })}
          </CardDescription>
        </CardHeader>
        <FormErrorBoundary>
          <form onSubmit={handleSubmit}>
            <CardContent>
              {!isSubmitted ? (
                <div className="flex flex-col space-y-1.5">
                  <Label htmlFor="email">
                    {intl.formatMessage({ id: 'page.register.email' })}
                  </Label>
                  <Input
                    id="email"
                    type="email"
                    value={email}
                    onChange={e => setEmail(e.target.value)}
                  />
                </div>
              ) : (
                <p className="text-sm text-muted-foreground">
                  {intl.formatMessage({ id: 'page.forgot.success' }, { email })}
                </p>
              )}
            </CardContent>
            <CardFooter className="flex flex-col gap-4">
              {!isSubmitted && (
                <Button
                  className="w-full bg-primary text-primary-foreground hover:bg-primary/90"
                  type="submit"
                >
                  {intl.formatMessage({ id: 'page.forgot.sendLink' })}
                </Button>
              )}
              <Button
                variant="link"
                className="text-primary hover:text-primary/90"
                onClick={() => navigate('/login')}
              >
                {intl.formatMessage({ id: 'page.forgot.backToLogin' })}
              </Button>
            </CardFooter>
          </form>
        </FormErrorBoundary>
      </Card>
    </div>
  );
}

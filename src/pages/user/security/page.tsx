import { useState, useEffect, FormEvent } from 'react';
import {
  Shield,
  Key,
  Smartphone,
  History,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Lock,
} from 'lucide-react';
import { useIntl } from 'react-intl';
import { FormErrorBoundary } from '@/components/base/boundary/form-error-boundary';
import { getSecurityData } from '@/services/user';
import type { SecurityData } from '@/types/user';
import { Button } from '@/components/ui/shadcn/button';
import { PasswordInput } from '@/components/features/login/password-input';
import { Label } from '@/components/ui/shadcn/label';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/shadcn/card';
import { Badge } from '@/components/ui/shadcn/badge';
import { Separator } from '@/components/ui/shadcn/separator';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/shadcn/tabs';
import { Alert, AlertDescription } from '@/components/ui/shadcn/alert';

/**
 * User Security Page Component
 *
 * Allows users to manage security settings including:
 * - Password management
 * - Two-factor authentication
 * - Session management
 * - Security audit log
 * Data is fetched from API with fallback to mock data.
 */
export default function Page() {
  const intl = useIntl();
  const [userData, setUserData] = useState<SecurityData | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Load security data on component mount
  useEffect(() => {
    const loadSecurityData = async () => {
      try {
        setIsLoading(true);
        const securityData = await getSecurityData();
        setUserData(securityData);
      } catch (error) {
        console.error('Failed to load security data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadSecurityData();
  }, []);

  // Form submission handlers
  const handlePasswordChange = (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    // In a real app, you would call an API to update the password
    console.log('Changing password');
    alert(intl.formatMessage({ id: 'user.security.notifications.saved' }));

    // Update the password last changed date
    if (userData) {
      setUserData({
        ...userData,
        passwordLastChanged: new Date().toISOString(),
      });
    }
  };

  const handleToggleTwoFactor = () => {
    // In a real app, you would call an API to enable/disable 2FA
    if (userData) {
      setUserData({
        ...userData,
        twoFactorEnabled: !userData.twoFactorEnabled,
      });
    }
  };

  const handleRevokeSessions = (sessionId: string | null = null) => {
    // In a real app, you would call an API to revoke sessions
    if (!userData) return;

    if (sessionId) {
      // Revoke a specific session
      setUserData({
        ...userData,
        activeSessions: userData.activeSessions.filter(
          session => session.id !== sessionId
        ),
      });
    } else {
      // Revoke all sessions except current
      setUserData({
        ...userData,
        activeSessions: userData.activeSessions.filter(
          session => session.current
        ),
      });
    }
  };

  // Format date for display
  const formatDate = (dateString: string): string => {
    const options: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    };
    return new Date(dateString).toLocaleDateString('en-US', options);
  };

  // Calculate days ago for a date
  const getDaysAgo = (dateString: string): number => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    return Math.floor(diffTime / (1000 * 60 * 60 * 24));
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="mb-6 h-8 w-1/4 rounded bg-muted"></div>
          <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
            <div className="space-y-4 lg:col-span-2">
              <div className="h-32 rounded bg-muted"></div>
              <div className="h-96 rounded bg-muted"></div>
            </div>
            <div className="h-80 rounded bg-muted"></div>
          </div>
        </div>
      </div>
    );
  }

  if (!userData) {
    return (
      <div className="flex min-h-[400px] items-center justify-center">
        <div className="space-y-3 text-center">
          <div className="text-muted-foreground">
            {intl.formatMessage({ id: 'user.security.loading.failed' })}
          </div>
          <Button variant="outline" onClick={() => window.location.reload()}>
            {intl.formatMessage({ id: 'actions.retry' })}
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            {intl.formatMessage({ id: 'user.security.title' })}
          </h1>
          <p className="text-muted-foreground">
            {intl.formatMessage({ id: 'user.security.subtitle' })}
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Shield className="h-5 w-5 text-green-600" />
          <Badge variant="secondary" className="bg-green-100 text-green-700">
            {intl.formatMessage({ id: 'user.security.status.secure' })}
          </Badge>
        </div>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
        <Card className="lg:col-span-2">
          <CardContent className="p-0">
            <Tabs defaultValue="password" className="space-y-6">
              <div className="p-6 pb-0">
                <TabsList className="grid w-full grid-cols-4">
                  <TabsTrigger
                    value="password"
                    className="flex items-center gap-2"
                  >
                    <Key className="h-4 w-4" />
                    <span className="hidden sm:inline">
                      {intl.formatMessage({
                        id: 'user.security.tabs.password',
                      })}
                    </span>
                  </TabsTrigger>
                  <TabsTrigger
                    value="two-factor"
                    className="flex items-center gap-2"
                  >
                    <Smartphone className="h-4 w-4" />
                    <span className="hidden sm:inline">
                      {intl.formatMessage({
                        id: 'user.security.tabs.twoFactor',
                      })}
                    </span>
                  </TabsTrigger>
                  <TabsTrigger
                    value="sessions"
                    className="flex items-center gap-2"
                  >
                    <Lock className="h-4 w-4" />
                    <span className="hidden sm:inline">
                      {intl.formatMessage({
                        id: 'user.security.tabs.sessions',
                      })}
                    </span>
                  </TabsTrigger>
                  <TabsTrigger
                    value="history"
                    className="flex items-center gap-2"
                  >
                    <History className="h-4 w-4" />
                    <span className="hidden sm:inline">
                      {intl.formatMessage({ id: 'user.security.tabs.history' })}
                    </span>
                  </TabsTrigger>
                </TabsList>
              </div>

              {/* Password Tab */}
              <TabsContent value="password" className="space-y-6 px-6 pb-6">
                <div>
                  <h3 className="mb-2 text-lg font-medium">
                    {intl.formatMessage({ id: 'user.security.password.title' })}
                  </h3>
                  <p className="mb-4 text-sm text-muted-foreground">
                    {intl.formatMessage({
                      id: 'user.security.password.description',
                    })}
                  </p>

                  <div className="mb-4 flex items-center gap-2 text-sm text-muted-foreground">
                    <History className="h-4 w-4" />
                    <span>
                      {intl.formatMessage(
                        { id: 'user.security.password.lastChanged' },
                        { date: formatDate(userData.passwordLastChanged) }
                      )}{' '}
                      ({getDaysAgo(userData.passwordLastChanged)}{' '}
                      {intl.formatMessage({ id: 'user.security.daysAgo' })})
                    </span>
                  </div>

                  {getDaysAgo(userData.passwordLastChanged) > 90 && (
                    <Alert className="mb-6">
                      <AlertTriangle className="h-4 w-4" />
                      <AlertDescription>
                        <p className="font-medium">
                          {intl.formatMessage({
                            id: 'user.security.password.oldWarning',
                          })}
                        </p>
                        <p className="mt-1">
                          {intl.formatMessage({
                            id: 'user.security.password.updateRecommendation',
                          })}
                        </p>
                      </AlertDescription>
                    </Alert>
                  )}

                  <FormErrorBoundary>
                    <form onSubmit={handlePasswordChange} className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="current-password">
                          {intl.formatMessage({
                            id: 'user.security.password.current',
                          })}
                        </Label>
                        <PasswordInput
                          id="current-password"
                          name="currentPassword"
                          required
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="new-password">
                          {intl.formatMessage({
                            id: 'user.security.password.new',
                          })}
                        </Label>
                        <PasswordInput
                          id="new-password"
                          name="newPassword"
                          required
                        />
                        <p className="text-sm text-muted-foreground">
                          {intl.formatMessage({
                            id: 'user.security.password.requirements.description',
                          })}
                        </p>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="confirm-password">
                          {intl.formatMessage({
                            id: 'user.security.password.confirm',
                          })}
                        </Label>
                        <PasswordInput
                          id="confirm-password"
                          name="confirmPassword"
                          required
                        />
                      </div>

                      <Button type="submit" className="w-full sm:w-auto">
                        {intl.formatMessage({
                          id: 'user.security.password.change',
                        })}
                      </Button>
                    </form>
                  </FormErrorBoundary>
                </div>
              </TabsContent>

              {/* Two-Factor Authentication Tab */}
              <TabsContent value="two-factor" className="space-y-6 px-6 pb-6">
                <div>
                  <div className="mb-4 flex items-start justify-between">
                    <div>
                      <h3 className="mb-2 text-lg font-medium">
                        {intl.formatMessage({
                          id: 'user.security.twoFactor.title',
                        })}
                      </h3>
                      <p className="text-sm text-muted-foreground">
                        {intl.formatMessage({
                          id: 'user.security.twoFactor.description',
                        })}
                      </p>
                    </div>
                    <Badge
                      variant={
                        userData.twoFactorEnabled ? 'default' : 'secondary'
                      }
                    >
                      {userData.twoFactorEnabled
                        ? intl.formatMessage({ id: 'common.enabled' })
                        : intl.formatMessage({ id: 'common.disabled' })}
                    </Badge>
                  </div>

                  {userData.twoFactorEnabled ? (
                    <div className="space-y-6">
                      <div className="rounded-lg border p-4">
                        <h4 className="mb-2 text-sm font-medium">
                          {intl.formatMessage({
                            id: 'user.security.twoFactor.recoveryCodes',
                          })}
                        </h4>
                        <p className="mb-4 text-sm text-muted-foreground">
                          {intl.formatMessage(
                            {
                              id: 'user.security.twoFactor.recoveryCodesDescription',
                            },
                            { remaining: userData.recoveryCodesRemaining }
                          )}
                        </p>
                        <div className="flex gap-3">
                          <Button variant="outline" size="sm">
                            {intl.formatMessage({
                              id: 'user.security.twoFactor.viewCodes',
                            })}
                          </Button>
                          <Button variant="outline" size="sm">
                            {intl.formatMessage({
                              id: 'user.security.twoFactor.generateCodes',
                            })}
                          </Button>
                        </div>
                      </div>

                      <Separator />

                      <Alert variant="destructive">
                        <AlertTriangle className="h-4 w-4" />
                        <AlertDescription>
                          <p className="font-medium">
                            {intl.formatMessage({
                              id: 'user.security.twoFactor.disableWarning.title',
                            })}
                          </p>
                          <p>
                            {intl.formatMessage({
                              id: 'user.security.twoFactor.disableWarning.description',
                            })}
                          </p>
                        </AlertDescription>
                      </Alert>

                      <Button
                        variant="destructive"
                        onClick={handleToggleTwoFactor}
                      >
                        {intl.formatMessage({
                          id: 'user.security.twoFactor.disable',
                        })}
                      </Button>
                    </div>
                  ) : (
                    <div className="space-y-6">
                      <Alert>
                        <Shield className="h-4 w-4" />
                        <AlertDescription>
                          <p className="font-medium">
                            {intl.formatMessage({
                              id: 'user.security.twoFactor.recommendation.title',
                            })}
                          </p>
                          <p className="mt-1">
                            {intl.formatMessage({
                              id: 'user.security.twoFactor.recommendation.description',
                            })}
                          </p>
                        </AlertDescription>
                      </Alert>

                      <div className="rounded-lg border p-4">
                        <h4 className="mb-4 text-sm font-medium">
                          {intl.formatMessage({
                            id: 'user.security.twoFactor.setupOptions',
                          })}
                        </h4>
                        <div className="space-y-4">
                          <div className="flex items-start space-x-3">
                            <div className="flex-shrink-0 pt-0.5">
                              <input
                                id="authenticator-app"
                                name="2fa-method"
                                type="radio"
                                defaultChecked
                                className="h-4 w-4 border-gray-300 text-primary"
                              />
                            </div>
                            <label
                              htmlFor="authenticator-app"
                              className="space-y-1"
                            >
                              <div className="text-sm font-medium">
                                {intl.formatMessage({
                                  id: 'user.security.twoFactor.authenticatorApp',
                                })}
                              </div>
                              <div className="text-sm text-muted-foreground">
                                {intl.formatMessage({
                                  id: 'user.security.twoFactor.authenticatorApp.description',
                                })}
                              </div>
                            </label>
                          </div>

                          <div className="flex items-start space-x-3">
                            <div className="flex-shrink-0 pt-0.5">
                              <input
                                id="sms"
                                name="2fa-method"
                                type="radio"
                                className="h-4 w-4 border-gray-300 text-primary"
                              />
                            </div>
                            <label htmlFor="sms" className="space-y-1">
                              <div className="text-sm font-medium">
                                {intl.formatMessage({
                                  id: 'user.security.twoFactor.sms',
                                })}
                              </div>
                              <div className="text-sm text-muted-foreground">
                                {intl.formatMessage({
                                  id: 'user.security.twoFactor.sms.description',
                                })}
                              </div>
                            </label>
                          </div>
                        </div>
                      </div>

                      <Button onClick={handleToggleTwoFactor}>
                        {intl.formatMessage({
                          id: 'user.security.twoFactor.enable',
                        })}
                      </Button>
                    </div>
                  )}
                </div>
              </TabsContent>

              {/* Active Sessions Tab */}
              <TabsContent value="sessions" className="space-y-6 px-6 pb-6">
                <div>
                  <div className="mb-4 flex items-center justify-between">
                    <div>
                      <h3 className="mb-2 text-lg font-medium">
                        {intl.formatMessage({
                          id: 'user.security.sessions.title',
                        })}
                      </h3>
                      <p className="text-sm text-muted-foreground">
                        {intl.formatMessage({
                          id: 'user.security.sessions.description',
                        })}
                      </p>
                    </div>
                    {userData.activeSessions.length > 1 && (
                      <Button
                        variant="destructive"
                        size="sm"
                        onClick={() => handleRevokeSessions()}
                      >
                        {intl.formatMessage({
                          id: 'user.security.sessions.revokeAll',
                        })}
                      </Button>
                    )}
                  </div>

                  <div className="space-y-4">
                    {userData.activeSessions.map(session => (
                      <div
                        key={session.id}
                        className={`rounded-lg border p-4 ${session.current ? 'border-primary bg-primary/5' : ''}`}
                      >
                        <div className="flex items-start justify-between">
                          <div className="space-y-2">
                            <div className="flex items-center gap-2">
                              <span className="font-medium">
                                {session.device}
                              </span>
                              {session.current && (
                                <Badge variant="default" className="text-xs">
                                  {intl.formatMessage({
                                    id: 'user.security.sessions.current',
                                  })}
                                </Badge>
                              )}
                            </div>
                            <div className="space-y-1 text-sm text-muted-foreground">
                              <div>
                                {intl.formatMessage({
                                  id: 'user.security.sessions.ipAddress',
                                })}
                                : {session.ipAddress} •{' '}
                                {intl.formatMessage({
                                  id: 'user.security.sessions.location',
                                })}
                                : {session.location}
                              </div>
                              <div>
                                {intl.formatMessage({
                                  id: 'user.security.sessions.lastActive',
                                })}
                                : {formatDate(session.lastActive)}
                              </div>
                            </div>
                          </div>

                          {!session.current && (
                            <Button
                              variant="destructive"
                              size="sm"
                              onClick={() => handleRevokeSessions(session.id)}
                            >
                              {intl.formatMessage({
                                id: 'user.security.sessions.revoke',
                              })}
                            </Button>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </TabsContent>

              {/* Security History Tab */}
              <TabsContent value="history" className="space-y-6 px-6 pb-6">
                <div>
                  <h3 className="mb-2 text-lg font-medium">
                    {intl.formatMessage({
                      id: 'user.security.loginHistory.title',
                    })}
                  </h3>
                  <p className="mb-4 text-sm text-muted-foreground">
                    {intl.formatMessage({
                      id: 'user.security.loginHistory.description',
                    })}
                  </p>

                  <div className="space-y-6">
                    {userData.securityEvents.map(event => (
                      <div
                        key={event.id}
                        className="flex items-start gap-4 border-b pb-4 last:border-b-0 last:pb-0"
                      >
                        <div className="mt-1 flex-shrink-0">
                          {event.type === 'login' && (
                            <div className="flex h-8 w-8 items-center justify-center rounded-full bg-green-100">
                              <CheckCircle className="h-4 w-4 text-green-600" />
                            </div>
                          )}
                          {event.type === 'login_failed' && (
                            <div className="flex h-8 w-8 items-center justify-center rounded-full bg-red-100">
                              <XCircle className="h-4 w-4 text-red-600" />
                            </div>
                          )}
                          {event.type === 'password_change' && (
                            <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary/10">
                              <Key className="h-4 w-4 text-primary" />
                            </div>
                          )}
                          {event.type === 'mfa_disabled' && (
                            <div className="flex h-8 w-8 items-center justify-center rounded-full bg-yellow-100">
                              <AlertTriangle className="h-4 w-4 text-yellow-600" />
                            </div>
                          )}
                        </div>

                        <div className="min-w-0 flex-1">
                          <h4 className="text-sm font-medium text-foreground">
                            {event.description}
                          </h4>
                          <div className="mt-2 grid grid-cols-1 gap-2 text-sm text-muted-foreground sm:grid-cols-2 lg:grid-cols-2">
                            <div>
                              <span className="font-medium">
                                {intl.formatMessage({
                                  id: 'user.security.activity.time',
                                })}
                                :
                              </span>{' '}
                              {formatDate(event.timestamp)}
                            </div>
                            <div>
                              <span className="font-medium">
                                {intl.formatMessage({
                                  id: 'user.security.activity.device',
                                })}
                                :
                              </span>{' '}
                              {event.device}
                            </div>
                            <div>
                              <span className="font-medium">
                                {intl.formatMessage({
                                  id: 'user.security.activity.ip',
                                })}
                                :
                              </span>{' '}
                              {event.ipAddress}
                            </div>
                            <div>
                              <span className="font-medium">
                                {intl.formatMessage({
                                  id: 'user.security.activity.location',
                                })}
                                :
                              </span>{' '}
                              {event.location}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>

        {/* Security Overview Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              {intl.formatMessage({ id: 'user.security.overview.title' })}
            </CardTitle>
            <CardDescription>
              {intl.formatMessage({ id: 'user.security.overview.description' })}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div>
                  <Label className="text-sm font-medium">
                    {intl.formatMessage({
                      id: 'user.security.overview.passwordStrength',
                    })}
                  </Label>
                  <p className="text-sm text-muted-foreground">
                    {intl.formatMessage({
                      id: 'user.security.overview.strong',
                    })}
                  </p>
                </div>
                <Badge
                  variant="secondary"
                  className="bg-green-100 text-green-700"
                >
                  ✓
                </Badge>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label className="text-sm font-medium">
                    {intl.formatMessage({
                      id: 'user.security.twoFactor.title',
                    })}
                  </Label>
                  <p className="text-sm text-muted-foreground">
                    {userData.twoFactorEnabled
                      ? intl.formatMessage({ id: 'common.enabled' })
                      : intl.formatMessage({ id: 'common.disabled' })}
                  </p>
                </div>
                <Badge
                  variant={
                    userData.twoFactorEnabled ? 'secondary' : 'destructive'
                  }
                  className={
                    userData.twoFactorEnabled
                      ? 'bg-green-100 text-green-700'
                      : ''
                  }
                >
                  {userData.twoFactorEnabled ? '✓' : '!'}
                </Badge>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label className="text-sm font-medium">
                    {intl.formatMessage({ id: 'user.security.sessions.title' })}
                  </Label>
                  <p className="text-sm text-muted-foreground">
                    {userData.activeSessions.length}{' '}
                    {intl.formatMessage({
                      id: 'user.security.overview.devices',
                    })}
                  </p>
                </div>
                <Badge variant="secondary">
                  {userData.activeSessions.length}
                </Badge>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label className="text-sm font-medium">
                    {intl.formatMessage({
                      id: 'user.security.overview.lastPasswordChange',
                    })}
                  </Label>
                  <p className="text-sm text-muted-foreground">
                    {getDaysAgo(userData.passwordLastChanged)}{' '}
                    {intl.formatMessage({ id: 'user.security.daysAgo' })}
                  </p>
                </div>
                <Badge
                  variant={
                    getDaysAgo(userData.passwordLastChanged) > 90
                      ? 'destructive'
                      : 'secondary'
                  }
                >
                  {getDaysAgo(userData.passwordLastChanged) > 90 ? '!' : '✓'}
                </Badge>
              </div>
            </div>

            <Separator />

            <div className="space-y-2">
              <Label className="text-sm font-medium">
                {intl.formatMessage({
                  id: 'user.security.overview.securityScore',
                })}
              </Label>
              <div className="flex items-center gap-2">
                <div className="h-2 flex-1 rounded-full bg-muted">
                  <div
                    className={`h-2 rounded-full ${
                      userData.twoFactorEnabled &&
                      getDaysAgo(userData.passwordLastChanged) <= 90
                        ? 'w-full bg-green-500'
                        : userData.twoFactorEnabled ||
                            getDaysAgo(userData.passwordLastChanged) <= 90
                          ? 'w-3/4 bg-yellow-500'
                          : 'w-1/2 bg-red-500'
                    }`}
                  ></div>
                </div>
                <span className="text-sm font-medium">
                  {userData.twoFactorEnabled &&
                  getDaysAgo(userData.passwordLastChanged) <= 90
                    ? intl.formatMessage({
                        id: 'user.security.overview.excellent',
                      })
                    : userData.twoFactorEnabled ||
                        getDaysAgo(userData.passwordLastChanged) <= 90
                      ? intl.formatMessage({
                          id: 'user.security.overview.good',
                        })
                      : intl.formatMessage({
                          id: 'user.security.overview.needsImprovement',
                        })}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

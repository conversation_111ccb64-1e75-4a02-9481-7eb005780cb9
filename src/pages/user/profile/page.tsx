import { useState, useEffect, FormEvent, ChangeEvent } from 'react';
import {
  Camera,
  Mail,
  Phone,
  MapPin,
  Building,
  Briefcase,
  Calendar,
  CheckCircle2,
  AlertCircle,
  Edit3,
  Save,
  X,
  User,
  Info,
} from 'lucide-react';
import { useIntl } from 'react-intl';
import { ErrorBoundary } from '@/components/base/boundary/error-boundary';
import { getUserProfile, updateUserProfile } from '@/services/user';
import type { UserProfileData } from '@/types/user';
import { Button } from '@/components/ui/shadcn/button';
import { Input } from '@/components/ui/shadcn/input';
import { Textarea } from '@/components/ui/shadcn/textarea';
import { Label } from '@/components/ui/shadcn/label';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/shadcn/card';
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from '@/components/ui/shadcn/avatar';
import { Badge } from '@/components/ui/shadcn/badge';
import { Separator } from '@/components/ui/shadcn/separator';
import { Progress } from '@/components/ui/shadcn/progress';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/shadcn/tooltip';
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from '@/components/ui/shadcn/hover-card';

/**
 * User Profile Page Component
 *
 * Displays and allows editing of user profile information.
 * Enhanced with professional enterprise-level shadcn/ui components.
 */
export default function Page() {
  const intl = useIntl();
  const [userData, setUserData] = useState<UserProfileData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [editData, setEditData] = useState<UserProfileData | null>(null);

  // Calculate profile completion percentage
  const calculateProfileCompletion = (data: UserProfileData | null) => {
    if (!data) return 0;
    const fields = [
      data.firstName,
      data.lastName,
      data.email,
      data.phone,
      data.jobTitle,
      data.company,
      data.location,
      data.bio,
      data.avatarUrl,
    ];
    const filledFields = fields.filter(
      field => field && field.trim() !== ''
    ).length;
    return Math.round((filledFields / fields.length) * 100);
  };

  // Load user profile data on component mount
  useEffect(() => {
    const loadUserData = async () => {
      try {
        setIsLoading(true);
        const profileData = await getUserProfile();
        setUserData(profileData);
        setEditData(profileData);
      } catch (error) {
        console.error('Failed to load user profile:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadUserData();
  }, []);

  // Handle form submission
  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!editData) return;

    try {
      setIsSaving(true);
      const updatedProfile = await updateUserProfile(editData);
      setUserData(updatedProfile);
      setIsEditing(false);
    } catch (error) {
      console.error('Failed to update profile:', error);
    } finally {
      setIsSaving(false);
    }
  };

  // Handle input changes
  const handleChange = (
    e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setEditData(prev => (prev ? { ...prev, [name]: value } : null));
  };

  // Cancel editing
  const handleCancel = () => {
    setEditData(userData);
    setIsEditing(false);
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="mb-6 h-8 w-1/4 rounded bg-muted"></div>
          <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
            <div className="space-y-4 lg:col-span-2">
              <div className="h-32 rounded bg-muted"></div>
              <div className="h-96 rounded bg-muted"></div>
            </div>
            <div className="h-80 rounded bg-muted"></div>
          </div>
        </div>
      </div>
    );
  }

  if (!userData) {
    return (
      <div className="flex min-h-[400px] items-center justify-center">
        <div className="space-y-3 text-center">
          <div className="text-muted-foreground">
            {intl.formatMessage({ id: 'user.profile.errors.failedToLoad' })}
          </div>
          <Button variant="outline" onClick={() => window.location.reload()}>
            {intl.formatMessage({ id: 'user.profile.actions.tryAgain' })}
          </Button>
        </div>
      </div>
    );
  }

  const profileCompletion = calculateProfileCompletion(userData);

  return (
    <ErrorBoundary
      level="page"
      showDetails={import.meta.env.MODE === 'development'}
    >
      <TooltipProvider>
        <div className="space-y-6">
          {/* Page Header */}
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <h1 className="text-3xl font-bold tracking-tight">
                {intl.formatMessage({ id: 'user.profile.title' })}
              </h1>
              <p className="text-muted-foreground">
                {intl.formatMessage({ id: 'user.profile.subtitle' })}
              </p>
            </div>
            {!isEditing ? (
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button onClick={() => setIsEditing(true)} className="gap-2">
                    <Edit3 className="h-4 w-4" />
                    {intl.formatMessage({
                      id: 'user.profile.actions.editProfile',
                    })}
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>
                    {intl.formatMessage({
                      id: 'user.profile.tooltips.editProfile',
                    })}
                  </p>
                </TooltipContent>
              </Tooltip>
            ) : (
              <div className="flex gap-2">
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="outline"
                      onClick={handleCancel}
                      className="gap-2"
                    >
                      <X className="h-4 w-4" />
                      {intl.formatMessage({
                        id: 'user.profile.actions.cancel',
                      })}
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>
                      {intl.formatMessage({
                        id: 'user.profile.tooltips.cancel',
                      })}
                    </p>
                  </TooltipContent>
                </Tooltip>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      onClick={() => {
                        const form = document.getElementById(
                          'profile-form'
                        ) as HTMLFormElement;
                        form?.requestSubmit();
                      }}
                      disabled={isSaving}
                      className="gap-2"
                    >
                      <Save className="h-4 w-4" />
                      {isSaving
                        ? intl.formatMessage({
                            id: 'user.profile.actions.saving',
                          })
                        : intl.formatMessage({
                            id: 'user.profile.actions.saveChanges',
                          })}
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>
                      {intl.formatMessage({
                        id: 'user.profile.tooltips.saveChanges',
                      })}
                    </p>
                  </TooltipContent>
                </Tooltip>
              </div>
            )}
          </div>

          {/* Main Content */}
          <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
            {/* Profile Overview Card */}
            <Card className="transition-shadow duration-200 hover:shadow-lg lg:col-span-2">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      <User className="h-5 w-5" />
                      {intl.formatMessage({
                        id: 'user.profile.sections.personalInformation',
                      })}
                    </CardTitle>
                    <CardDescription>
                      {intl.formatMessage({
                        id: 'user.profile.sections.personalInformationDescription',
                      })}
                    </CardDescription>
                  </div>
                  <HoverCard>
                    <HoverCardTrigger asChild>
                      <div className="flex cursor-help items-center gap-2">
                        <Info className="h-4 w-4 text-muted-foreground" />
                        <Badge
                          variant={
                            profileCompletion === 100 ? 'default' : 'secondary'
                          }
                          className="gap-1"
                        >
                          {profileCompletion === 100 ? (
                            <CheckCircle2 className="h-3 w-3" />
                          ) : (
                            <AlertCircle className="h-3 w-3" />
                          )}
                          {profileCompletion}%{' '}
                          {intl.formatMessage({
                            id: 'user.profile.completion.complete',
                          })}
                        </Badge>
                      </div>
                    </HoverCardTrigger>
                    <HoverCardContent className="w-80">
                      <div className="space-y-3">
                        <h4 className="text-sm font-semibold">
                          {intl.formatMessage({
                            id: 'user.profile.completion.title',
                          })}
                        </h4>
                        <Progress
                          value={profileCompletion}
                          className="w-full"
                        />
                        <p className="text-xs text-muted-foreground">
                          {intl.formatMessage({
                            id: 'user.profile.completion.description',
                          })}
                        </p>
                      </div>
                    </HoverCardContent>
                  </HoverCard>
                </div>
              </CardHeader>
              <CardContent>
                <form
                  id="profile-form"
                  onSubmit={handleSubmit}
                  className="space-y-6"
                >
                  {/* Profile Picture Section */}
                  <div className="flex items-center gap-6">
                    <div className="group relative">
                      <Avatar className="h-24 w-24 transition-transform group-hover:scale-105">
                        <AvatarImage
                          src={userData.avatarUrl ?? undefined}
                          alt="Profile"
                        />
                        <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-600 text-lg text-white">
                          {(userData.firstName?.[0] ?? '') +
                            (userData.lastName?.[0] ?? '')}
                        </AvatarFallback>
                      </Avatar>
                      {isEditing && (
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              type="button"
                              variant="outline"
                              size="icon"
                              className="absolute -bottom-2 -right-2 h-8 w-8 rounded-full shadow-lg transition-all duration-200 hover:shadow-xl"
                            >
                              <Camera className="h-4 w-4" />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>
                              {intl.formatMessage({
                                id: 'user.profile.tooltips.changeProfilePicture',
                              })}
                            </p>
                          </TooltipContent>
                        </Tooltip>
                      )}
                    </div>
                    <div className="space-y-2">
                      <h3 className="text-xl font-semibold">
                        {(userData.firstName ?? '') +
                          ' ' +
                          (userData.lastName ?? '')}
                      </h3>
                      <div className="flex items-center gap-2 text-muted-foreground">
                        <Mail className="h-4 w-4" />
                        <span>{userData.email}</span>
                      </div>
                      <div className="flex items-center gap-3">
                        <Badge
                          variant="secondary"
                          className="gap-1 transition-colors hover:bg-secondary/80"
                        >
                          <Calendar className="h-3 w-3" />
                          {intl.formatMessage(
                            { id: 'user.profile.memberSince' },
                            { date: formatDate(userData.joinDate) }
                          )}
                        </Badge>
                        {profileCompletion === 100 && (
                          <Tooltip>
                            <TooltipTrigger asChild>
                              {' '}
                              <Badge
                                variant="default"
                                className="gap-1 bg-green-500 hover:bg-green-600"
                              >
                                <CheckCircle2 className="h-3 w-3" />
                                {intl.formatMessage({
                                  id: 'user.profile.status.verified',
                                })}
                              </Badge>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>
                                {intl.formatMessage({
                                  id: 'user.profile.tooltips.verified',
                                })}
                              </p>
                            </TooltipContent>
                          </Tooltip>
                        )}
                      </div>
                    </div>
                  </div>

                  <Separator />

                  {/* Form Fields */}
                  <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                    <div className="space-y-2">
                      <Label
                        htmlFor="firstName"
                        className="flex items-center gap-2"
                      >
                        {intl.formatMessage({
                          id: 'user.profile.fields.firstName',
                        })}
                        {!editData?.firstName && (
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <AlertCircle className="h-3 w-3 text-amber-500" />
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>
                                {intl.formatMessage({
                                  id: 'user.profile.tooltips.fieldRequired',
                                })}
                              </p>
                            </TooltipContent>
                          </Tooltip>
                        )}
                      </Label>
                      <Input
                        id="firstName"
                        name="firstName"
                        value={editData?.firstName || ''}
                        onChange={handleChange}
                        disabled={!isEditing}
                        className={`transition-all duration-200 ${
                          isEditing
                            ? 'hover:border-primary/50 focus:ring-2'
                            : ''
                        } ${!editData?.firstName ? 'border-amber-200' : ''}`}
                        placeholder={intl.formatMessage({
                          id: 'user.profile.placeholders.firstName',
                        })}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label
                        htmlFor="lastName"
                        className="flex items-center gap-2"
                      >
                        {intl.formatMessage({
                          id: 'user.profile.fields.lastName',
                        })}
                        {!editData?.lastName && (
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <AlertCircle className="h-3 w-3 text-amber-500" />
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>
                                {intl.formatMessage({
                                  id: 'user.profile.tooltips.fieldRequired',
                                })}
                              </p>
                            </TooltipContent>
                          </Tooltip>
                        )}
                      </Label>
                      <Input
                        id="lastName"
                        name="lastName"
                        value={editData?.lastName || ''}
                        onChange={handleChange}
                        disabled={!isEditing}
                        className={`transition-all duration-200 ${
                          isEditing
                            ? 'hover:border-primary/50 focus:ring-2'
                            : ''
                        } ${!editData?.lastName ? 'border-amber-200' : ''}`}
                        placeholder={intl.formatMessage({
                          id: 'user.profile.placeholders.lastName',
                        })}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label
                        htmlFor="email"
                        className="flex items-center gap-2"
                      >
                        <Mail className="h-4 w-4" />
                        {intl.formatMessage({
                          id: 'user.profile.fields.email',
                        })}
                        <Badge variant="outline" className="text-xs">
                          {intl.formatMessage({
                            id: 'user.profile.labels.required',
                          })}
                        </Badge>
                      </Label>
                      <Input
                        type="email"
                        id="email"
                        name="email"
                        value={editData?.email || ''}
                        onChange={handleChange}
                        disabled={!isEditing}
                        className="transition-all duration-200 hover:border-primary/50 focus:ring-2"
                        placeholder={intl.formatMessage({
                          id: 'user.profile.placeholders.email',
                        })}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label
                        htmlFor="phone"
                        className="flex items-center gap-2"
                      >
                        <Phone className="h-4 w-4" />
                        {intl.formatMessage({
                          id: 'user.profile.fields.phone',
                        })}
                        {!editData?.phone && (
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <AlertCircle className="h-3 w-3 text-amber-500" />
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>
                                {intl.formatMessage({
                                  id: 'user.profile.tooltips.phoneImproves',
                                })}
                              </p>
                            </TooltipContent>
                          </Tooltip>
                        )}
                      </Label>
                      <Input
                        type="tel"
                        id="phone"
                        name="phone"
                        value={editData?.phone || ''}
                        onChange={handleChange}
                        disabled={!isEditing}
                        className={`transition-all duration-200 ${
                          isEditing
                            ? 'hover:border-primary/50 focus:ring-2'
                            : ''
                        } ${!editData?.phone ? 'border-amber-200' : ''}`}
                        placeholder={intl.formatMessage({
                          id: 'user.profile.placeholders.phone',
                        })}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label
                        htmlFor="jobTitle"
                        className="flex items-center gap-2"
                      >
                        <Briefcase className="h-4 w-4" />
                        {intl.formatMessage({
                          id: 'user.profile.fields.jobTitle',
                        })}
                        {!editData?.jobTitle && (
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <AlertCircle className="h-3 w-3 text-amber-500" />
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>
                                {intl.formatMessage({
                                  id: 'user.profile.tooltips.jobTitle',
                                })}
                              </p>
                            </TooltipContent>
                          </Tooltip>
                        )}
                      </Label>
                      <Input
                        id="jobTitle"
                        name="jobTitle"
                        value={editData?.jobTitle || ''}
                        onChange={handleChange}
                        disabled={!isEditing}
                        className={`transition-all duration-200 ${
                          isEditing
                            ? 'hover:border-primary/50 focus:ring-2'
                            : ''
                        } ${!editData?.jobTitle ? 'border-amber-200' : ''}`}
                        placeholder={intl.formatMessage({
                          id: 'user.profile.placeholders.jobTitle',
                        })}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label
                        htmlFor="company"
                        className="flex items-center gap-2"
                      >
                        <Building className="h-4 w-4" />
                        {intl.formatMessage({
                          id: 'user.profile.fields.company',
                        })}
                        {!editData?.company && (
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <AlertCircle className="h-3 w-3 text-amber-500" />
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>
                                {intl.formatMessage({
                                  id: 'user.profile.tooltips.company',
                                })}
                              </p>
                            </TooltipContent>
                          </Tooltip>
                        )}
                      </Label>
                      <Input
                        id="company"
                        name="company"
                        value={editData?.company || ''}
                        onChange={handleChange}
                        disabled={!isEditing}
                        className={`transition-all duration-200 ${
                          isEditing
                            ? 'hover:border-primary/50 focus:ring-2'
                            : ''
                        } ${!editData?.company ? 'border-amber-200' : ''}`}
                        placeholder={intl.formatMessage({
                          id: 'user.profile.placeholders.company',
                        })}
                      />
                    </div>

                    <div className="space-y-2 md:col-span-2">
                      <Label
                        htmlFor="location"
                        className="flex items-center gap-2"
                      >
                        <MapPin className="h-4 w-4" />
                        {intl.formatMessage({
                          id: 'user.profile.fields.location',
                        })}
                        {!editData?.location && (
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <AlertCircle className="h-3 w-3 text-amber-500" />
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>
                                {intl.formatMessage({
                                  id: 'user.profile.tooltips.location',
                                })}
                              </p>
                            </TooltipContent>
                          </Tooltip>
                        )}
                      </Label>
                      <Input
                        id="location"
                        name="location"
                        value={editData?.location || ''}
                        onChange={handleChange}
                        disabled={!isEditing}
                        className={`transition-all duration-200 ${
                          isEditing
                            ? 'hover:border-primary/50 focus:ring-2'
                            : ''
                        } ${!editData?.location ? 'border-amber-200' : ''}`}
                        placeholder={intl.formatMessage({
                          id: 'user.profile.placeholders.location',
                        })}
                      />
                    </div>

                    <div className="space-y-2 md:col-span-2">
                      <Label htmlFor="bio" className="flex items-center gap-2">
                        {intl.formatMessage({ id: 'user.profile.fields.bio' })}
                        {!editData?.bio && (
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <AlertCircle className="h-3 w-3 text-amber-500" />
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>
                                {intl.formatMessage({
                                  id: 'user.profile.tooltips.bio',
                                })}
                              </p>
                            </TooltipContent>
                          </Tooltip>
                        )}
                      </Label>
                      <Textarea
                        id="bio"
                        name="bio"
                        rows={4}
                        value={editData?.bio || ''}
                        onChange={handleChange}
                        disabled={!isEditing}
                        className={`resize-none transition-all duration-200 ${
                          isEditing
                            ? 'hover:border-primary/50 focus:ring-2'
                            : ''
                        } ${!editData?.bio ? 'border-amber-200' : ''}`}
                        placeholder={intl.formatMessage({
                          id: 'user.profile.placeholders.bio',
                        })}
                      />
                    </div>
                  </div>
                </form>
              </CardContent>
            </Card>

            {/* Enhanced Profile Stats Card */}
            <Card className="transition-shadow duration-200 hover:shadow-lg">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Info className="h-5 w-5" />
                  {intl.formatMessage({ id: 'user.profile.summary.title' })}
                </CardTitle>
                <CardDescription>
                  {intl.formatMessage({
                    id: 'user.profile.summary.description',
                  })}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Profile Completion Progress */}
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <h4 className="text-sm font-medium">
                      {intl.formatMessage({
                        id: 'user.profile.completion.title',
                      })}
                    </h4>
                    <span className="text-sm text-muted-foreground">
                      {profileCompletion}%
                    </span>
                  </div>
                  <Progress value={profileCompletion} className="w-full" />
                  <p className="text-xs text-muted-foreground">
                    {profileCompletion === 100
                      ? intl.formatMessage({
                          id: 'user.profile.completion.complete',
                        })
                      : intl.formatMessage(
                          { id: 'user.profile.completion.remaining' },
                          { count: Math.ceil((100 - profileCompletion) / 11) }
                        )}
                  </p>
                </div>

                <Separator />

                {/* Contact Information */}
                <div className="space-y-4">
                  <h4 className="flex items-center gap-2 text-sm font-medium">
                    {intl.formatMessage({
                      id: 'user.profile.sections.contactInformation',
                    })}
                    {(!userData.phone || !userData.location) && (
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <AlertCircle className="h-3 w-3 text-amber-500" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>
                            {intl.formatMessage({
                              id: 'user.profile.tooltips.completeContact',
                            })}
                          </p>
                        </TooltipContent>
                      </Tooltip>
                    )}
                  </h4>
                  <div className="space-y-3">
                    <HoverCard>
                      <HoverCardTrigger asChild>
                        <div className="flex cursor-pointer items-center gap-3 rounded-md p-2 text-sm transition-colors hover:bg-muted/50">
                          <Phone className="h-4 w-4 text-muted-foreground" />
                          <span
                            className={
                              !userData.phone
                                ? 'italic text-muted-foreground'
                                : ''
                            }
                          >
                            {userData.phone ||
                              intl.formatMessage({
                                id: 'user.profile.labels.noPhone',
                              })}
                          </span>
                          {userData.phone && (
                            <Badge
                              variant="outline"
                              className="ml-auto text-xs"
                            >
                              <CheckCircle2 className="mr-1 h-3 w-3" />
                              {intl.formatMessage({
                                id: 'user.profile.labels.set',
                              })}
                            </Badge>
                          )}
                        </div>
                      </HoverCardTrigger>
                      <HoverCardContent>
                        <p className="text-sm">
                          {userData.phone
                            ? intl.formatMessage({
                                id: 'user.profile.tooltips.phoneAvailable',
                              })
                            : intl.formatMessage({
                                id: 'user.profile.tooltips.addPhone',
                              })}
                        </p>
                      </HoverCardContent>
                    </HoverCard>

                    <HoverCard>
                      <HoverCardTrigger asChild>
                        <div className="flex cursor-pointer items-center gap-3 rounded-md p-2 text-sm transition-colors hover:bg-muted/50">
                          <MapPin className="h-4 w-4 text-muted-foreground" />
                          <span
                            className={
                              !userData.location
                                ? 'italic text-muted-foreground'
                                : ''
                            }
                          >
                            {userData.location ||
                              intl.formatMessage({
                                id: 'user.profile.labels.locationNotSet',
                              })}
                          </span>
                          {userData.location && (
                            <Badge
                              variant="outline"
                              className="ml-auto text-xs"
                            >
                              <CheckCircle2 className="mr-1 h-3 w-3" />
                              {intl.formatMessage({
                                id: 'user.profile.labels.set',
                              })}
                            </Badge>
                          )}
                        </div>
                      </HoverCardTrigger>
                      <HoverCardContent>
                        <p className="text-sm">
                          {userData.location
                            ? intl.formatMessage({
                                id: 'user.profile.tooltips.locationHelps',
                              })
                            : intl.formatMessage({
                                id: 'user.profile.tooltips.addLocation',
                              })}
                        </p>
                      </HoverCardContent>
                    </HoverCard>
                  </div>
                </div>

                <Separator />

                {/* Professional Information */}
                <div className="space-y-4">
                  <h4 className="flex items-center gap-2 text-sm font-medium">
                    {intl.formatMessage({
                      id: 'user.profile.sections.professionalDetails',
                    })}
                    {(!userData.jobTitle || !userData.company) && (
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <AlertCircle className="h-3 w-3 text-amber-500" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>
                            {intl.formatMessage({
                              id: 'user.profile.tooltips.completeProfessional',
                            })}
                          </p>
                        </TooltipContent>
                      </Tooltip>
                    )}
                  </h4>
                  <div className="space-y-3">
                    <HoverCard>
                      <HoverCardTrigger asChild>
                        <div className="flex cursor-pointer items-center gap-3 rounded-md p-2 text-sm transition-colors hover:bg-muted/50">
                          <Briefcase className="h-4 w-4 text-muted-foreground" />
                          <span
                            className={
                              !userData.jobTitle
                                ? 'italic text-muted-foreground'
                                : ''
                            }
                          >
                            {userData.jobTitle ||
                              intl.formatMessage({
                                id: 'user.profile.labels.jobTitleNotSet',
                              })}
                          </span>
                          {userData.jobTitle && (
                            <Badge
                              variant="outline"
                              className="ml-auto text-xs"
                            >
                              <CheckCircle2 className="mr-1 h-3 w-3" />
                              {intl.formatMessage({
                                id: 'user.profile.labels.set',
                              })}
                            </Badge>
                          )}
                        </div>
                      </HoverCardTrigger>
                      <HoverCardContent>
                        <p className="text-sm">
                          {userData.jobTitle
                            ? intl.formatMessage({
                                id: 'user.profile.tooltips.currentRole',
                              })
                            : intl.formatMessage({
                                id: 'user.profile.tooltips.addJobTitle',
                              })}
                        </p>
                      </HoverCardContent>
                    </HoverCard>

                    <HoverCard>
                      <HoverCardTrigger asChild>
                        <div className="flex cursor-pointer items-center gap-3 rounded-md p-2 text-sm transition-colors hover:bg-muted/50">
                          <Building className="h-4 w-4 text-muted-foreground" />
                          <span
                            className={
                              !userData.company
                                ? 'italic text-muted-foreground'
                                : ''
                            }
                          >
                            {userData.company ||
                              intl.formatMessage({
                                id: 'user.profile.labels.companyNotSet',
                              })}
                          </span>
                          {userData.company && (
                            <Badge
                              variant="outline"
                              className="ml-auto text-xs"
                            >
                              <CheckCircle2 className="mr-1 h-3 w-3" />
                              {intl.formatMessage({
                                id: 'user.profile.labels.set',
                              })}
                            </Badge>
                          )}
                        </div>
                      </HoverCardTrigger>
                      <HoverCardContent>
                        <p className="text-sm">
                          {userData.company
                            ? intl.formatMessage({
                                id: 'user.profile.tooltips.currentWorkplace',
                              })
                            : intl.formatMessage({
                                id: 'user.profile.tooltips.addCompany',
                              })}
                        </p>
                      </HoverCardContent>
                    </HoverCard>
                  </div>
                </div>

                <Separator />

                {/* Bio Section */}
                <div className="space-y-3">
                  <h4 className="flex items-center gap-2 text-sm font-medium">
                    {intl.formatMessage({
                      id: 'user.profile.sections.aboutYou',
                    })}
                    {!userData.bio && (
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <AlertCircle className="h-3 w-3 text-amber-500" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>
                            {intl.formatMessage({
                              id: 'user.profile.tooltips.bio',
                            })}
                          </p>
                        </TooltipContent>
                      </Tooltip>
                    )}
                  </h4>
                  <div className="rounded-lg bg-muted/30 p-3">
                    <p className="text-sm leading-relaxed text-muted-foreground">
                      {userData.bio || (
                        <span className="italic">
                          {intl.formatMessage({
                            id: 'user.profile.labels.noBio',
                          })}
                        </span>
                      )}
                    </p>
                  </div>
                </div>

                <Separator />

                {/* Account Info */}
                <div className="space-y-3">
                  <h4 className="text-sm font-medium">
                    {intl.formatMessage({
                      id: 'user.profile.sections.accountInformation',
                    })}
                  </h4>
                  <div className="space-y-2 text-xs text-muted-foreground">
                    <div className="flex items-center justify-between">
                      <span>
                        {intl.formatMessage({
                          id: 'user.profile.labels.memberSince',
                        })}
                        :
                      </span>
                      <Badge variant="secondary" className="text-xs">
                        {formatDate(userData.joinDate)}
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span>
                        {intl.formatMessage({
                          id: 'user.profile.labels.lastLogin',
                        })}
                        :
                      </span>
                      <span>{userData.lastLogin}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span>
                        {intl.formatMessage({
                          id: 'user.profile.labels.profileStatus',
                        })}
                        :
                      </span>
                      <Badge
                        variant={
                          profileCompletion === 100 ? 'default' : 'secondary'
                        }
                        className="gap-1 text-xs"
                      >
                        {profileCompletion === 100 ? (
                          <>
                            <CheckCircle2 className="h-3 w-3" />
                            {intl.formatMessage({
                              id: 'user.profile.status.complete',
                            })}
                          </>
                        ) : (
                          <>
                            <AlertCircle className="h-3 w-3" />
                            {intl.formatMessage({
                              id: 'user.profile.status.inProgress',
                            })}
                          </>
                        )}
                      </Badge>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </TooltipProvider>
    </ErrorBoundary>
  );
}

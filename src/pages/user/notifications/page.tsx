import { useState, useEffect } from 'react';
import {
  Bell,
  Settings,
  Filter,
  Archive,
  Trash2,
  MoreHorizontal,
  Clock,
  AlertCircle,
  Info,
  CheckCircle2,
  Star,
  Zap,
  Shield,
  Mail,
  MessageSquare,
  Activity,
} from 'lucide-react';
import { useIntl } from 'react-intl';
import { ErrorBoundary } from '@/components/base/boundary/error-boundary';
import { getUserNotifications } from '@/services/user';
import type { NotificationData } from '@/types/user';
import { Button } from '@/components/ui/shadcn/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/shadcn/card';
import { Badge } from '@/components/ui/shadcn/badge';
import { Separator } from '@/components/ui/shadcn/separator';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/shadcn/tabs';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/shadcn/dropdown-menu';
import { Switch } from '@/components/ui/shadcn/switch';
import { Label } from '@/components/ui/shadcn/label';
import { Progress } from '@/components/ui/shadcn/progress';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/shadcn/tooltip';
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from '@/components/ui/shadcn/hover-card';

// Interface for notification preferences
interface NotificationPreferences {
  email: boolean;
  sms: boolean;
  push: boolean;
  security: boolean;
  marketing: boolean;
}

/**
 * User Notifications Page Component
 *
 * Enhanced with professional enterprise-level shadcn/ui components.
 * Displays and manages user notifications including:
 * - Recent notifications with rich interactions
 * - Advanced notification preferences
 * - Detailed notification analytics
 */
export default function Page() {
  const intl = useIntl();
  const [notifications, setNotifications] = useState<NotificationData[]>([]);
  const [preferences, setPreferences] = useState<NotificationPreferences>({
    email: true,
    sms: false,
    push: true,
    security: true,
    marketing: false,
  });
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('all');

  // Load notifications data on component mount
  useEffect(() => {
    const loadNotifications = async () => {
      try {
        setIsLoading(true);
        const data = await getUserNotifications();
        setNotifications(data);
      } catch (error) {
        console.error('Failed to load notifications:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadNotifications();
  }, []);

  // Handle notification actions
  const handleMarkAsRead = (notificationId: number) => {
    setNotifications(prev =>
      prev.map(notification =>
        notification.id === notificationId
          ? { ...notification, read: true }
          : notification
      )
    );
  };

  const handleMarkAllAsRead = () => {
    setNotifications(prev =>
      prev.map(notification => ({
        ...notification,
        read: true,
      }))
    );
  };

  const handleDeleteNotification = (notificationId: number) => {
    setNotifications(prev =>
      prev.filter(notification => notification.id !== notificationId)
    );
  };

  const handlePreferenceChange = (
    key: keyof NotificationPreferences,
    value: boolean
  ) => {
    setPreferences(prev => ({
      ...prev,
      [key]: value,
    }));
  };

  // Format time display
  const formatTime = (timeString: string) => {
    const date = new Date(timeString);
    const now = new Date();
    const diffInHours = Math.floor(
      (now.getTime() - date.getTime()) / (1000 * 60 * 60)
    );

    if (diffInHours < 1)
      return intl.formatMessage({ id: 'user.notifications.time.justNow' });
    if (diffInHours < 24)
      return intl.formatMessage(
        { id: 'user.notifications.time.hoursAgo' },
        { hours: diffInHours }
      );
    return date.toLocaleDateString();
  };

  // Filter notifications based on active tab
  const getFilteredNotifications = () => {
    switch (activeTab) {
      case 'unread':
        return notifications.filter(n => !n.read);
      case 'important':
        return notifications.filter(
          n => n.type === 'system' || n.type === 'security'
        );
      default:
        return notifications;
    }
  };

  // Enhanced notification icon mapping with Lucide React icons
  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'system':
        return <Settings className="h-4 w-4 text-primary" />;
      case 'activity':
        return (
          <Activity className="h-4 w-4 text-emerald-500 dark:text-emerald-400" />
        );
      case 'mention':
        return (
          <MessageSquare className="h-4 w-4 text-purple-500 dark:text-purple-400" />
        );
      case 'security':
        return <Shield className="h-4 w-4 text-destructive" />;
      default:
        return <Bell className="h-4 w-4 text-muted-foreground" />;
    }
  };

  // Calculate notification engagement rate
  const calculateEngagementRate = () => {
    if (notifications.length === 0) return 0;
    const readCount = notifications.filter(n => n.read).length;
    return Math.round((readCount / notifications.length) * 100);
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="mb-6 h-8 w-1/4 rounded bg-muted"></div>
          <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
            <div className="space-y-4 lg:col-span-2">
              <div className="h-32 rounded bg-muted"></div>
              <div className="h-64 rounded bg-muted"></div>
            </div>
            <div className="h-80 rounded bg-muted"></div>
          </div>
        </div>
      </div>
    );
  }

  const unreadCount = notifications.filter(n => !n.read).length;
  const engagementRate = calculateEngagementRate();

  return (
    <ErrorBoundary
      level="page"
      showDetails={import.meta.env.MODE === 'development'}
    >
      <TooltipProvider>
        <div className="space-y-6">
          {/* Enhanced Page Header */}
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <h1 className="text-3xl font-bold tracking-tight">
                {intl.formatMessage({ id: 'user.notifications.title' })}
              </h1>
              <p className="text-muted-foreground">
                {intl.formatMessage({ id: 'user.notifications.subtitle' })}
              </p>
            </div>
            <div className="flex gap-2">
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="outline"
                    onClick={handleMarkAllAsRead}
                    disabled={unreadCount === 0}
                    className="gap-2"
                  >
                    <CheckCircle2 className="h-4 w-4" />
                    {intl.formatMessage({
                      id: 'user.notifications.actions.markAllRead',
                    })}
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>
                    {intl.formatMessage(
                      { id: 'user.notifications.tooltips.markAllRead' },
                      { count: unreadCount }
                    )}
                  </p>
                </TooltipContent>
              </Tooltip>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="outline" className="gap-2">
                    <Settings className="h-4 w-4" />
                    {intl.formatMessage({
                      id: 'user.notifications.actions.settings',
                    })}
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>
                    {intl.formatMessage({
                      id: 'user.notifications.tooltips.settings',
                    })}
                  </p>
                </TooltipContent>
              </Tooltip>
            </div>
          </div>

          {/* Enhanced Main Content */}
          <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
            {/* Enhanced Notifications List */}
            <Card className="transition-shadow duration-200 hover:shadow-lg lg:col-span-2">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      <Bell className="h-5 w-5" />
                      {intl.formatMessage({
                        id: 'user.notifications.sections.recent',
                      })}
                      {unreadCount > 0 && (
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Badge
                              variant="destructive"
                              className="animate-pulse gap-1"
                            >
                              <AlertCircle className="h-3 w-3" />
                              {unreadCount}
                            </Badge>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>
                              {intl.formatMessage(
                                {
                                  id: 'user.notifications.tooltips.unreadCount',
                                },
                                { count: unreadCount }
                              )}
                            </p>
                          </TooltipContent>
                        </Tooltip>
                      )}
                    </CardTitle>
                    <CardDescription>
                      {intl.formatMessage({
                        id: 'user.notifications.sections.recentDescription',
                      })}
                    </CardDescription>
                  </div>
                  <HoverCard>
                    <HoverCardTrigger asChild>
                      <Button variant="outline" size="sm" className="gap-2">
                        <Filter className="h-4 w-4" />
                        {intl.formatMessage({
                          id: 'user.notifications.actions.filter',
                        })}
                      </Button>
                    </HoverCardTrigger>
                    <HoverCardContent className="w-80">
                      <div className="space-y-3">
                        <h4 className="text-sm font-semibold">
                          {intl.formatMessage({
                            id: 'user.notifications.filters.title',
                          })}
                        </h4>
                        <div className="space-y-2 text-sm">
                          <p>
                            •{' '}
                            <strong>
                              {intl.formatMessage({
                                id: 'user.notifications.filters.all',
                              })}
                              :
                            </strong>{' '}
                            {intl.formatMessage({
                              id: 'user.notifications.filters.allDescription',
                            })}
                          </p>
                          <p>
                            •{' '}
                            <strong>
                              {intl.formatMessage({
                                id: 'user.notifications.filters.unread',
                              })}
                              :
                            </strong>{' '}
                            {intl.formatMessage({
                              id: 'user.notifications.filters.unreadDescription',
                            })}
                          </p>
                          <p>
                            •{' '}
                            <strong>
                              {intl.formatMessage({
                                id: 'user.notifications.filters.important',
                              })}
                              :
                            </strong>{' '}
                            {intl.formatMessage({
                              id: 'user.notifications.filters.importantDescription',
                            })}
                          </p>
                        </div>
                      </div>
                    </HoverCardContent>
                  </HoverCard>
                </div>
              </CardHeader>
              <CardContent>
                <Tabs value={activeTab} onValueChange={setActiveTab}>
                  <TabsList className="grid w-full grid-cols-3">
                    <TabsTrigger value="all" className="gap-2">
                      <Bell className="h-4 w-4" />
                      {intl.formatMessage({
                        id: 'user.notifications.tabs.all',
                      })}
                    </TabsTrigger>
                    <TabsTrigger value="unread" className="gap-2">
                      <AlertCircle className="h-4 w-4" />
                      {intl.formatMessage({
                        id: 'user.notifications.tabs.unread',
                      })}{' '}
                      {unreadCount > 0 && `(${unreadCount})`}
                    </TabsTrigger>
                    <TabsTrigger value="important" className="gap-2">
                      <Star className="h-4 w-4" />
                      {intl.formatMessage({
                        id: 'user.notifications.tabs.important',
                      })}
                    </TabsTrigger>
                  </TabsList>

                  <TabsContent value={activeTab} className="mt-6">
                    <div className="space-y-4">
                      {getFilteredNotifications().length === 0 ? (
                        <div className="py-12 text-center">
                          <div className="mx-auto mb-4 flex h-24 w-24 items-center justify-center rounded-full bg-muted">
                            <Bell className="h-8 w-8 text-muted-foreground" />
                          </div>
                          <h3 className="mb-2 text-lg font-medium">
                            {intl.formatMessage({
                              id: 'user.notifications.empty.title',
                            })}
                          </h3>
                          <p className="text-muted-foreground">
                            {activeTab === 'unread'
                              ? intl.formatMessage({
                                  id: 'user.notifications.empty.unread',
                                })
                              : intl.formatMessage({
                                  id: 'user.notifications.empty.general',
                                })}
                          </p>
                        </div>
                      ) : (
                        getFilteredNotifications().map(
                          (notification, index) => (
                            <div key={notification.id}>
                              <HoverCard>
                                <HoverCardTrigger asChild>
                                  <div
                                    className={`group flex cursor-pointer items-start gap-4 rounded-lg p-4 transition-all duration-200 ${
                                      !notification.read
                                        ? 'border border-primary/20 bg-primary/5 hover:bg-primary/10'
                                        : 'border border-transparent hover:border-border hover:bg-muted/50'
                                    }`}
                                  >
                                    <div className="mt-1 flex-shrink-0">
                                      <div
                                        className={`rounded-full p-2 ${
                                          !notification.read
                                            ? 'bg-primary/10'
                                            : 'bg-muted'
                                        }`}
                                      >
                                        {getNotificationIcon(notification.type)}
                                      </div>
                                    </div>
                                    <div className="min-w-0 flex-1">
                                      <div className="flex items-start justify-between">
                                        <div className="flex-1">
                                          <div className="flex items-center gap-2">
                                            <h4
                                              className={`line-clamp-1 text-sm font-medium ${
                                                !notification.read
                                                  ? 'text-foreground'
                                                  : 'text-muted-foreground'
                                              }`}
                                            >
                                              {notification.title}
                                            </h4>
                                            {!notification.read && (
                                              <div className="h-2 w-2 animate-pulse rounded-full bg-blue-500" />
                                            )}
                                          </div>
                                          <p className="mt-1 line-clamp-2 text-sm text-muted-foreground">
                                            {notification.message}
                                          </p>
                                          <div className="mt-3 flex items-center gap-3">
                                            <div className="flex items-center gap-1 text-xs text-muted-foreground">
                                              <Clock className="h-3 w-3" />
                                              {formatTime(notification.date)}
                                            </div>
                                            <Badge
                                              variant={
                                                notification.type === 'security'
                                                  ? 'destructive'
                                                  : 'outline'
                                              }
                                              className="gap-1 text-xs"
                                            >
                                              {notification.type ===
                                                'security' && (
                                                <Shield className="h-3 w-3" />
                                              )}
                                              {notification.type ===
                                                'system' && (
                                                <Settings className="h-3 w-3" />
                                              )}
                                              {notification.type ===
                                                'activity' && (
                                                <Activity className="h-3 w-3" />
                                              )}
                                              {notification.type ===
                                                'mention' && (
                                                <MessageSquare className="h-3 w-3" />
                                              )}
                                              {notification.type}
                                            </Badge>
                                            {notification.type ===
                                              'security' && (
                                              <Tooltip>
                                                <TooltipTrigger asChild>
                                                  <Badge
                                                    variant="destructive"
                                                    className="gap-1 text-xs"
                                                  >
                                                    <Zap className="h-3 w-3" />
                                                    {intl.formatMessage({
                                                      id: 'user.notifications.labels.highPriority',
                                                    })}
                                                  </Badge>
                                                </TooltipTrigger>
                                                <TooltipContent>
                                                  <p>
                                                    {intl.formatMessage({
                                                      id: 'user.notifications.tooltips.highPriority',
                                                    })}
                                                  </p>
                                                </TooltipContent>
                                              </Tooltip>
                                            )}
                                          </div>
                                        </div>
                                        <DropdownMenu>
                                          <DropdownMenuTrigger asChild>
                                            <Button
                                              variant="ghost"
                                              size="icon"
                                              className="h-8 w-8 opacity-0 transition-opacity group-hover:opacity-100"
                                            >
                                              <MoreHorizontal className="h-4 w-4" />
                                            </Button>
                                          </DropdownMenuTrigger>
                                          <DropdownMenuContent align="end">
                                            {!notification.read && (
                                              <DropdownMenuItem
                                                onClick={() =>
                                                  handleMarkAsRead(
                                                    notification.id
                                                  )
                                                }
                                              >
                                                <CheckCircle2 className="mr-2 h-4 w-4" />
                                                {intl.formatMessage({
                                                  id: 'user.notifications.actions.markAsRead',
                                                })}
                                              </DropdownMenuItem>
                                            )}
                                            <DropdownMenuItem>
                                              <Archive className="mr-2 h-4 w-4" />
                                              {intl.formatMessage({
                                                id: 'user.notifications.actions.archive',
                                              })}
                                            </DropdownMenuItem>
                                            <DropdownMenuItem
                                              onClick={() =>
                                                handleDeleteNotification(
                                                  notification.id
                                                )
                                              }
                                              className="text-destructive"
                                            >
                                              <Trash2 className="mr-2 h-4 w-4" />
                                              {intl.formatMessage({
                                                id: 'user.notifications.actions.delete',
                                              })}
                                            </DropdownMenuItem>
                                          </DropdownMenuContent>
                                        </DropdownMenu>
                                      </div>
                                    </div>
                                  </div>
                                </HoverCardTrigger>
                                <HoverCardContent className="w-80">
                                  <div className="space-y-3">
                                    <div className="flex items-center gap-2">
                                      {getNotificationIcon(notification.type)}
                                      <h4 className="text-sm font-semibold">
                                        {notification.title}
                                      </h4>
                                    </div>
                                    <p className="text-sm text-muted-foreground">
                                      {notification.message}
                                    </p>
                                    <div className="flex items-center justify-between text-xs text-muted-foreground">
                                      <span>
                                        {intl.formatMessage({
                                          id: 'user.notifications.labels.received',
                                        })}
                                        : {formatTime(notification.date)}
                                      </span>
                                      <Badge
                                        variant="outline"
                                        className="text-xs"
                                      >
                                        {notification.type}
                                      </Badge>
                                    </div>
                                  </div>
                                </HoverCardContent>
                              </HoverCard>
                              {index <
                                getFilteredNotifications().length - 1 && (
                                <Separator className="my-2" />
                              )}
                            </div>
                          )
                        )
                      )}
                    </div>
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>

            {/* Enhanced Notification Preferences */}
            <Card className="transition-shadow duration-200 hover:shadow-lg">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="h-5 w-5" />
                  {intl.formatMessage({
                    id: 'user.notifications.preferences.title',
                  })}
                </CardTitle>
                <CardDescription>
                  {intl.formatMessage({
                    id: 'user.notifications.preferences.description',
                  })}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Engagement Analytics */}
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <h4 className="text-sm font-medium">
                      {intl.formatMessage({
                        id: 'user.notifications.analytics.engagementRate',
                      })}
                    </h4>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Info className="h-4 w-4 cursor-help text-muted-foreground" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>
                          {intl.formatMessage({
                            id: 'user.notifications.tooltips.engagementRate',
                          })}
                        </p>
                      </TooltipContent>
                    </Tooltip>
                  </div>
                  <Progress value={engagementRate} className="w-full" />
                  <p className="text-xs text-muted-foreground">
                    {intl.formatMessage(
                      { id: 'user.notifications.analytics.readPercentage' },
                      { percentage: engagementRate }
                    )}
                  </p>
                </div>

                <Separator />

                {/* Enhanced Notification Controls */}
                <div className="space-y-4">
                  <h4 className="text-sm font-medium">
                    {intl.formatMessage({
                      id: 'user.notifications.preferences.deliveryMethods',
                    })}
                  </h4>
                  <div className="space-y-4">
                    <HoverCard>
                      <HoverCardTrigger asChild>
                        <div className="flex items-center justify-between rounded-lg border p-3 transition-colors hover:bg-muted/50">
                          <div className="flex items-center gap-3">
                            <Mail className="h-4 w-4 text-muted-foreground" />
                            <div className="space-y-0.5">
                              <Label className="cursor-pointer text-sm font-medium">
                                {intl.formatMessage({
                                  id: 'user.notifications.preferences.email',
                                })}
                              </Label>
                              <p className="text-xs text-muted-foreground">
                                {intl.formatMessage({
                                  id: 'user.notifications.preferences.emailDescription',
                                })}
                              </p>
                            </div>
                          </div>
                          <Switch
                            checked={preferences.email}
                            onCheckedChange={checked =>
                              handlePreferenceChange('email', checked)
                            }
                          />
                        </div>
                      </HoverCardTrigger>
                      <HoverCardContent>
                        <div className="space-y-2">
                          <h4 className="text-sm font-semibold">
                            {intl.formatMessage({
                              id: 'user.notifications.preferences.email',
                            })}
                          </h4>
                          <p className="text-sm text-muted-foreground">
                            {intl.formatMessage({
                              id: 'user.notifications.tooltips.emailNotifications',
                            })}
                          </p>
                        </div>
                      </HoverCardContent>
                    </HoverCard>

                    <HoverCard>
                      <HoverCardTrigger asChild>
                        <div className="flex items-center justify-between rounded-lg border p-3 transition-colors hover:bg-muted/50">
                          <div className="flex items-center gap-3">
                            <MessageSquare className="h-4 w-4 text-muted-foreground" />
                            <div className="space-y-0.5">
                              <Label className="cursor-pointer text-sm font-medium">
                                {intl.formatMessage({
                                  id: 'user.notifications.preferences.sms',
                                })}
                              </Label>
                              <p className="text-xs text-muted-foreground">
                                {intl.formatMessage({
                                  id: 'user.notifications.preferences.smsDescription',
                                })}
                              </p>
                            </div>
                          </div>
                          <Switch
                            checked={preferences.sms}
                            onCheckedChange={checked =>
                              handlePreferenceChange('sms', checked)
                            }
                          />
                        </div>
                      </HoverCardTrigger>
                      <HoverCardContent>
                        <div className="space-y-2">
                          <h4 className="text-sm font-semibold">
                            {intl.formatMessage({
                              id: 'user.notifications.preferences.sms',
                            })}
                          </h4>
                          <p className="text-sm text-muted-foreground">
                            {intl.formatMessage({
                              id: 'user.notifications.tooltips.smsNotifications',
                            })}
                          </p>
                        </div>
                      </HoverCardContent>
                    </HoverCard>

                    <HoverCard>
                      <HoverCardTrigger asChild>
                        <div className="flex items-center justify-between rounded-lg border p-3 transition-colors hover:bg-muted/50">
                          <div className="flex items-center gap-3">
                            <Bell className="h-4 w-4 text-muted-foreground" />
                            <div className="space-y-0.5">
                              <Label className="cursor-pointer text-sm font-medium">
                                {intl.formatMessage({
                                  id: 'user.notifications.preferences.push',
                                })}
                              </Label>
                              <p className="text-xs text-muted-foreground">
                                {intl.formatMessage({
                                  id: 'user.notifications.preferences.pushDescription',
                                })}
                              </p>
                            </div>
                          </div>
                          <Switch
                            checked={preferences.push}
                            onCheckedChange={checked =>
                              handlePreferenceChange('push', checked)
                            }
                          />
                        </div>
                      </HoverCardTrigger>
                      <HoverCardContent>
                        <div className="space-y-2">
                          <h4 className="text-sm font-semibold">
                            {intl.formatMessage({
                              id: 'user.notifications.preferences.push',
                            })}
                          </h4>
                          <p className="text-sm text-muted-foreground">
                            {intl.formatMessage({
                              id: 'user.notifications.tooltips.pushNotifications',
                            })}
                          </p>
                        </div>
                      </HoverCardContent>
                    </HoverCard>
                  </div>
                </div>

                <Separator />

                {/* Enhanced Content Preferences */}
                <div className="space-y-4">
                  <h4 className="text-sm font-medium">
                    {intl.formatMessage({
                      id: 'user.notifications.preferences.contentTypes',
                    })}
                  </h4>
                  <div className="space-y-4">
                    <HoverCard>
                      <HoverCardTrigger asChild>
                        <div className="flex items-center justify-between rounded-lg border p-3 transition-colors hover:bg-muted/50">
                          <div className="flex items-center gap-3">
                            <Shield className="h-4 w-4 text-red-500" />
                            <div className="space-y-0.5">
                              <Label className="flex cursor-pointer items-center gap-2 text-sm font-medium">
                                {intl.formatMessage({
                                  id: 'user.notifications.preferences.security',
                                })}
                                <Badge
                                  variant="destructive"
                                  className="text-xs"
                                >
                                  {intl.formatMessage({
                                    id: 'user.notifications.labels.critical',
                                  })}
                                </Badge>
                              </Label>
                              <p className="text-xs text-muted-foreground">
                                {intl.formatMessage({
                                  id: 'user.notifications.preferences.securityDescription',
                                })}
                              </p>
                            </div>
                          </div>
                          <Switch
                            checked={preferences.security}
                            onCheckedChange={checked =>
                              handlePreferenceChange('security', checked)
                            }
                          />
                        </div>
                      </HoverCardTrigger>
                      <HoverCardContent>
                        <div className="space-y-2">
                          <h4 className="text-sm font-semibold">
                            {intl.formatMessage({
                              id: 'user.notifications.preferences.security',
                            })}
                          </h4>
                          <p className="text-sm text-muted-foreground">
                            {intl.formatMessage({
                              id: 'user.notifications.tooltips.securityAlerts',
                            })}
                          </p>
                        </div>
                      </HoverCardContent>
                    </HoverCard>

                    <HoverCard>
                      <HoverCardTrigger asChild>
                        <div className="flex items-center justify-between rounded-lg border p-3 transition-colors hover:bg-muted/50">
                          <div className="flex items-center gap-3">
                            <Zap className="h-4 w-4 text-yellow-500" />
                            <div className="space-y-0.5">
                              <Label className="cursor-pointer text-sm font-medium">
                                {intl.formatMessage({
                                  id: 'user.notifications.preferences.marketing',
                                })}
                              </Label>
                              <p className="text-xs text-muted-foreground">
                                {intl.formatMessage({
                                  id: 'user.notifications.preferences.marketingDescription',
                                })}
                              </p>
                            </div>
                          </div>
                          <Switch
                            checked={preferences.marketing}
                            onCheckedChange={checked =>
                              handlePreferenceChange('marketing', checked)
                            }
                          />
                        </div>
                      </HoverCardTrigger>
                      <HoverCardContent>
                        <div className="space-y-2">
                          <h4 className="text-sm font-semibold">
                            {intl.formatMessage({
                              id: 'user.notifications.preferences.marketing',
                            })}
                          </h4>
                          <p className="text-sm text-muted-foreground">
                            {intl.formatMessage({
                              id: 'user.notifications.tooltips.marketingCommunications',
                            })}
                          </p>
                        </div>
                      </HoverCardContent>
                    </HoverCard>
                  </div>
                </div>

                <Separator />

                {/* Enhanced Quick Stats */}
                <div className="space-y-3">
                  <h4 className="flex items-center gap-2 text-sm font-medium">
                    {intl.formatMessage({
                      id: 'user.notifications.analytics.quickStats',
                    })}
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Info className="h-3 w-3 cursor-help text-muted-foreground" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>
                          {intl.formatMessage({
                            id: 'user.notifications.tooltips.quickStats',
                          })}
                        </p>
                      </TooltipContent>
                    </Tooltip>
                  </h4>
                  <div className="grid grid-cols-2 gap-4">
                    <HoverCard>
                      <HoverCardTrigger asChild>
                        <div className="cursor-pointer rounded-lg bg-muted/30 p-3 transition-colors hover:bg-muted/50">
                          <p className="text-xs text-muted-foreground">
                            {intl.formatMessage({
                              id: 'user.notifications.labels.total',
                            })}
                          </p>
                          <p className="text-xl font-bold">
                            {notifications.length}
                          </p>
                        </div>
                      </HoverCardTrigger>
                      <HoverCardContent>
                        <p className="text-sm">
                          {intl.formatMessage({
                            id: 'user.notifications.tooltips.totalNotifications',
                          })}
                        </p>
                      </HoverCardContent>
                    </HoverCard>
                    <HoverCard>
                      <HoverCardTrigger asChild>
                        <div className="cursor-pointer rounded-lg bg-muted/30 p-3 transition-colors hover:bg-muted/50">
                          <p className="text-xs text-muted-foreground">
                            {intl.formatMessage({
                              id: 'user.notifications.labels.unread',
                            })}
                          </p>
                          <p className="text-xl font-bold text-blue-600">
                            {unreadCount}
                          </p>
                        </div>
                      </HoverCardTrigger>
                      <HoverCardContent>
                        <p className="text-sm">
                          {intl.formatMessage({
                            id: 'user.notifications.tooltips.unreadNotifications',
                          })}
                        </p>
                      </HoverCardContent>
                    </HoverCard>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </TooltipProvider>
    </ErrorBoundary>
  );
}

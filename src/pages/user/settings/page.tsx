import { useState, useEffect } from 'react';
import { Settings2, <PERSON><PERSON>, Globe, Users, Check, X } from 'lucide-react';
import { useIntl } from 'react-intl';
import { ErrorBoundary } from '@/components/base/boundary/error-boundary';
import { getUserSettings } from '@/services/user';
import type { UserSettingsData } from '@/types/user';
import { Button } from '@/components/ui/shadcn/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/shadcn/card';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/shadcn/tabs';
import { Label } from '@/components/ui/shadcn/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/shadcn/select';
import { Badge } from '@/components/ui/shadcn/badge';
import { RadioGroup, RadioGroupItem } from '@/components/ui/shadcn/radio-group';
import { Separator } from '@/components/ui/shadcn/separator';
import {
  TooltipProvider,
  Tooltip,
  TooltipTrigger,
  TooltipContent,
} from '@/components/ui/shadcn/tooltip';
import { Progress } from '@/components/ui/shadcn/progress';

/**
 * User Settings Page Component
 *
 * Manages application and account settings including:
 * - Appearance and theme preferences
 * - Notification settings
 * - Language and localization
 * - Connected accounts and integrations
 * Data is fetched from API with fallback to mock data.
 */
export default function Page() {
  const intl = useIntl();
  const [userData, setUserData] = useState<UserSettingsData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);

  // Load user settings on component mount
  useEffect(() => {
    const loadUserSettings = async () => {
      try {
        setIsLoading(true);
        const settingsData = await getUserSettings();
        setUserData(settingsData);
      } catch (error) {
        console.error('Failed to load user settings:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadUserSettings();
  }, []);

  // Save settings handler
  const handleSaveSettings = async () => {
    if (!userData) return;

    try {
      setIsSaving(true);
      // In a real app, you would send the updated data to the backend
      console.log('Saving settings:', userData);

      // Mock API delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      alert(intl.formatMessage({ id: 'user.settings.notifications.saved' }));
    } catch (error) {
      console.error('Failed to save settings:', error);
      alert(intl.formatMessage({ id: 'user.settings.notifications.failed' }));
    } finally {
      setIsSaving(false);
    }
  };

  // Update settings field
  const updateSetting = (field: string, value: any) => {
    if (!userData) return;

    setUserData(prev => {
      if (!prev) return prev;

      if (field.includes('.')) {
        const [parent, child, subfield] = field.split('.');
        if (subfield) {
          // Handle three-level nesting (e.g., notifications.email.productUpdates)
          return {
            ...prev,
            [parent]: {
              ...(prev as any)[parent],
              [child]: {
                ...((prev as any)[parent] as any)[child],
                [subfield]: value,
              },
            },
          };
        } else {
          // Handle two-level nesting
          return {
            ...prev,
            [parent]: {
              ...(prev as any)[parent],
              [child]: value,
            },
          };
        }
      }

      return {
        ...prev,
        [field]: value,
      };
    });
  };

  // Toggle connected account
  const toggleConnectedAccount = (
    account: keyof UserSettingsData['connectedAccounts']
  ) => {
    if (!userData) return;

    setUserData(prev => ({
      ...prev!,
      connectedAccounts: {
        ...prev!.connectedAccounts,
        [account]: !prev!.connectedAccounts[account],
      },
    }));
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="mb-6 h-8 w-1/4 rounded bg-muted"></div>
          <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
            <div className="space-y-4 lg:col-span-2">
              <div className="h-32 rounded bg-muted"></div>
              <div className="h-96 rounded bg-muted"></div>
            </div>
            <div className="h-80 rounded bg-muted"></div>
          </div>
        </div>
      </div>
    );
  }

  if (!userData) {
    return (
      <div className="flex min-h-[400px] items-center justify-center">
        <div className="space-y-3 text-center">
          <div className="text-muted-foreground">
            {intl.formatMessage({ id: 'user.settings.loading.failed' })}
          </div>
          <Button variant="outline" onClick={() => window.location.reload()}>
            {intl.formatMessage({ id: 'actions.retry' })}
          </Button>
        </div>
      </div>
    );
  }

  return (
    <ErrorBoundary
      level="page"
      showDetails={import.meta.env.MODE === 'development'}
    >
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              {intl.formatMessage({ id: 'user.settings.title' })}
            </h1>
            <p className="text-muted-foreground">
              {intl.formatMessage({ id: 'user.settings.subtitle' })}
            </p>
          </div>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button onClick={handleSaveSettings} disabled={isSaving}>
                  {isSaving
                    ? intl.formatMessage({ id: 'actions.processing' })
                    : intl.formatMessage({ id: 'actions.save' })}
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>{intl.formatMessage({ id: 'user.settings.saveTooltip' })}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
          <Card className="lg:col-span-2">
            <CardContent className="p-0">
              <Tabs defaultValue="appearance" className="space-y-6">
                <div className="p-6 pb-0">
                  <TabsList className="grid w-full grid-cols-3">
                    <TabsTrigger
                      value="appearance"
                      className="flex items-center gap-2"
                    >
                      <Palette className="h-4 w-4" />
                      <span>
                        {intl.formatMessage({
                          id: 'user.settings.tabs.appearance',
                        })}
                      </span>
                    </TabsTrigger>
                    <TabsTrigger
                      value="language"
                      className="flex items-center gap-2"
                    >
                      <Globe className="h-4 w-4" />
                      <span>
                        {intl.formatMessage({
                          id: 'user.settings.tabs.language',
                        })}
                      </span>
                    </TabsTrigger>
                    <TabsTrigger
                      value="integrations"
                      className="flex items-center gap-2"
                    >
                      <Users className="h-4 w-4" />
                      <span>
                        {intl.formatMessage({
                          id: 'user.settings.tabs.integrations',
                        })}
                      </span>
                    </TabsTrigger>
                  </TabsList>
                </div>

                {/* Appearance Settings */}
                <TabsContent value="appearance" className="space-y-6 px-6 pb-6">
                  <div>
                    <h3 className="mb-2 text-lg font-medium">
                      {intl.formatMessage({
                        id: 'user.settings.appearance.title',
                      })}
                    </h3>
                    <p className="mb-4 text-sm text-muted-foreground">
                      {intl.formatMessage({
                        id: 'user.settings.appearance.description',
                      })}
                    </p>

                    <RadioGroup
                      value={userData.theme}
                      onValueChange={value => updateSetting('theme', value)}
                      className="space-y-3"
                    >
                      <div className="flex items-center space-x-3 rounded-lg border p-4 transition-colors hover:bg-accent/50">
                        <RadioGroupItem value="system" id="theme-system" />
                        <div className="flex-1">
                          <Label
                            htmlFor="theme-system"
                            className="cursor-pointer font-medium"
                          >
                            {intl.formatMessage({
                              id: 'user.settings.appearance.theme.system',
                            })}
                          </Label>
                          <p className="text-sm text-muted-foreground">
                            {intl.formatMessage({
                              id: 'user.settings.appearance.theme.system.description',
                            })}
                          </p>
                        </div>
                      </div>

                      <div className="flex items-center space-x-3 rounded-lg border p-4 transition-colors hover:bg-accent/50">
                        <RadioGroupItem value="light" id="theme-light" />
                        <div className="flex-1">
                          <Label
                            htmlFor="theme-light"
                            className="cursor-pointer font-medium"
                          >
                            {intl.formatMessage({
                              id: 'user.settings.appearance.theme.light',
                            })}
                          </Label>
                          <p className="text-sm text-muted-foreground">
                            {intl.formatMessage({
                              id: 'user.settings.appearance.theme.light.description',
                            })}
                          </p>
                        </div>
                      </div>

                      <div className="flex items-center space-x-3 rounded-lg border p-4 transition-colors hover:bg-accent/50">
                        <RadioGroupItem value="dark" id="theme-dark" />
                        <div className="flex-1">
                          <Label
                            htmlFor="theme-dark"
                            className="cursor-pointer font-medium"
                          >
                            {intl.formatMessage({
                              id: 'user.settings.appearance.theme.dark',
                            })}
                          </Label>
                          <p className="text-sm text-muted-foreground">
                            {intl.formatMessage({
                              id: 'user.settings.appearance.theme.dark.description',
                            })}
                          </p>
                        </div>
                      </div>
                    </RadioGroup>
                  </div>
                </TabsContent>

                {/* Language Settings */}
                <TabsContent value="language" className="space-y-6 px-6 pb-6">
                  <div>
                    <h3 className="mb-2 text-lg font-medium">
                      {intl.formatMessage({
                        id: 'user.settings.language.title',
                      })}
                    </h3>
                    <p className="mb-4 text-sm text-muted-foreground">
                      {intl.formatMessage({
                        id: 'user.settings.language.description',
                      })}
                    </p>
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="language">
                          {intl.formatMessage({
                            id: 'user.settings.language.interface',
                          })}
                        </Label>
                        <Select
                          value={userData.language}
                          onValueChange={value =>
                            updateSetting('language', value)
                          }
                        >
                          <SelectTrigger id="language">
                            <SelectValue
                              placeholder={intl.formatMessage({
                                id: 'user.settings.language.selectLanguage',
                              })}
                            />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="en">🇺🇸 English</SelectItem>
                            <SelectItem value="es">
                              🇪🇸 Español (Spanish)
                            </SelectItem>
                            <SelectItem value="fr">
                              🇫🇷 Français (French)
                            </SelectItem>
                            <SelectItem value="de">
                              🇩🇪 Deutsch (German)
                            </SelectItem>
                            <SelectItem value="ja">
                              🇯🇵 日本語 (Japanese)
                            </SelectItem>
                            <SelectItem value="zh">
                              🇨🇳 中文 (Chinese)
                            </SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="timezone">
                          {intl.formatMessage({
                            id: 'user.settings.language.timezone',
                          })}
                        </Label>
                        <Select
                          value={userData.timezone}
                          onValueChange={value =>
                            updateSetting('timezone', value)
                          }
                        >
                          <SelectTrigger id="timezone">
                            <SelectValue
                              placeholder={intl.formatMessage({
                                id: 'user.settings.language.selectTimezone',
                              })}
                            />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="America/Los_Angeles">
                              🇺🇸 Pacific Time (US & Canada)
                            </SelectItem>
                            <SelectItem value="America/Denver">
                              🇺🇸 Mountain Time (US & Canada)
                            </SelectItem>
                            <SelectItem value="America/Chicago">
                              🇺🇸 Central Time (US & Canada)
                            </SelectItem>
                            <SelectItem value="America/New_York">
                              🇺🇸 Eastern Time (US & Canada)
                            </SelectItem>
                            <SelectItem value="UTC">🌍 UTC</SelectItem>
                            <SelectItem value="Europe/London">
                              🇬🇧 London
                            </SelectItem>
                            <SelectItem value="Europe/Paris">
                              🇫🇷 Paris
                            </SelectItem>
                            <SelectItem value="Europe/Berlin">
                              🇩🇪 Berlin
                            </SelectItem>
                            <SelectItem value="Asia/Tokyo">🇯🇵 Tokyo</SelectItem>
                            <SelectItem value="Asia/Shanghai">
                              🇨🇳 Shanghai
                            </SelectItem>
                            <SelectItem value="Australia/Sydney">
                              🇦🇺 Sydney
                            </SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </div>
                </TabsContent>

                {/* Integration Settings */}
                <TabsContent
                  value="integrations"
                  className="space-y-6 px-6 pb-6"
                >
                  <div>
                    <h3 className="mb-2 text-lg font-medium">
                      {intl.formatMessage({
                        id: 'user.settings.integrations.title',
                      })}
                    </h3>
                    <p className="mb-4 text-sm text-muted-foreground">
                      {intl.formatMessage({
                        id: 'user.settings.integrations.description',
                      })}
                    </p>
                    <div className="space-y-3">
                      {[
                        {
                          key: 'google',
                          name: 'Google',
                          description: intl.formatMessage({
                            id: 'user.settings.integrations.google.description',
                          }),
                          icon: '🔴',
                          color: 'bg-red-100 text-red-600',
                        },
                        {
                          key: 'github',
                          name: 'GitHub',
                          description: intl.formatMessage({
                            id: 'user.settings.integrations.github.description',
                          }),
                          icon: '⚫',
                          color: 'bg-gray-900 text-white',
                        },
                        {
                          key: 'linkedin',
                          name: 'LinkedIn',
                          description: intl.formatMessage({
                            id: 'user.settings.integrations.linkedin.description',
                          }),
                          icon: '🔵',
                          color: 'bg-primary/10 text-primary',
                        },
                        {
                          key: 'slack',
                          name: 'Slack',
                          description: intl.formatMessage({
                            id: 'user.settings.integrations.slack.description',
                          }),
                          icon: '🟢',
                          color: 'bg-green-100 text-green-600',
                        },
                      ].map(service => {
                        const isConnected =
                          userData.connectedAccounts[
                            service.key as keyof typeof userData.connectedAccounts
                          ];
                        return (
                          <Card
                            key={service.key}
                            className="transition-all duration-200 hover:shadow-md"
                          >
                            <CardContent className="flex items-center justify-between p-4">
                              <div className="flex items-center space-x-3">
                                <div
                                  className={`flex h-10 w-10 items-center justify-center rounded-full ${service.color}`}
                                >
                                  <span className="text-lg">
                                    {service.icon}
                                  </span>
                                </div>
                                <div>
                                  <p className="font-medium">{service.name}</p>
                                  <p className="text-sm text-muted-foreground">
                                    {service.description}
                                  </p>
                                </div>
                              </div>
                              <div className="flex items-center space-x-3">
                                {isConnected && (
                                  <TooltipProvider>
                                    <Tooltip>
                                      <TooltipTrigger asChild>
                                        <Badge
                                          variant="secondary"
                                          className="bg-green-100 text-green-700"
                                        >
                                          <Check className="mr-1 h-3 w-3" />
                                          {intl.formatMessage({
                                            id: 'user.settings.integrations.connected',
                                          })}
                                        </Badge>
                                      </TooltipTrigger>
                                      <TooltipContent>
                                        <p>
                                          {intl.formatMessage(
                                            {
                                              id: 'user.settings.integrations.connectedTooltip',
                                            },
                                            { service: service.name }
                                          )}
                                        </p>
                                      </TooltipContent>
                                    </Tooltip>
                                  </TooltipProvider>
                                )}
                                <Button
                                  variant={isConnected ? 'outline' : 'default'}
                                  size="sm"
                                  onClick={() =>
                                    toggleConnectedAccount(
                                      service.key as keyof typeof userData.connectedAccounts
                                    )
                                  }
                                  className="transition-all duration-200"
                                >
                                  {isConnected ? (
                                    <>
                                      <X className="mr-1 h-3 w-3" />
                                      {intl.formatMessage({
                                        id: 'user.settings.integrations.disconnect',
                                      })}
                                    </>
                                  ) : (
                                    <>
                                      <Check className="mr-1 h-3 w-3" />
                                      {intl.formatMessage({
                                        id: 'user.settings.integrations.connect',
                                      })}
                                    </>
                                  )}
                                </Button>
                              </div>
                            </CardContent>
                          </Card>
                        );
                      })}
                    </div>
                  </div>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>

          {/* Settings Summary Card */}
          <Card className="h-fit">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings2 className="h-5 w-5" />
                {intl.formatMessage({ id: 'user.settings.overview.title' })}
              </CardTitle>
              <CardDescription>
                {intl.formatMessage({
                  id: 'user.settings.overview.description',
                })}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label className="text-sm font-medium">
                      {intl.formatMessage({
                        id: 'user.settings.appearance.theme',
                      })}
                    </Label>
                    <Badge variant="outline" className="capitalize">
                      {intl.formatMessage({
                        id: `user.settings.appearance.theme.${userData.theme}`,
                      })}
                    </Badge>
                  </div>
                  <Progress value={33} className="h-1" />
                </div>

                <Separator />

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label className="text-sm font-medium">
                      {intl.formatMessage({
                        id: 'user.settings.language.interface',
                      })}
                    </Label>
                    <Badge variant="outline">
                      {userData.language === 'en'
                        ? '🇺🇸 EN'
                        : userData.language === 'es'
                          ? '🇪🇸 ES'
                          : userData.language === 'fr'
                            ? '🇫🇷 FR'
                            : userData.language === 'de'
                              ? '🇩🇪 DE'
                              : userData.language === 'ja'
                                ? '🇯🇵 JA'
                                : userData.language === 'zh'
                                  ? '🇨🇳 ZH'
                                  : '🇺🇸 EN'}
                    </Badge>
                  </div>
                  <Progress value={66} className="h-1" />
                </div>

                <Separator />

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label className="text-sm font-medium">
                      {intl.formatMessage({
                        id: 'user.settings.integrations.connectedServices',
                      })}
                    </Label>
                    <Badge variant="outline">
                      {
                        Object.values(userData.connectedAccounts).filter(
                          Boolean
                        ).length
                      }
                      /4
                    </Badge>
                  </div>
                  <Progress
                    value={
                      (Object.values(userData.connectedAccounts).filter(Boolean)
                        .length /
                        4) *
                      100
                    }
                    className="h-1"
                  />
                </div>
              </div>

              <Separator />

              <div className="space-y-3">
                <Label className="text-sm font-medium">
                  {intl.formatMessage({
                    id: 'user.settings.integrations.activeIntegrations',
                  })}
                </Label>
                <div className="space-y-2">
                  {Object.entries(userData.connectedAccounts).map(
                    ([key, connected]) =>
                      connected && (
                        <div
                          key={key}
                          className="flex items-center justify-between text-sm"
                        >
                          <span className="capitalize">{key}</span>
                          <Badge variant="secondary" className="text-xs">
                            <Check className="mr-1 h-3 w-3" />
                            {intl.formatMessage({
                              id: 'user.settings.integrations.active',
                            })}
                          </Badge>
                        </div>
                      )
                  )}
                  {Object.values(userData.connectedAccounts).every(
                    val => !val
                  ) && (
                    <p className="text-sm text-muted-foreground">
                      {intl.formatMessage({
                        id: 'user.settings.integrations.noConnected',
                      })}
                    </p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </ErrorBoundary>
  );
}

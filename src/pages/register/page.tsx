import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/shadcn/button';
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/shadcn/card';
import { Input } from '@/components/ui/shadcn/input';
import { PasswordInput } from '@/components/features/login/password-input';
import { Label } from '@/components/ui/shadcn/label';
import useAuth from '@/hooks/use-auth';
import { useIntl } from 'react-intl';
import { ArrowLeft } from 'lucide-react';
import { FormErrorBoundary } from '@/components/base/boundary/form-error-boundary';
import '@/styles/animations.css'; // Import the centralized animations

export default function Register() {
  const navigate = useNavigate();
  const { register } = useAuth();
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const intl = useIntl();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await register(email, password, firstName, lastName);
      navigate('/login');
    } catch (error) {
      console.error(error);
    }
  };

  const handleBack = () => {
    navigate(-1); // Navigate to the previous page in history
  };

  return (
    <div className="flex h-screen w-full flex-col items-center justify-center px-4">
      <div className="mb-2 w-full max-w-[350px]">
        <Button
          variant="ghost"
          size="sm"
          className="animate-fadeIn flex items-center gap-1 hover:bg-transparent hover:opacity-80"
          onClick={handleBack}
          aria-label={intl.formatMessage({ id: 'back' })}
        >
          <ArrowLeft className="h-4 w-4" />
          <span>{intl.formatMessage({ id: 'back' })}</span>
        </Button>
      </div>
      <Card className="animate-fadeIn w-full max-w-[350px]">
        <CardHeader className="pb-4">
          <CardTitle className="animate-slideUp">
            {intl.formatMessage({ id: 'page.register.title' })}
          </CardTitle>
          <CardDescription className="animate-slideUp animation-delay-100">
            {intl.formatMessage({ id: 'page.register.description' })}
          </CardDescription>
        </CardHeader>
        <FormErrorBoundary>
          <form onSubmit={handleSubmit}>
            <CardContent>
              <div className="grid w-full items-center gap-4">
                <div className="animate-fadeIn animation-delay-100 flex flex-col space-y-1.5">
                  <Label htmlFor="firstName">
                    {intl.formatMessage({ id: 'page.register.firstName' })}
                  </Label>
                  <Input
                    id="firstName"
                    value={firstName}
                    onChange={e => setFirstName(e.target.value)}
                  />
                </div>
                <div className="animate-fadeIn animation-delay-200 flex flex-col space-y-1.5">
                  <Label htmlFor="lastName">
                    {intl.formatMessage({ id: 'page.register.lastName' })}
                  </Label>
                  <Input
                    id="lastName"
                    value={lastName}
                    onChange={e => setLastName(e.target.value)}
                  />
                </div>
                <div className="animate-fadeIn animation-delay-200 flex flex-col space-y-1.5">
                  <Label htmlFor="email">
                    {intl.formatMessage({ id: 'page.register.email' })}
                  </Label>
                  <Input
                    id="email"
                    type="email"
                    value={email}
                    onChange={e => setEmail(e.target.value)}
                  />
                </div>
                <div className="animate-fadeIn animation-delay-300 flex flex-col space-y-1.5">
                  <Label htmlFor="password">
                    {intl.formatMessage({ id: 'page.register.password' })}
                  </Label>
                  <PasswordInput
                    id="password"
                    value={password}
                    onChange={e => setPassword(e.target.value)}
                  />
                </div>
              </div>
            </CardContent>
            <CardFooter className="animate-fadeIn animation-delay-300 flex flex-col gap-4">
              <Button
                className="shimmer-effect w-full bg-primary text-primary-foreground hover:bg-primary/90"
                type="submit"
              >
                {intl.formatMessage({ id: 'page.register.submit' })}
              </Button>
              <Button
                variant="link"
                className="shimmer-effect text-primary hover:text-primary/90"
                onClick={() => navigate('/login')}
              >
                {intl.formatMessage({ id: 'page.register.haveAccount' })}
              </Button>
            </CardFooter>
          </form>
        </FormErrorBoundary>
      </Card>
    </div>
  );
}

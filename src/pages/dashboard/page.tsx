import { ErrorBoundary } from '@/components/base/boundary/error-boundary';

export default function DashboardPage() {
  return (
    <ErrorBoundary
      level="page"
      showDetails={import.meta.env.MODE === 'development'}
    >
      <div className="flex flex-1 flex-col gap-4 p-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
            <p className="text-muted-foreground">
              Welcome to your dashboard overview.
            </p>
          </div>
        </div>
        <div className="grid auto-rows-min gap-4 md:grid-cols-3">
          <div className="aspect-video rounded-xl bg-muted/50" />
          <div className="aspect-video rounded-xl bg-muted/50" />
          <div className="aspect-video rounded-xl bg-muted/50" />
        </div>
        <div className="min-h-[100vh] flex-1 rounded-xl bg-muted/50 md:min-h-min" />
      </div>
    </ErrorBoundary>
  );
}

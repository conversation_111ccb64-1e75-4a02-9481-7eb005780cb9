import { SITE_INFO, PAGE_METADATA } from '@/constants/site-config';
import { useEffect, useRef, useState } from 'react';
import { updatePageMetadata } from '@/utils';
import { useIntl } from 'react-intl';
import { termsOfServiceContent } from '@/data/public/policies';
import MarkdownRender from '@/components/common/markdown-renderer-lazy';
import useConfig from '@/hooks/use-config';
import '@/styles/animations.css'; // Import the centralized animations

/**
 * Terms of Service Page
 *
 * Displays the company's terms of service using data from the site configuration.
 * This page outlines the rules, guidelines, and legal agreements between the user and the company.
 * Supports multiple languages based on the user's selected locale.
 * Enhanced with animations for better user experience.
 */
export default function Page() {
  const intl = useIntl();
  const { locale } = useConfig();
  const metadata = PAGE_METADATA.terms;
  const lastUpdated = SITE_INFO.legal.termsLastUpdated;
  const companyName = SITE_INFO.organization.name;
  const email = SITE_INFO.contact.email.general;

  // State to track if content is visible
  const [contentVisible, setContentVisible] = useState(false);
  const contentRef = useRef(null);

  // Add intersection observer to check when content enters viewport
  useEffect(() => {
    const observerOptions = {
      root: null,
      rootMargin: '0px',
      threshold: 0.1,
    };

    const observerCallback = (entries: IntersectionObserverEntry[]) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          setContentVisible(true);
        }
      });
    };

    const observer = new IntersectionObserver(
      observerCallback,
      observerOptions
    );

    if (contentRef.current) {
      observer.observe(contentRef.current);
    }

    return () => {
      observer.disconnect();
    };
  }, []);

  // Get the appropriate content based on locale
  const getLocalizedContent = () => {
    // First check if the current locale is available
    if (locale in termsOfServiceContent.content) {
      const content =
        termsOfServiceContent.content[
          locale as keyof typeof termsOfServiceContent.content
        ];
      if (content) return content;
    }

    // Try to find content for the current locale's language without region
    const language = intl.locale.split('-')[0];
    if (language !== intl.locale && language in termsOfServiceContent.content) {
      const content =
        termsOfServiceContent.content[
          language as keyof typeof termsOfServiceContent.content
        ];
      if (content) return content;
    }

    // Default to English
    return termsOfServiceContent.content.en;
  };

  // Get localized title based on current locale
  const getLocalizedTitle = () => {
    if (termsOfServiceContent.titleTranslations) {
      // First check exact locale match
      if (locale in termsOfServiceContent.titleTranslations) {
        const title =
          termsOfServiceContent.titleTranslations[
            locale as keyof typeof termsOfServiceContent.titleTranslations
          ];
        if (title) return title;
      }

      // Try language without region
      const language = intl.locale.split('-')[0];
      if (
        language !== intl.locale &&
        language in termsOfServiceContent.titleTranslations
      ) {
        const title =
          termsOfServiceContent.titleTranslations[
            language as keyof typeof termsOfServiceContent.titleTranslations
          ];
        if (title) return title;
      }
    }

    return termsOfServiceContent.title;
  };

  // Update document metadata
  useEffect(() => {
    const translatedTitle = intl.formatMessage({ id: 'page.terms.title' });
    const translatedDescription = intl.formatMessage({
      id: 'page.terms.subtitle',
    });

    updatePageMetadata({
      ...metadata,
      title: translatedTitle,
      description: translatedDescription,
    });
  }, [metadata, intl]);

  return (
    <div
      ref={contentRef}
      className={`section-reveal ${contentVisible ? 'visible' : ''}`}
    >
      <div className="animate-fadeIn">
        <MarkdownRender
          content={getLocalizedContent()}
          variables={{
            companyName: companyName,
            email: email,
          }}
          title={getLocalizedTitle()}
          lastUpdated={lastUpdated}
          className="animate-fadeIn"
        />
      </div>
    </div>
  );
}

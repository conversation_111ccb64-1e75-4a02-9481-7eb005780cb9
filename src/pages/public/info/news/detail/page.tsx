import { PAGE_METADATA } from '@/constants/site-config';
import { useState, useEffect, useRef } from 'react';
import { updatePageMetadata } from '@/utils';
import { useParams, Link, useNavigate } from 'react-router-dom';
import { useIntl } from 'react-intl';
import {
  Calendar,
  Clock,
  Tag,
  Share2,
  ArrowLeft,
  ChevronRight,
} from 'lucide-react';
import { getNewsBySlug, getRelatedNews } from '@/data/public/news';
import { NewsArticle } from '@/types/public/news';
import MarkdownRenderer from '@/components/common/markdown-renderer';
import '@/styles/animations.css';

/**
 * News Detail Page Component
 *
 * Displays a single news article with full content.
 * Features include:
 * - Full article content with markdown rendering
 * - Author information and bio
 * - Social sharing functionality
 * - Related articles section
 *
 * @returns {JSX.Element} The News Detail Page component
 */
export default function Page() {
  const { slug } = useParams<{ slug: string }>();
  const intl = useIntl();
  const navigate = useNavigate();
  const locale = intl.locale;
  const metadata = PAGE_METADATA.newsDetail;

  const [article, setArticle] = useState<NewsArticle | null>(null);
  const [relatedArticles, setRelatedArticles] = useState<NewsArticle[]>([]);
  const [showShareMenu, setShowShareMenu] = useState(false);

  // State to track which sections are visible
  const [visibleSections, setVisibleSections] = useState({
    header: true,
    content: true,
    author: true,
    related: true,
  });

  // Refs for the sections
  const headerRef = useRef(null);
  const contentRef = useRef(null);
  const authorRef = useRef(null);
  const relatedRef = useRef(null);

  useEffect(() => {
    if (slug) {
      const foundArticle = getNewsBySlug(slug);
      if (foundArticle) {
        setArticle(foundArticle);
        setRelatedArticles(getRelatedNews(foundArticle));

        // Update page metadata with article-specific info
        const dynamicMetadata = {
          ...metadata,
          title:
            foundArticle.title[locale as keyof typeof foundArticle.title] ||
            intl.formatMessage({
              id: 'news.notFound',
              defaultMessage: 'Article Not Found',
            }),
          description:
            foundArticle.excerpt[locale as keyof typeof foundArticle.excerpt] ||
            metadata.description,
          ogImage: foundArticle.featuredImage || metadata.ogImage,
        };
        updatePageMetadata(dynamicMetadata);
      }
    }
  }, [slug, locale, metadata, intl]);

  // Add intersection observer to check when sections are visible
  useEffect(() => {
    const observerOptions = {
      root: null,
      rootMargin: '0px',
      threshold: 0.1,
    };

    const observerCallback = (entries: IntersectionObserverEntry[]) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const sectionId = entry.target.getAttribute('data-section');
          if (sectionId) {
            setVisibleSections(prev => ({
              ...prev,
              [sectionId]: true,
            }));
          }
        }
      });
    };

    const observer = new IntersectionObserver(
      observerCallback,
      observerOptions
    );

    // Observe each section
    if (headerRef.current) observer.observe(headerRef.current);
    if (contentRef.current) observer.observe(contentRef.current);
    if (authorRef.current) observer.observe(authorRef.current);
    if (relatedRef.current) observer.observe(relatedRef.current);

    return () => {
      observer.disconnect();
    };
  }, []);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString(intl.locale, {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const handleShare = (platform: string) => {
    const url = window.location.href;
    const title =
      article?.title[intl.locale as keyof typeof article.title] || '';

    let shareUrl = '';
    switch (platform) {
      case 'twitter':
        shareUrl = `https://twitter.com/intent/tweet?url=${encodeURIComponent(url)}&text=${encodeURIComponent(title)}`;
        break;
      case 'linkedin':
        shareUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(url)}`;
        break;
      case 'facebook':
        shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`;
        break;
      case 'copy':
        navigator.clipboard.writeText(url);
        setShowShareMenu(false);
        return;
    }

    if (shareUrl) {
      window.open(shareUrl, '_blank', 'width=600,height=400');
      setShowShareMenu(false);
    }
  };

  if (!article) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-background">
        <div className="animate-fadeIn text-center">
          <h1 className="mb-4 text-2xl font-bold text-foreground">
            {intl.formatMessage({
              id: 'news.notFound',
              defaultMessage: 'Article Not Found',
            })}
          </h1>
          <p className="mb-6 text-muted-foreground">
            {intl.formatMessage({
              id: 'news.notFoundDescription',
              defaultMessage: 'The article you are looking for does not exist.',
            })}
          </p>
          <Link
            to="/news"
            className="inline-flex items-center rounded-lg bg-primary px-4 py-2 text-primary-foreground transition-colors hover:bg-primary/90"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            {intl.formatMessage({
              id: 'news.backToNews',
              defaultMessage: 'Back to News',
            })}
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="border-b border-border bg-card">
        <div className="mx-auto max-w-4xl px-4 py-4 sm:px-6 lg:px-8">
          <button
            onClick={() => navigate('/news')}
            className="animate-fadeIn inline-flex items-center text-muted-foreground transition-colors hover:text-foreground"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            {intl.formatMessage({
              id: 'news.backToNews',
              defaultMessage: 'Back to News',
            })}
          </button>
        </div>
      </div>

      {/* Article */}
      <article className="mx-auto max-w-4xl px-4 py-12 sm:px-6 lg:px-8">
        {/* Article Header */}
        <header
          ref={headerRef}
          data-section="header"
          className={`mb-8 transition-all duration-1000 ${visibleSections.header ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}`}
        >
          <div className="mb-4 flex items-center text-sm text-muted-foreground">
            <span className="mr-4 rounded-full bg-primary/10 px-3 py-1 text-xs font-medium text-primary">
              {
                article.category.name[
                  intl.locale as keyof typeof article.category.name
                ]
              }
            </span>
            <Calendar className="mr-1 h-4 w-4" />
            <span>{formatDate(article.publishedAt)}</span>
            <span className="mx-2">•</span>
            <Clock className="mr-1 h-4 w-4" />
            <span>{article.readTime} min read</span>
          </div>

          <h1 className="mb-6 text-3xl font-bold text-foreground md:text-4xl">
            {article.title[intl.locale as keyof typeof article.title]}
          </h1>

          <p className="mb-6 text-xl text-muted-foreground">
            {article.excerpt[intl.locale as keyof typeof article.excerpt]}
          </p>

          {/* Author and Share */}
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <img
                src={article.author.avatar}
                alt={article.author.name}
                className="mr-4 h-12 w-12 rounded-full"
              />
              <div>
                <p className="font-medium text-foreground">
                  {article.author.name}
                </p>
                <p className="text-sm text-muted-foreground">
                  {
                    article.author.title[
                      intl.locale as keyof typeof article.author.title
                    ]
                  }
                </p>
              </div>
            </div>

            <div className="relative">
              <button
                onClick={() => setShowShareMenu(!showShareMenu)}
                className="flex items-center rounded-lg border border-border px-4 py-2 text-muted-foreground transition-colors hover:bg-muted hover:text-foreground"
              >
                <Share2 className="mr-2 h-4 w-4" />
                {intl.formatMessage({
                  id: 'common.share',
                  defaultMessage: 'Share',
                })}
              </button>

              {showShareMenu && (
                <div className="animate-slideUp absolute right-0 z-10 mt-2 w-48 rounded-lg border border-border bg-card shadow-lg">
                  <div className="py-2">
                    <button
                      onClick={() => handleShare('twitter')}
                      className="w-full px-4 py-2 text-left text-foreground transition-colors hover:bg-muted"
                    >
                      Share on Twitter
                    </button>
                    <button
                      onClick={() => handleShare('linkedin')}
                      className="w-full px-4 py-2 text-left text-foreground transition-colors hover:bg-muted"
                    >
                      Share on LinkedIn
                    </button>
                    <button
                      onClick={() => handleShare('facebook')}
                      className="w-full px-4 py-2 text-left text-foreground transition-colors hover:bg-muted"
                    >
                      Share on Facebook
                    </button>
                    <button
                      onClick={() => handleShare('copy')}
                      className="w-full px-4 py-2 text-left text-foreground transition-colors hover:bg-muted"
                    >
                      Copy Link
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </header>

        {/* Featured Image */}
        <div
          ref={contentRef}
          data-section="content"
          className={`mb-8 transition-all duration-1000 ${visibleSections.content ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}`}
        >
          <img
            src={article.featuredImage}
            alt={article.title[intl.locale as keyof typeof article.title]}
            className="h-64 w-full rounded-lg object-cover md:h-96"
          />
        </div>

        {/* Article Content */}
        <div
          className={`prose prose-lg mb-8 max-w-none transition-all delay-200 duration-1000 ${visibleSections.content ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}`}
        >
          <MarkdownRenderer
            content={
              article.content[intl.locale as keyof typeof article.content]
            }
          />
        </div>

        {/* Tags */}
        {article.tags.length > 0 && (
          <div
            className={`mb-8 border-b border-border pb-8 transition-all delay-300 duration-1000 ${visibleSections.content ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}`}
          >
            <div className="flex flex-wrap items-center gap-2">
              <Tag className="mr-2 h-4 w-4 text-muted-foreground" />
              {article.tags.map(tag => (
                <span
                  key={tag}
                  className="rounded-full bg-muted px-3 py-1 text-sm text-muted-foreground"
                >
                  {tag}
                </span>
              ))}
            </div>
          </div>
        )}

        {/* Author Bio */}
        <div
          ref={authorRef}
          data-section="author"
          className={`mb-8 rounded-lg bg-muted p-6 transition-all duration-1000 ${visibleSections.author ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}`}
        >
          <div className="flex items-start">
            <img
              src={article.author.avatar}
              alt={article.author.name}
              className="mr-4 h-16 w-16 flex-shrink-0 rounded-full"
            />
            <div>
              <h3 className="mb-1 font-bold text-foreground">
                {article.author.name}
              </h3>
              <p className="mb-2 text-sm text-muted-foreground">
                {
                  article.author.title[
                    intl.locale as keyof typeof article.author.title
                  ]
                }
              </p>
              <p className="text-foreground">
                {
                  article.author.bio[
                    intl.locale as keyof typeof article.author.bio
                  ]
                }
              </p>
            </div>
          </div>
        </div>
      </article>

      {/* Related Articles */}
      {relatedArticles.length > 0 && (
        <section
          ref={relatedRef}
          data-section="related"
          className="bg-muted/30 py-16"
        >
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <div
              className={`transition-all duration-1000 ${visibleSections.related ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}`}
            >
              <h2 className="mb-8 text-center text-2xl font-bold text-foreground">
                {intl.formatMessage({
                  id: 'news.relatedArticles',
                  defaultMessage: 'Related Articles',
                })}
              </h2>
              <div className="grid grid-cols-1 gap-8 md:grid-cols-3">
                {relatedArticles.map((relatedArticle, index) => (
                  <article
                    key={relatedArticle.id}
                    className={`stagger-card overflow-hidden rounded-lg bg-card shadow-lg transition-all duration-300 hover:shadow-xl ${
                      visibleSections.related
                        ? 'translate-y-0 opacity-100'
                        : 'translate-y-8 opacity-0'
                    }`}
                    style={{ transitionDelay: `${200 + index * 100}ms` }}
                  >
                    <img
                      src={relatedArticle.featuredImage}
                      alt={
                        relatedArticle.title[
                          intl.locale as keyof typeof relatedArticle.title
                        ]
                      }
                      className="h-48 w-full object-cover"
                    />
                    <div className="p-6">
                      <div className="mb-3 flex items-center text-sm text-muted-foreground">
                        <span className="mr-3 rounded-full bg-primary/10 px-2 py-1 text-xs font-medium text-primary">
                          {
                            relatedArticle.category.name[
                              intl.locale as keyof typeof relatedArticle.category.name
                            ]
                          }
                        </span>
                        <Calendar className="mr-1 h-4 w-4" />
                        <span>{formatDate(relatedArticle.publishedAt)}</span>
                      </div>
                      <h3 className="mb-3 text-lg font-bold text-foreground">
                        {
                          relatedArticle.title[
                            intl.locale as keyof typeof relatedArticle.title
                          ]
                        }
                      </h3>
                      <p className="mb-4 line-clamp-3 text-muted-foreground">
                        {
                          relatedArticle.excerpt[
                            intl.locale as keyof typeof relatedArticle.excerpt
                          ]
                        }
                      </p>
                      <Link
                        to={`/news/${relatedArticle.slug}`}
                        className="inline-flex items-center font-medium text-primary transition-colors hover:text-primary/80"
                      >
                        {intl.formatMessage({
                          id: 'common.readMore',
                          defaultMessage: 'Read More',
                        })}
                        <ChevronRight className="ml-1 h-4 w-4" />
                      </Link>
                    </div>
                  </article>
                ))}
              </div>
            </div>
          </div>
        </section>
      )}
    </div>
  );
}

import { PAGE_METADATA } from '@/constants/site-config';
import { useState, useEffect, useRef } from 'react';
import { updatePageMetadata } from '@/utils';
import { useIntl } from 'react-intl';
import { Link } from 'react-router-dom';
import {
  Search,
  Calendar,
  User,
  Tag,
  Filter,
  ChevronRight,
} from 'lucide-react';
import {
  newsArticles,
  newsCategories,
  getFeaturedNews,
  getNewsByCategory,
  searchNews,
} from '@/data/public/news';
import { NewsArticle, NewsFilters } from '@/types/public/news';
import '@/styles/animations.css';

/**
 * News Page Component
 *
 * Displays news articles with filtering and search functionality.
 * Features include:
 * - Featured news section
 * - Category filtering
 * - Search functionality
 * - Responsive grid layout
 *
 * @returns {JSX.Element} The News Page component
 */
export default function Page() {
  const intl = useIntl();
  const metadata = PAGE_METADATA.news;

  // Update document metadata
  useEffect(() => {
    const translatedMetadata = {
      ...metadata,
      title: intl.formatMessage({
        id: 'news.title',
        defaultMessage: 'News & Press',
      }),
      description: intl.formatMessage({
        id: 'news.subtitle',
        defaultMessage:
          'Stay updated with our latest announcements, product updates, and industry insights',
      }),
    };
    updatePageMetadata(translatedMetadata);
  }, [metadata, intl]);

  const [filteredNews, setFilteredNews] = useState<NewsArticle[]>(newsArticles);
  const [filters, setFilters] = useState<NewsFilters>({});
  const [searchQuery, setSearchQuery] = useState('');
  const [showFilters, setShowFilters] = useState(false);

  // State to track which sections are visible
  const [visibleSections, setVisibleSections] = useState({
    hero: true,
    featured: true,
    filters: true,
    grid: true,
  });

  // Refs for the sections
  const heroRef = useRef(null);
  const featuredRef = useRef(null);
  const filtersRef = useRef(null);
  const gridRef = useRef(null);

  const featuredNews = getFeaturedNews();

  // Add intersection observer to check when sections are visible
  useEffect(() => {
    const observerOptions = {
      root: null,
      rootMargin: '0px',
      threshold: 0.1,
    };

    const observerCallback = (entries: IntersectionObserverEntry[]) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const sectionId = entry.target.getAttribute('data-section');
          if (sectionId) {
            setVisibleSections(prev => ({
              ...prev,
              [sectionId]: true,
            }));
          }
        }
      });
    };

    const observer = new IntersectionObserver(
      observerCallback,
      observerOptions
    );

    // Observe each section
    if (heroRef.current) observer.observe(heroRef.current);
    if (featuredRef.current) observer.observe(featuredRef.current);
    if (filtersRef.current) observer.observe(filtersRef.current);
    if (gridRef.current) observer.observe(gridRef.current);

    return () => {
      observer.disconnect();
    };
  }, []);

  useEffect(() => {
    let filtered = newsArticles.filter(
      article => article.status === 'published'
    );

    // Apply search filter
    if (searchQuery) {
      filtered = searchNews(searchQuery);
    }

    // Apply category filter
    if (filters.category) {
      filtered = getNewsByCategory(filters.category);
    }

    // Apply featured filter
    if (filters.featured) {
      filtered = filtered.filter(article => article.featured);
    }

    setFilteredNews(filtered);
  }, [filters, searchQuery]);

  const handleFilterChange = (
    key: keyof NewsFilters,
    value: string | boolean
  ) => {
    setFilters(prev => ({
      ...prev,
      [key]: value === prev[key] ? undefined : value,
    }));
  };

  const clearFilters = () => {
    setFilters({});
    setSearchQuery('');
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString(intl.locale, {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  return (
    <div className="bg-background">
      {/* Hero Section */}
      <div
        ref={heroRef}
        data-section="hero"
        className={`section-reveal bg-gradient-to-r from-primary to-primary/80 text-white ${visibleSections.hero ? 'visible' : ''}`}
      >
        <div className="container mx-auto px-4 py-16 md:py-24">
          <div className="animate-hero-content mx-auto max-w-4xl text-center">
            <h1 className="animate-fadeIn mb-6 text-4xl font-bold">
              {intl.formatMessage({
                id: 'news.title',
                defaultMessage: 'News & Press',
              })}
            </h1>
            <p className="hero-element hero-delay-1 mb-8 text-xl opacity-90">
              {intl.formatMessage({
                id: 'news.subtitle',
                defaultMessage:
                  'Stay updated with our latest announcements, product updates, and industry insights',
              })}
            </p>
          </div>
        </div>
      </div>

      {/* Featured News */}
      {featuredNews.length > 0 && (
        <div
          ref={featuredRef}
          data-section="featured"
          className={`section-reveal bg-muted py-16 ${visibleSections.featured ? 'visible' : ''}`}
        >
          <div className="container mx-auto px-4">
            <div className="mx-auto max-w-7xl">
              <h2 className="animate-slideUp mb-12 text-center text-3xl font-bold md:text-4xl">
                {intl.formatMessage({
                  id: 'news.featured',
                  defaultMessage: 'Featured News',
                })}
              </h2>
              <div className="grid grid-cols-1 gap-8 lg:grid-cols-2">
                {featuredNews.slice(0, 2).map((article, index) => (
                  <article
                    key={article.id}
                    className="stagger-card animate-fadeIn overflow-hidden rounded-lg bg-card shadow-sm transition-all duration-300 hover:-translate-y-1 hover:shadow-md"
                    style={{ animationDelay: `${index * 150}ms` }}
                  >
                    <img
                      src={article.featuredImage}
                      alt={
                        article.title[intl.locale as keyof typeof article.title]
                      }
                      className="h-64 w-full object-cover"
                    />
                    <div className="p-6">
                      <div className="mb-3 flex items-center text-sm text-muted-foreground">
                        <span className="mr-3 rounded-full bg-primary/10 px-2 py-1 text-xs font-medium text-primary">
                          {
                            article.category.name[
                              intl.locale as keyof typeof article.category.name
                            ]
                          }
                        </span>
                        <Calendar className="mr-1 h-4 w-4" />
                        <span>{formatDate(article.publishedAt)}</span>
                        <span className="mx-2">•</span>
                        <span>{article.readTime} min read</span>
                      </div>
                      <h3 className="mb-3 text-xl font-bold text-foreground">
                        {
                          article.title[
                            intl.locale as keyof typeof article.title
                          ]
                        }
                      </h3>
                      <p className="mb-4 text-muted-foreground">
                        {
                          article.excerpt[
                            intl.locale as keyof typeof article.excerpt
                          ]
                        }
                      </p>
                      <Link
                        to={`/news/${article.slug}`}
                        className="inline-flex items-center font-medium text-primary transition-colors hover:text-primary/80"
                      >
                        {intl.formatMessage({
                          id: 'common.readMore',
                          defaultMessage: 'Read More',
                        })}
                        <ChevronRight className="ml-1 h-4 w-4" />
                      </Link>
                    </div>
                  </article>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Search and Filters */}
      <div
        ref={filtersRef}
        data-section="filters"
        className={`section-reveal container mx-auto px-4 py-8 ${visibleSections.filters ? 'visible' : ''}`}
      >
        <div className="mx-auto max-w-7xl">
          <div className="animate-fadeIn rounded-lg bg-card p-6 shadow-sm">
            <div className="flex flex-col gap-4 lg:flex-row">
              {/* Search */}
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 transform text-muted-foreground" />
                <input
                  type="text"
                  placeholder={intl.formatMessage({
                    id: 'news.searchPlaceholder',
                    defaultMessage: 'Search news articles...',
                  })}
                  value={searchQuery}
                  onChange={e => setSearchQuery(e.target.value)}
                  className="w-full rounded-lg border border-input bg-background py-2 pl-10 pr-4 text-foreground focus:border-transparent focus:ring-2 focus:ring-ring"
                />
              </div>

              {/* Filter Toggle */}
              <button
                onClick={() => setShowFilters(!showFilters)}
                className="shimmer-effect flex items-center rounded-lg bg-muted px-4 py-2 text-muted-foreground transition-colors hover:bg-muted/80"
              >
                <Filter className="mr-2 h-4 w-4" />
                {intl.formatMessage({
                  id: 'common.filters',
                  defaultMessage: 'Filters',
                })}
              </button>
            </div>

            {/* Filters */}
            {showFilters && (
              <div className="animate-slideUp mt-6 border-t border-border pt-6">
                <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                  {/* Category Filter */}
                  <div>
                    <label className="mb-2 block text-sm font-medium text-foreground">
                      {intl.formatMessage({
                        id: 'news.category',
                        defaultMessage: 'Category',
                      })}
                    </label>
                    <div className="space-y-2">
                      {newsCategories.map(category => (
                        <label key={category.id} className="flex items-center">
                          <input
                            type="checkbox"
                            checked={filters.category === category.slug}
                            onChange={() =>
                              handleFilterChange('category', category.slug)
                            }
                            className="rounded border-input text-primary focus:ring-ring"
                          />
                          <span className="ml-2 text-sm text-foreground">
                            {
                              category.name[
                                intl.locale as keyof typeof category.name
                              ]
                            }
                          </span>
                        </label>
                      ))}
                    </div>
                  </div>

                  {/* Featured Filter */}
                  <div>
                    <label className="mb-2 block text-sm font-medium text-foreground">
                      {intl.formatMessage({
                        id: 'news.type',
                        defaultMessage: 'Type',
                      })}
                    </label>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={filters.featured || false}
                        onChange={() => handleFilterChange('featured', true)}
                        className="rounded border-input text-primary focus:ring-ring"
                      />
                      <span className="ml-2 text-sm text-foreground">
                        {intl.formatMessage({
                          id: 'news.featuredOnly',
                          defaultMessage: 'Featured Only',
                        })}
                      </span>
                    </label>
                  </div>

                  {/* Clear Filters */}
                  <div className="flex items-end">
                    <button
                      onClick={clearFilters}
                      className="px-4 py-2 text-sm text-muted-foreground underline transition-colors hover:text-foreground"
                    >
                      {intl.formatMessage({
                        id: 'common.clearFilters',
                        defaultMessage: 'Clear Filters',
                      })}
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* News Grid */}
      <div
        ref={gridRef}
        data-section="grid"
        className={`section-reveal container mx-auto px-4 py-16 ${visibleSections.grid ? 'visible' : ''}`}
      >
        <div className="mx-auto max-w-7xl">
          {filteredNews.length === 0 ? (
            <div className="animate-fadeIn py-12 text-center">
              <p className="text-lg text-muted-foreground">
                {intl.formatMessage({
                  id: 'news.noResults',
                  defaultMessage:
                    'No news articles found matching your criteria.',
                })}
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
              {filteredNews.map((article, index) => (
                <article
                  key={article.id}
                  className="stagger-card animate-fadeIn overflow-hidden rounded-lg bg-card shadow-sm transition-all duration-300 hover:-translate-y-1 hover:shadow-md"
                  style={{ animationDelay: `${index * 100}ms` }}
                >
                  <img
                    src={article.featuredImage}
                    alt={
                      article.title[intl.locale as keyof typeof article.title]
                    }
                    className="h-48 w-full object-cover"
                  />
                  <div className="p-6">
                    <div className="mb-3 flex items-center text-sm text-muted-foreground">
                      <span className="mr-3 rounded-full bg-primary/10 px-2 py-1 text-xs font-medium text-primary">
                        {
                          article.category.name[
                            intl.locale as keyof typeof article.category.name
                          ]
                        }
                      </span>
                      <Calendar className="mr-1 h-4 w-4" />
                      <span>{formatDate(article.publishedAt)}</span>
                    </div>
                    <h3 className="mb-3 text-lg font-bold text-foreground">
                      {article.title[intl.locale as keyof typeof article.title]}
                    </h3>
                    <p className="mb-4 line-clamp-3 text-muted-foreground">
                      {
                        article.excerpt[
                          intl.locale as keyof typeof article.excerpt
                        ]
                      }
                    </p>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center text-sm text-muted-foreground">
                        <User className="mr-1 h-4 w-4" />
                        <span>{article.author.name}</span>
                        <span className="mx-2">•</span>
                        <span>{article.readTime} min</span>
                      </div>
                      <Link
                        to={`/news/${article.slug}`}
                        className="text-sm font-medium text-primary transition-colors hover:text-primary/80"
                      >
                        {intl.formatMessage({
                          id: 'common.readMore',
                          defaultMessage: 'Read More',
                        })}
                      </Link>
                    </div>
                    {article.tags.length > 0 && (
                      <div className="mt-4 border-t border-border pt-4">
                        <div className="flex flex-wrap items-center gap-2">
                          <Tag className="h-4 w-4 text-muted-foreground" />
                          {article.tags.slice(0, 3).map(tag => (
                            <span
                              key={tag}
                              className="rounded-full bg-muted px-2 py-1 text-xs text-muted-foreground"
                            >
                              {tag}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </article>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

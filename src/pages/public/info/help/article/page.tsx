import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useIntl } from 'react-intl';
import { updatePageMetadata } from '@/utils';
import { PAGE_METADATA } from '@/constants/site-config';
import {
  getHelpArticleById,
  getRelatedArticles,
  HELP_CATEGORIES,
} from '@/data/public/help-center/';
import { HelpArticleDetail } from '@/types/public/help-center';
import {
  ChevronLeft,
  ThumbsUp,
  ThumbsDown,
  Star,
  Clock,
  ArrowRight,
} from 'lucide-react';
import MarkdownRender from '@/components/common/markdown-renderer';
import PageLoader from '@/components/base/page-loader';
import '@/styles/animations.css';

export default function Page() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const intl = useIntl();
  const [article, setArticle] = useState<HelpArticleDetail | null>(null);
  const [relatedArticles, setRelatedArticles] = useState<HelpArticleDetail[]>(
    []
  );
  const [rating, setRating] = useState<number>(0);
  const [feedback, setFeedback] = useState<'helpful' | 'not-helpful' | null>(
    null
  );
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadArticle = async () => {
      if (!id) return;

      try {
        const articleData = getHelpArticleById(id);
        if (articleData) {
          setArticle(articleData);
          const related = getRelatedArticles(
            articleData.category || 'getting-started',
            id
          );
          setRelatedArticles(related);
        }
      } catch (error) {
        console.error('Error loading help article:', error);
      } finally {
        setLoading(false);
      }
    };

    loadArticle();
  }, [id]);

  useEffect(() => {
    if (article) {
      const locale = intl.locale;
      const title =
        article.title[locale as keyof typeof article.title] || article.title.en;
      const description =
        article.excerpt[locale as keyof typeof article.excerpt] ||
        article.excerpt.en;

      updatePageMetadata({
        ...PAGE_METADATA.helpArticle,
        title: `${title} - ${intl.formatMessage({ id: 'page.help.title' })}`,
        description: description,
      });
    }
  }, [article, intl]);

  const handleRating = (value: number) => {
    setRating(value);
    // In a real app, this would send the rating to the backend
    console.log('Article rated:', value);
  };

  const handleFeedback = (type: 'helpful' | 'not-helpful') => {
    setFeedback(type);
    // In a real app, this would send feedback to the backend
    console.log('Feedback submitted:', type);
  };

  // Get translated category name
  const getCategoryName = (categoryId: string) => {
    const category = HELP_CATEGORIES.find(cat => cat.id === categoryId);
    if (!category) return categoryId;
    return (
      category.name[intl.locale as keyof typeof category.name] ||
      category.name.en
    );
  };

  if (loading) {
    return <PageLoader />;
  }

  if (!article) {
    return (
      <div className="min-h-screen bg-background">
        {/* Header section matching the design */}
        <div className="bg-gradient-to-r from-primary to-primary/80 text-white">
          <div className="container mx-auto px-4 py-16">
            <div className="mx-auto max-w-4xl text-center">
              <div className="mb-6">
                <svg
                  className="mx-auto h-16 w-16 text-white/60"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={1}
                    d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                  />
                </svg>
              </div>
              <h1 className="mb-4 text-3xl font-bold">
                {intl.formatMessage({ id: 'page.help.article.not-found' })}
              </h1>
              <p className="mb-8 text-xl opacity-90">
                {intl.formatMessage({
                  id: 'page.help.article.not-found.description',
                  defaultMessage:
                    "The help article you're looking for could not be found or may have been moved.",
                })}
              </p>
            </div>
          </div>
        </div>

        {/* Content section */}
        <div className="container mx-auto px-4 py-12">
          <div className="mx-auto max-w-2xl text-center">
            <div className="mb-8">
              <h2 className="mb-4 text-xl font-semibold">
                {intl.formatMessage({
                  id: 'page.help.article.not-found.suggestions',
                  defaultMessage: 'What would you like to do?',
                })}
              </h2>
              <div className="grid gap-4 md:grid-cols-2">
                <button
                  onClick={() => navigate('/help')}
                  className="flex items-center justify-center gap-3 rounded-lg border border-border p-4 transition-colors hover:bg-accent"
                >
                  <ChevronLeft className="h-5 w-5 text-primary" />
                  <span className="font-medium">
                    {intl.formatMessage({
                      id: 'page.help.article.back-to-help',
                    })}
                  </span>
                </button>
                <button
                  onClick={() => navigate('/contact')}
                  className="flex items-center justify-center gap-3 rounded-lg border border-border p-4 transition-colors hover:bg-accent"
                >
                  <svg
                    className="h-5 w-5 text-primary"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                    />
                  </svg>
                  <span className="font-medium">
                    {intl.formatMessage({
                      id: 'page.help.article.contact-support',
                      defaultMessage: 'Contact Support',
                    })}
                  </span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-background">
      {/* Header section */}
      <div className="bg-gradient-to-r from-primary to-primary/80 text-white">
        <div className="container mx-auto px-4 py-16">
          <div className="mx-auto max-w-4xl">
            {/* Back Navigation */}
            <button
              onClick={() => navigate('/help')}
              className="animate-fadeIn mb-8 flex items-center gap-2 text-white/80 transition-colors hover:text-white"
            >
              <ChevronLeft className="h-4 w-4" />
              {intl.formatMessage({ id: 'page.help.article.back-to-help' })}
            </button>

            {/* Article Header */}
            <div className="animate-fadeIn animation-delay-200">
              <div className="mb-4 flex flex-wrap items-center gap-4 text-sm text-white/80">
                <div className="flex items-center gap-2">
                  <span className="rounded-full bg-white/20 px-3 py-1 text-sm font-medium">
                    {getCategoryName(article.category || 'help')}
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4" />
                  <span>{article.readingTime} min read</span>
                </div>
                {article.lastUpdated && (
                  <div className="flex items-center gap-2">
                    <span>Updated {article.lastUpdated}</span>
                  </div>
                )}
              </div>
              <h1 className="mb-6 text-2xl font-bold leading-tight md:text-3xl lg:text-4xl">
                {article.title[intl.locale as keyof typeof article.title] ||
                  article.title.en}
              </h1>
              <p className="text-xl text-white/90">
                {article.excerpt[intl.locale as keyof typeof article.excerpt] ||
                  article.excerpt.en}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Article Content */}
      <div className="container mx-auto px-4 py-8 md:py-12">
        <div className="mx-auto max-w-4xl">
          <div className="animate-fadeIn">
            <MarkdownRender
              structuredContent={article.content}
              locale={intl.locale}
              className="prose-base md:prose-lg"
            />
          </div>
        </div>
      </div>

      {/* Feedback Section */}
      <div className="border-t border-border bg-muted/30 py-12">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl">
            <div className="rounded-lg border border-border bg-card p-8">
              <h3 className="mb-6 text-xl font-semibold text-card-foreground">
                {intl.formatMessage({ id: 'page.help.article.was-helpful' })}
              </h3>

              {/* Star Rating */}
              <div className="mb-6">
                <p className="mb-3 text-sm text-muted-foreground">
                  Rate this article:
                </p>
                <div className="flex items-center gap-1">
                  {[1, 2, 3, 4, 5].map(star => (
                    <button
                      key={star}
                      onClick={() => handleRating(star)}
                      className={`p-1 transition-colors ${
                        star <= rating
                          ? 'text-yellow-400'
                          : 'text-muted-foreground hover:text-yellow-400'
                      }`}
                    >
                      <Star className="h-6 w-6 fill-current" />
                    </button>
                  ))}
                </div>
              </div>

              {/* Helpful/Not Helpful Buttons */}
              <div className="mb-6 flex gap-4">
                <button
                  onClick={() => handleFeedback('helpful')}
                  className={`flex items-center gap-2 rounded-lg border px-6 py-3 transition-all ${
                    feedback === 'helpful'
                      ? 'border-green-200 bg-green-50 text-green-700 dark:border-green-800 dark:bg-green-950 dark:text-green-300'
                      : 'border-border bg-background hover:border-green-200 hover:bg-green-50 dark:hover:border-green-800 dark:hover:bg-green-950'
                  }`}
                >
                  <ThumbsUp className="h-4 w-4" />
                  {intl.formatMessage({ id: 'page.help.article.helpful' })}
                </button>
                <button
                  onClick={() => handleFeedback('not-helpful')}
                  className={`flex items-center gap-2 rounded-lg border px-6 py-3 transition-all ${
                    feedback === 'not-helpful'
                      ? 'border-red-200 bg-red-50 text-red-700 dark:border-red-800 dark:bg-red-950 dark:text-red-300'
                      : 'border-border bg-background hover:border-red-200 hover:bg-red-50 dark:hover:border-red-800 dark:hover:bg-red-950'
                  }`}
                >
                  <ThumbsDown className="h-4 w-4" />
                  {intl.formatMessage({ id: 'page.help.article.not-helpful' })}
                </button>
              </div>

              {/* Feedback Thank You */}
              {feedback && (
                <div className="animate-fadeIn rounded-lg bg-primary/10 p-4">
                  <p className="text-sm text-primary">
                    {intl.formatMessage({
                      id: 'page.help.article.feedback.thanks-helpful',
                    })}
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Related Articles */}
      {relatedArticles.length > 0 && (
        <div className="border-t border-border bg-background py-12">
          <div className="container mx-auto px-4">
            <div className="mx-auto max-w-4xl">
              <h3 className="mb-8 text-2xl font-bold text-card-foreground">
                {intl.formatMessage({
                  id: 'page.help.article.related-articles',
                })}
              </h3>
              <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                {relatedArticles.map((relatedArticle, index) => (
                  <div
                    key={relatedArticle.id}
                    onClick={() => navigate(`/help/${relatedArticle.id}`)}
                    className="stagger-card group cursor-pointer rounded-lg border border-border bg-card p-6 transition-all hover:border-primary hover:shadow-lg"
                    style={{ animationDelay: `${index * 100}ms` }}
                  >
                    <div className="mb-4 flex items-center justify-between">
                      <span className="rounded-full bg-primary/10 px-3 py-1 text-xs font-medium text-primary">
                        {getCategoryName(relatedArticle.category || 'help')}
                      </span>
                      <ArrowRight className="h-4 w-4 text-muted-foreground transition-transform group-hover:translate-x-1 group-hover:text-primary" />
                    </div>
                    <h4 className="mb-3 font-semibold text-card-foreground group-hover:text-primary">
                      {relatedArticle.title[
                        intl.locale as keyof typeof relatedArticle.title
                      ] || relatedArticle.title.en}
                    </h4>
                    <p className="mb-4 line-clamp-2 text-sm text-muted-foreground">
                      {relatedArticle.excerpt[
                        intl.locale as keyof typeof relatedArticle.excerpt
                      ] || relatedArticle.excerpt.en}
                    </p>
                    <div className="flex items-center gap-2 text-xs text-muted-foreground">
                      <Clock className="h-3 w-3" />
                      <span>{relatedArticle.readingTime} min read</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

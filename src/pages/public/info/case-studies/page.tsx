import { PAGE_METADATA } from '@/constants/site-config';
import { useState, useEffect, useRef } from 'react';
import { updatePageMetadata } from '@/utils';
import { useIntl } from 'react-intl';
import { Link } from 'react-router-dom';
import {
  Search,
  Calendar,
  Building,
  Tag,
  Filter,
  ChevronRight,
  TrendingUp,
  Award,
  Target,
} from 'lucide-react';
import {
  caseStudies,
  caseStudyCategories,
  getFeaturedCaseStudies,
  getCaseStudiesByCategory,
  searchCaseStudies,
} from '@/data/public/case-studies';
import { CaseStudy, CaseStudyFilters } from '@/types/public/case-studies';
import '@/styles/animations.css';

/**
 * Case Studies Page Component
 *
 * Displays case studies with filtering and search functionality.
 * Features include:
 * - Featured case studies section
 * - Category and industry filtering
 * - Search functionality
 * - Metrics display for each case study
 *
 * @returns {JSX.Element} The Case Studies Page component
 */
export default function Page() {
  const intl = useIntl();
  const metadata = PAGE_METADATA.caseStudies;

  // Update document metadata
  useEffect(() => {
    const translatedMetadata = {
      ...metadata,
      title: intl.formatMessage({
        id: 'caseStudies.title',
        defaultMessage: 'Case Studies',
      }),
      description: intl.formatMessage({
        id: 'caseStudies.subtitle',
        defaultMessage:
          'Discover how we help businesses achieve remarkable results through innovative solutions and strategic partnerships',
      }),
    };
    updatePageMetadata(translatedMetadata);
  }, [metadata, intl]);

  const [filteredCaseStudies, setFilteredCaseStudies] =
    useState<CaseStudy[]>(caseStudies);
  const [filters, setFilters] = useState<CaseStudyFilters>({});
  const [searchQuery, setSearchQuery] = useState('');
  const [showFilters, setShowFilters] = useState(false);

  // State to track which sections are visible
  const [visibleSections, setVisibleSections] = useState({
    hero: true,
    featured: true,
    filters: true,
    grid: true,
  });

  // Refs for the sections
  const heroRef = useRef(null);
  const featuredRef = useRef(null);
  const filtersRef = useRef(null);
  const gridRef = useRef(null);

  const featuredCaseStudies = getFeaturedCaseStudies();

  // Add intersection observer to check when sections are visible
  useEffect(() => {
    const observerOptions = {
      root: null,
      rootMargin: '0px',
      threshold: 0.1,
    };

    const observerCallback = (entries: IntersectionObserverEntry[]) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const sectionId = entry.target.getAttribute('data-section');
          if (sectionId) {
            setVisibleSections(prev => ({
              ...prev,
              [sectionId]: true,
            }));
          }
        }
      });
    };

    const observer = new IntersectionObserver(
      observerCallback,
      observerOptions
    );

    // Observe each section
    if (heroRef.current) observer.observe(heroRef.current);
    if (featuredRef.current) observer.observe(featuredRef.current);
    if (filtersRef.current) observer.observe(filtersRef.current);
    if (gridRef.current) observer.observe(gridRef.current);

    return () => {
      observer.disconnect();
    };
  }, []);

  useEffect(() => {
    let filtered = caseStudies.filter(study => study.status === 'published');

    // Apply search filter
    if (searchQuery) {
      filtered = searchCaseStudies(searchQuery);
    }

    // Apply category filter
    if (filters.category) {
      filtered = getCaseStudiesByCategory(filters.category);
    }

    // Apply featured filter
    if (filters.featured) {
      filtered = filtered.filter(study => study.featured);
    }

    setFilteredCaseStudies(filtered);
  }, [filters, searchQuery]);

  const handleFilterChange = (
    key: keyof CaseStudyFilters,
    value: string | boolean
  ) => {
    setFilters(prev => ({
      ...prev,
      [key]: value === prev[key] ? undefined : value,
    }));
  };

  const clearFilters = () => {
    setFilters({});
    setSearchQuery('');
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString(intl.locale, {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const getMetricIcon = (iconName: string) => {
    switch (iconName) {
      case 'trending-up':
        return <TrendingUp className="h-5 w-5" />;
      case 'award':
        return <Award className="h-5 w-5" />;
      case 'target':
        return <Target className="h-5 w-5" />;
      default:
        return <TrendingUp className="h-5 w-5" />;
    }
  };

  return (
    <div className="bg-background">
      {/* Hero Section */}
      <div
        ref={heroRef}
        data-section="hero"
        className={`section-reveal bg-gradient-to-r from-primary to-primary/80 text-white ${visibleSections.hero ? 'visible' : ''}`}
      >
        <div className="container mx-auto px-4 py-16 md:py-24">
          <div className="animate-hero-content mx-auto max-w-4xl text-center">
            <h1 className="animate-fadeIn mb-6 text-4xl font-bold">
              {intl.formatMessage({
                id: 'caseStudies.title',
                defaultMessage: 'Case Studies',
              })}
            </h1>
            <p className="hero-element hero-delay-1 mb-8 text-xl opacity-90">
              {intl.formatMessage({
                id: 'caseStudies.subtitle',
                defaultMessage:
                  'Discover how we help businesses achieve remarkable results through innovative solutions and strategic partnerships',
              })}
            </p>
          </div>
        </div>
      </div>

      {/* Featured Case Studies */}
      {featuredCaseStudies.length > 0 && (
        <div
          ref={featuredRef}
          data-section="featured"
          className={`section-reveal bg-muted py-16 ${visibleSections.featured ? 'visible' : ''}`}
        >
          <div className="container mx-auto px-4">
            <div className="mx-auto max-w-7xl">
              <h2 className="animate-slideUp mb-12 text-center text-3xl font-bold md:text-4xl">
                {intl.formatMessage({
                  id: 'caseStudies.featured',
                  defaultMessage: 'Featured Success Stories',
                })}
              </h2>
              <div className="grid grid-cols-1 gap-8 lg:grid-cols-2">
                {featuredCaseStudies.slice(0, 2).map((study, index) => (
                  <article
                    key={study.id}
                    className="stagger-card animate-fadeIn overflow-hidden rounded-lg bg-card shadow-sm transition-all duration-300 hover:-translate-y-1 hover:shadow-md"
                    style={{ animationDelay: `${index * 150}ms` }}
                  >
                    <img
                      src={study.featuredImage}
                      alt={study.title[intl.locale as keyof typeof study.title]}
                      className="h-64 w-full object-cover"
                    />
                    <div className="p-6">
                      <div className="mb-3 flex items-center text-sm text-muted-foreground">
                        <span className="mr-3 rounded-full bg-primary/10 px-2 py-1 text-xs font-medium text-primary">
                          {
                            study.category.name[
                              intl.locale as keyof typeof study.category.name
                            ]
                          }
                        </span>
                        <Building className="mr-1 h-4 w-4" />
                        <span>{study.client.name}</span>
                        <span className="mx-2">•</span>
                        <span>{study.readTime} min read</span>
                      </div>
                      <h3 className="mb-3 text-xl font-bold text-foreground">
                        {study.title[intl.locale as keyof typeof study.title]}
                      </h3>
                      <p className="mb-4 text-muted-foreground">
                        {
                          study.excerpt[
                            intl.locale as keyof typeof study.excerpt
                          ]
                        }
                      </p>

                      {/* Key Metrics Preview */}
                      {study.keyMetrics.length > 0 && (
                        <div className="mb-4 grid grid-cols-2 gap-4 rounded-lg bg-muted/50 p-4">
                          {study.keyMetrics.slice(0, 2).map(metric => (
                            <div key={metric.id} className="text-center">
                              <div className="mb-1 flex items-center justify-center text-primary">
                                {getMetricIcon(metric.icon || 'trending-up')}
                              </div>
                              <div className="text-2xl font-bold text-foreground">
                                {metric.value}
                              </div>
                              <div className="text-xs text-muted-foreground">
                                {
                                  metric.label[
                                    intl.locale as keyof typeof metric.label
                                  ]
                                }
                              </div>
                            </div>
                          ))}
                        </div>
                      )}

                      <Link
                        to={`/case-studies/${study.slug}`}
                        className="inline-flex items-center font-medium text-primary transition-colors hover:text-primary/80"
                      >
                        {intl.formatMessage({
                          id: 'common.readMore',
                          defaultMessage: 'Read More',
                        })}
                        <ChevronRight className="ml-1 h-4 w-4" />
                      </Link>
                    </div>
                  </article>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Search and Filters */}
      <div
        ref={filtersRef}
        data-section="filters"
        className={`section-reveal container mx-auto px-4 py-8 ${visibleSections.filters ? 'visible' : ''}`}
      >
        <div className="mx-auto max-w-7xl">
          <div className="animate-fadeIn rounded-lg bg-card p-6 shadow-sm">
            <div className="flex flex-col gap-4 lg:flex-row">
              {/* Search */}
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 transform text-muted-foreground" />
                <input
                  type="text"
                  placeholder={intl.formatMessage({
                    id: 'caseStudies.searchPlaceholder',
                    defaultMessage: 'Search case studies...',
                  })}
                  value={searchQuery}
                  onChange={e => setSearchQuery(e.target.value)}
                  className="w-full rounded-lg border border-input bg-background py-2 pl-10 pr-4 text-foreground focus:border-transparent focus:ring-2 focus:ring-ring"
                />
              </div>

              {/* Filter Toggle */}
              <button
                onClick={() => setShowFilters(!showFilters)}
                className="shimmer-effect flex items-center rounded-lg bg-muted px-4 py-2 text-muted-foreground transition-colors hover:bg-muted/80"
              >
                <Filter className="mr-2 h-4 w-4" />
                {intl.formatMessage({
                  id: 'common.filters',
                  defaultMessage: 'Filters',
                })}
              </button>
            </div>

            {/* Filters */}
            {showFilters && (
              <div className="animate-slideUp mt-6 border-t border-border pt-6">
                <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                  {/* Category Filter */}
                  <div>
                    <label className="mb-2 block text-sm font-medium text-foreground">
                      {intl.formatMessage({
                        id: 'caseStudies.category',
                        defaultMessage: 'Category',
                      })}
                    </label>
                    <div className="space-y-2">
                      {caseStudyCategories.map(category => (
                        <label key={category.id} className="flex items-center">
                          <input
                            type="checkbox"
                            checked={filters.category === category.slug}
                            onChange={() =>
                              handleFilterChange('category', category.slug)
                            }
                            className="rounded border-input text-primary focus:ring-ring"
                          />
                          <span className="ml-2 text-sm text-foreground">
                            {
                              category.name[
                                intl.locale as keyof typeof category.name
                              ]
                            }
                          </span>
                        </label>
                      ))}
                    </div>
                  </div>

                  {/* Featured Filter */}
                  <div>
                    <label className="mb-2 block text-sm font-medium text-foreground">
                      {intl.formatMessage({
                        id: 'caseStudies.type',
                        defaultMessage: 'Type',
                      })}
                    </label>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={filters.featured || false}
                        onChange={() => handleFilterChange('featured', true)}
                        className="rounded border-input text-primary focus:ring-ring"
                      />
                      <span className="ml-2 text-sm text-foreground">
                        {intl.formatMessage({
                          id: 'caseStudies.featuredOnly',
                          defaultMessage: 'Featured Only',
                        })}
                      </span>
                    </label>
                  </div>

                  {/* Clear Filters */}
                  <div className="flex items-end">
                    <button
                      onClick={clearFilters}
                      className="px-4 py-2 text-sm text-muted-foreground underline transition-colors hover:text-foreground"
                    >
                      {intl.formatMessage({
                        id: 'common.clearFilters',
                        defaultMessage: 'Clear Filters',
                      })}
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Case Studies Grid */}
      <div
        ref={gridRef}
        data-section="grid"
        className={`section-reveal container mx-auto px-4 py-16 ${visibleSections.grid ? 'visible' : ''}`}
      >
        <div className="mx-auto max-w-7xl">
          {filteredCaseStudies.length === 0 ? (
            <div className="animate-fadeIn py-12 text-center">
              <p className="text-lg text-muted-foreground">
                {intl.formatMessage({
                  id: 'caseStudies.noResults',
                  defaultMessage:
                    'No case studies found matching your criteria.',
                })}
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
              {filteredCaseStudies.map((study, index) => (
                <article
                  key={study.id}
                  className="stagger-card animate-fadeIn overflow-hidden rounded-lg bg-card shadow-sm transition-all duration-300 hover:-translate-y-1 hover:shadow-md"
                  style={{ animationDelay: `${index * 100}ms` }}
                >
                  <img
                    src={study.featuredImage}
                    alt={study.title[intl.locale as keyof typeof study.title]}
                    className="h-48 w-full object-cover"
                  />
                  <div className="p-6">
                    <div className="mb-3 flex items-center text-sm text-muted-foreground">
                      <span className="mr-3 rounded-full bg-primary/10 px-2 py-1 text-xs font-medium text-primary">
                        {
                          study.category.name[
                            intl.locale as keyof typeof study.category.name
                          ]
                        }
                      </span>
                      <Building className="mr-1 h-4 w-4" />
                      <span>{study.client.name}</span>
                    </div>
                    <h3 className="mb-3 text-lg font-bold text-foreground">
                      {study.title[intl.locale as keyof typeof study.title]}
                    </h3>
                    <p className="mb-4 line-clamp-3 text-muted-foreground">
                      {study.excerpt[intl.locale as keyof typeof study.excerpt]}
                    </p>

                    {/* Key Metrics */}
                    {study.keyMetrics.length > 0 && (
                      <div className="mb-4 grid grid-cols-2 gap-2 rounded-lg bg-muted/50 p-3">
                        {study.keyMetrics.slice(0, 2).map(metric => (
                          <div key={metric.id} className="text-center">
                            <div className="text-lg font-bold text-primary">
                              {metric.value}
                            </div>
                            <div className="text-xs text-muted-foreground">
                              {
                                metric.label[
                                  intl.locale as keyof typeof metric.label
                                ]
                              }
                            </div>
                          </div>
                        ))}
                      </div>
                    )}

                    <div className="flex items-center justify-between">
                      <div className="flex items-center text-sm text-muted-foreground">
                        <Calendar className="mr-1 h-4 w-4" />
                        <span>{formatDate(study.publishedAt)}</span>
                        <span className="mx-2">•</span>
                        <span>{study.readTime} min</span>
                      </div>
                      <Link
                        to={`/case-studies/${study.slug}`}
                        className="text-sm font-medium text-primary transition-colors hover:text-primary/80"
                      >
                        {intl.formatMessage({
                          id: 'common.readMore',
                          defaultMessage: 'Read More',
                        })}
                      </Link>
                    </div>
                    {study.tags.length > 0 && (
                      <div className="mt-4 border-t border-border pt-4">
                        <div className="flex flex-wrap items-center gap-2">
                          <Tag className="h-4 w-4 text-muted-foreground" />
                          {study.tags.slice(0, 3).map(tag => (
                            <span
                              key={tag}
                              className="rounded-full bg-muted px-2 py-1 text-xs text-muted-foreground"
                            >
                              {tag}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </article>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

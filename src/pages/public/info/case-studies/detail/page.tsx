import { PAGE_METADATA } from '@/constants/site-config';
import { useState, useEffect, useRef } from 'react';
import { useParams, Link, useNavigate } from 'react-router-dom';
import { updatePageMetadata } from '@/utils';
import { useIntl } from 'react-intl';
import {
  ArrowLeft,
  Calendar,
  Clock,
  Building,
  Tag,
  Share2,
  TrendingUp,
  Award,
  Target,
  DollarSign,
  Zap,
  ShieldCheck,
  ChevronRight,
  Quote,
} from 'lucide-react';
import {
  getCaseStudyBySlug,
  getRelatedCaseStudies,
} from '@/data/public/case-studies';
import { CaseStudy } from '@/types/public/case-studies';
import MarkdownRender from '@/components/common/markdown-renderer';
import '@/styles/animations.css';

/**
 * Case Study Detail Page Component
 *
 * Displays detailed information about a specific case study including:
 * - Full case study content with challenges, solutions, and outcomes
 * - Key metrics and results
 * - Client testimonial
 * - Related case studies
 *
 * @returns {JSX.Element} The Case Study Detail Page component
 */
export default function Page() {
  const { slug } = useParams<{ slug: string }>();
  const navigate = useNavigate();
  const intl = useIntl();
  const metadata = PAGE_METADATA.caseStudyDetail;

  const [caseStudy, setCaseStudy] = useState<CaseStudy | null>(null);
  const [relatedCaseStudies, setRelatedCaseStudies] = useState<CaseStudy[]>([]);
  const [loading, setLoading] = useState(true);

  // State to track which sections are visible
  const [visibleSections, setVisibleSections] = useState({
    header: true,
    content: true,
    metrics: true,
    testimonial: true,
    related: true,
  });

  // Refs for the sections
  const headerRef = useRef(null);
  const contentRef = useRef(null);
  const metricsRef = useRef(null);
  const testimonialRef = useRef(null);
  const relatedRef = useRef(null);

  useEffect(() => {
    if (!slug) {
      navigate('/case-studies');
      return;
    }

    const study = getCaseStudyBySlug(slug);
    if (!study) {
      navigate('/case-studies');
      return;
    }

    setCaseStudy(study);
    setRelatedCaseStudies(getRelatedCaseStudies(study));
    setLoading(false);

    // Update document metadata
    const translatedMetadata = {
      ...metadata,
      title:
        study.title[intl.locale as keyof typeof study.title] || study.title.en,
      description:
        study.excerpt[intl.locale as keyof typeof study.excerpt] ||
        study.excerpt.en,
    };
    updatePageMetadata(translatedMetadata);
  }, [slug, navigate, metadata, intl]);

  // Add intersection observer to check when sections are visible
  useEffect(() => {
    const observerOptions = {
      root: null,
      rootMargin: '0px',
      threshold: 0.1,
    };

    const observerCallback = (entries: IntersectionObserverEntry[]) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const sectionId = entry.target.getAttribute('data-section');
          if (sectionId) {
            setVisibleSections(prev => ({
              ...prev,
              [sectionId]: true,
            }));
          }
        }
      });
    };

    const observer = new IntersectionObserver(
      observerCallback,
      observerOptions
    );

    // Observe each section
    if (headerRef.current) observer.observe(headerRef.current);
    if (contentRef.current) observer.observe(contentRef.current);
    if (metricsRef.current) observer.observe(metricsRef.current);
    if (testimonialRef.current) observer.observe(testimonialRef.current);
    if (relatedRef.current) observer.observe(relatedRef.current);

    return () => {
      observer.disconnect();
    };
  }, [caseStudy]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString(intl.locale, {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const getMetricIcon = (iconName: string) => {
    switch (iconName) {
      case 'trending-up':
        return <TrendingUp className="h-6 w-6" />;
      case 'award':
        return <Award className="h-6 w-6" />;
      case 'target':
        return <Target className="h-6 w-6" />;
      case 'dollar-sign':
        return <DollarSign className="h-6 w-6" />;
      case 'zap':
        return <Zap className="h-6 w-6" />;
      case 'shield-check':
        return <ShieldCheck className="h-6 w-6" />;
      default:
        return <TrendingUp className="h-6 w-6" />;
    }
  };

  const handleShare = async () => {
    if (navigator.share && caseStudy) {
      try {
        await navigator.share({
          title: caseStudy.title[intl.locale as keyof typeof caseStudy.title],
          text: caseStudy.excerpt[
            intl.locale as keyof typeof caseStudy.excerpt
          ],
          url: window.location.href,
        });
      } catch {
        // Fallback to copying URL to clipboard
        navigator.clipboard.writeText(window.location.href);
      }
    } else {
      // Fallback to copying URL to clipboard
      navigator.clipboard.writeText(window.location.href);
    }
  };

  if (loading) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-background">
        <div className="h-32 w-32 animate-spin rounded-full border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!caseStudy) {
    return null;
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header Section */}
      <section
        ref={headerRef}
        data-section="header"
        className="px-4 py-16 sm:px-6 lg:px-8"
      >
        <div className="mx-auto max-w-4xl">
          <div
            className={`transition-all duration-1000 ${visibleSections.header ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}`}
          >
            {/* Back Button */}
            <Link
              to="/case-studies"
              className="mb-8 inline-flex items-center text-muted-foreground transition-colors hover:text-foreground"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              {intl.formatMessage({
                id: 'caseStudies.backToCaseStudies',
                defaultMessage: 'Back to Case Studies',
              })}
            </Link>

            {/* Category Badge */}
            <div className="mb-4">
              <span className="rounded-full bg-primary/10 px-3 py-1 text-sm font-medium text-primary">
                {
                  caseStudy.category.name[
                    intl.locale as keyof typeof caseStudy.category.name
                  ]
                }
              </span>
            </div>

            {/* Title */}
            <h1 className="mb-6 text-4xl font-bold text-foreground md:text-5xl lg:text-6xl">
              {caseStudy.title[intl.locale as keyof typeof caseStudy.title]}
            </h1>

            {/* Excerpt */}
            <p className="mb-8 text-xl leading-relaxed text-muted-foreground">
              {caseStudy.excerpt[intl.locale as keyof typeof caseStudy.excerpt]}
            </p>

            {/* Meta Information */}
            <div className="mb-8 flex flex-wrap items-center gap-6 text-sm text-muted-foreground">
              <div className="flex items-center">
                <Building className="mr-2 h-4 w-4" />
                <span>{caseStudy.client.name}</span>
              </div>
              <div className="flex items-center">
                <Calendar className="mr-2 h-4 w-4" />
                <span>{formatDate(caseStudy.publishedAt)}</span>
              </div>
              <div className="flex items-center">
                <Clock className="mr-2 h-4 w-4" />
                <span>{caseStudy.readTime} min read</span>
              </div>
              <button
                onClick={handleShare}
                className="flex items-center transition-colors hover:text-foreground"
              >
                <Share2 className="mr-2 h-4 w-4" />
                {intl.formatMessage({
                  id: 'common.share',
                  defaultMessage: 'Share',
                })}
              </button>
            </div>

            {/* Featured Image */}
            <div className="overflow-hidden rounded-lg shadow-lg">
              <img
                src={caseStudy.featuredImage}
                alt={
                  caseStudy.title[intl.locale as keyof typeof caseStudy.title]
                }
                className="h-64 w-full object-cover md:h-96"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Key Metrics Section */}
      {caseStudy.keyMetrics.length > 0 && (
        <section
          ref={metricsRef}
          data-section="metrics"
          className="bg-muted/30 px-4 py-16 sm:px-6 lg:px-8"
        >
          <div className="mx-auto max-w-6xl">
            <div
              className={`transition-all duration-1000 ${visibleSections.metrics ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}`}
            >
              <h2 className="mb-12 text-center text-3xl font-bold text-foreground md:text-4xl">
                {intl.formatMessage({
                  id: 'caseStudies.details.keyResults',
                  defaultMessage: 'Key Results',
                })}
              </h2>
              <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-4">
                {caseStudy.keyMetrics.map((metric, index) => (
                  <div
                    key={metric.id}
                    className={`stagger-card rounded-lg bg-card p-6 text-center shadow-lg transition-all duration-300 hover:shadow-xl ${
                      visibleSections.metrics
                        ? 'translate-y-0 opacity-100'
                        : 'translate-y-8 opacity-0'
                    }`}
                    style={{ transitionDelay: `${200 + index * 100}ms` }}
                  >
                    <div className="mb-4 flex items-center justify-center text-primary">
                      {getMetricIcon(metric.icon || 'trending-up')}
                    </div>
                    <div className="mb-2 text-4xl font-bold text-foreground">
                      {metric.value}
                    </div>
                    <div className="mb-2 text-lg font-medium text-foreground">
                      {metric.label[intl.locale as keyof typeof metric.label]}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {
                        metric.description[
                          intl.locale as keyof typeof metric.description
                        ]
                      }
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>
      )}

      {/* Content Section */}
      <section
        ref={contentRef}
        data-section="content"
        className="px-4 py-16 sm:px-6 lg:px-8"
      >
        <div className="mx-auto max-w-4xl">
          <div
            className={`transition-all duration-1000 ${visibleSections.content ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}`}
          >
            <div className="prose prose-lg max-w-none">
              <MarkdownRender
                content={
                  caseStudy.content[
                    intl.locale as keyof typeof caseStudy.content
                  ]
                }
              />
            </div>

            {/* Challenges Section */}
            {caseStudy.challenges.length > 0 && (
              <div className="mt-16">
                <h2 className="mb-8 text-3xl font-bold text-foreground">
                  {intl.formatMessage({
                    id: 'caseStudies.details.challenges',
                    defaultMessage: 'Challenges',
                  })}
                </h2>
                <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                  {caseStudy.challenges.map(challenge => (
                    <div
                      key={challenge.id}
                      className="rounded-lg bg-card p-6 shadow-lg"
                    >
                      <h3 className="mb-3 text-xl font-bold text-foreground">
                        {
                          challenge.title[
                            intl.locale as keyof typeof challenge.title
                          ]
                        }
                      </h3>
                      <p className="text-muted-foreground">
                        {
                          challenge.description[
                            intl.locale as keyof typeof challenge.description
                          ]
                        }
                      </p>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Solutions Section */}
            {caseStudy.solutions.length > 0 && (
              <div className="mt-16">
                <h2 className="mb-8 text-3xl font-bold text-foreground">
                  {intl.formatMessage({
                    id: 'caseStudies.details.solutions',
                    defaultMessage: 'Solutions',
                  })}
                </h2>
                <div className="space-y-8">
                  {caseStudy.solutions.map(solution => (
                    <div
                      key={solution.id}
                      className="rounded-lg bg-card p-6 shadow-lg"
                    >
                      <h3 className="mb-3 text-xl font-bold text-foreground">
                        {
                          solution.title[
                            intl.locale as keyof typeof solution.title
                          ]
                        }
                      </h3>
                      <p className="mb-4 text-muted-foreground">
                        {
                          solution.description[
                            intl.locale as keyof typeof solution.description
                          ]
                        }
                      </p>
                      {solution.features && solution.features.length > 0 && (
                        <div className="grid grid-cols-1 gap-3 md:grid-cols-3">
                          {solution.features.map((feature, index) => (
                            <div
                              key={index}
                              className="flex items-center text-sm text-muted-foreground"
                            >
                              <div className="mr-2 h-2 w-2 rounded-full bg-primary"></div>
                              {feature[intl.locale as keyof typeof feature]}
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Outcomes Section */}
            {caseStudy.outcomes.length > 0 && (
              <div className="mt-16">
                <h2 className="mb-8 text-3xl font-bold text-foreground">
                  {intl.formatMessage({
                    id: 'caseStudies.details.outcomes',
                    defaultMessage: 'Outcomes',
                  })}
                </h2>
                <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                  {caseStudy.outcomes.map(outcome => (
                    <div
                      key={outcome.id}
                      className="rounded-lg bg-card p-6 shadow-lg"
                    >
                      <h3 className="mb-3 text-xl font-bold text-foreground">
                        {
                          outcome.title[
                            intl.locale as keyof typeof outcome.title
                          ]
                        }
                      </h3>
                      <p className="text-muted-foreground">
                        {
                          outcome.description[
                            intl.locale as keyof typeof outcome.description
                          ]
                        }
                      </p>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Tags */}
            {caseStudy.tags.length > 0 && (
              <div className="mt-16 border-t border-border pt-8">
                <div className="flex flex-wrap items-center gap-3">
                  <Tag className="h-5 w-5 text-muted-foreground" />
                  {caseStudy.tags.map(tag => (
                    <span
                      key={tag}
                      className="rounded-full bg-muted px-3 py-1 text-sm text-muted-foreground"
                    >
                      {tag}
                    </span>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </section>

      {/* Testimonial Section */}
      {caseStudy.testimonial && (
        <section
          ref={testimonialRef}
          data-section="testimonial"
          className="bg-muted/30 px-4 py-16 sm:px-6 lg:px-8"
        >
          <div className="mx-auto max-w-4xl">
            <div
              className={`transition-all duration-1000 ${visibleSections.testimonial ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}`}
            >
              <div className="rounded-lg bg-card p-8 text-center shadow-lg">
                <Quote className="mx-auto mb-6 h-12 w-12 text-primary" />
                <blockquote className="mb-6 text-xl italic text-foreground md:text-2xl">
                  "
                  {
                    caseStudy.testimonial.quote[
                      intl.locale as keyof typeof caseStudy.testimonial.quote
                    ]
                  }
                  "
                </blockquote>
                <div className="flex items-center justify-center">
                  {caseStudy.testimonial.author.avatar && (
                    <img
                      src={caseStudy.testimonial.author.avatar}
                      alt={caseStudy.testimonial.author.name}
                      className="mr-4 h-12 w-12 rounded-full"
                    />
                  )}
                  <div className="text-left">
                    <div className="font-bold text-foreground">
                      {caseStudy.testimonial.author.name}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {
                        caseStudy.testimonial.author.title[
                          intl.locale as keyof typeof caseStudy.testimonial.author.title
                        ]
                      }
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      )}

      {/* Related Case Studies */}
      {relatedCaseStudies.length > 0 && (
        <section
          ref={relatedRef}
          data-section="related"
          className="px-4 py-16 sm:px-6 lg:px-8"
        >
          <div className="mx-auto max-w-7xl">
            <div
              className={`transition-all duration-1000 ${visibleSections.related ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}`}
            >
              <h2 className="mb-12 text-center text-3xl font-bold text-foreground md:text-4xl">
                {intl.formatMessage({
                  id: 'caseStudies.details.relatedCaseStudies',
                  defaultMessage: 'Related Case Studies',
                })}
              </h2>
              <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
                {relatedCaseStudies.map((study, index) => (
                  <article
                    key={study.id}
                    className={`stagger-card overflow-hidden rounded-lg bg-card shadow-lg transition-all duration-300 hover:shadow-xl ${
                      visibleSections.related
                        ? 'translate-y-0 opacity-100'
                        : 'translate-y-8 opacity-0'
                    }`}
                    style={{ transitionDelay: `${100 + index * 100}ms` }}
                  >
                    <img
                      src={study.featuredImage}
                      alt={study.title[intl.locale as keyof typeof study.title]}
                      className="h-48 w-full object-cover"
                    />
                    <div className="p-6">
                      <div className="mb-3 flex items-center text-sm text-muted-foreground">
                        <span className="mr-3 rounded-full bg-primary/10 px-2 py-1 text-xs font-medium text-primary">
                          {
                            study.category.name[
                              intl.locale as keyof typeof study.category.name
                            ]
                          }
                        </span>
                        <Building className="mr-1 h-4 w-4" />
                        <span>{study.client.name}</span>
                      </div>
                      <h3 className="mb-3 text-lg font-bold text-foreground">
                        {study.title[intl.locale as keyof typeof study.title]}
                      </h3>
                      <p className="mb-4 line-clamp-3 text-muted-foreground">
                        {
                          study.excerpt[
                            intl.locale as keyof typeof study.excerpt
                          ]
                        }
                      </p>
                      <Link
                        to={`/case-studies/${study.slug}`}
                        className="inline-flex items-center font-medium text-primary transition-colors hover:text-primary/80"
                      >
                        {intl.formatMessage({
                          id: 'common.readMore',
                          defaultMessage: 'Read More',
                        })}
                        <ChevronRight className="ml-1 h-4 w-4" />
                      </Link>
                    </div>
                  </article>
                ))}
              </div>
            </div>
          </div>
        </section>
      )}
    </div>
  );
}

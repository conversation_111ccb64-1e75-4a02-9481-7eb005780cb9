import { useState, useEffect, useRef, JSX } from 'react';
import { PAGE_METADATA } from '@/constants/site-config';

import { updatePageMetadata } from '@/utils';
import { getTranslation } from '@/utils/translation';
import { useIntl } from 'react-intl';
import {
  Search,
  Star,
  AlertTriangle,
  CheckCircle,
  Bug,
  Zap,
  Shield,
  Trash2,
  Tag as TagIcon,
} from 'lucide-react';
import {
  changelog,
  getChangelogByCategory,
  getChangelogByVersion,
  searchChangelog,
  getLatestVersion,
} from '@/data/public/changelog';
import { ChangelogEntry, ChangelogFilters } from '@/types/public/changelog';
import '@/styles/animations.css';

/**
 * Changelog Page Component
 *
 * Displays product changelog with version history and filtering.
 * Features include:
 * - Latest version highlight
 * - Version filtering and search
 * - Change type categorization
 * - Breaking changes highlighting
 *
 * @returns {JSX.Element} The Changelog Page component
 */
export default function Page(): JSX.Element {
  const intl = useIntl();
  const metadata = PAGE_METADATA.changelog;
  const locale = intl.locale;

  // Helper function to get translated text
  const getText = (text: { en: string; [key: string]: string | undefined }) => {
    return getTranslation(text, locale);
  };
  // Update document metadata
  useEffect(() => {
    const translatedMetadata = {
      ...metadata,
      title: intl.formatMessage({
        id: 'changelog.title',
        defaultMessage: 'Changelog',
      }),
      description: intl.formatMessage({
        id: 'changelog.subtitle',
        defaultMessage:
          'Stay up to date with the latest product updates, features, and improvements',
      }),
    };
    updatePageMetadata(translatedMetadata);
  }, [metadata, intl]);

  const [filteredChangelog, setFilteredChangelog] =
    useState<ChangelogEntry[]>(changelog);
  const [filters] = useState<ChangelogFilters>({});
  const [searchQuery, setSearchQuery] = useState('');
  const [latestSectionClass, setLatestSectionClass] = useState('');

  // Handle professional collapse/expand animation (only when user interacts)
  useEffect(() => {
    if (searchQuery) {
      setLatestSectionClass('slide-out');
    } else {
      // Only add slide-in if we previously had slide-out (user interaction)
      if (latestSectionClass === 'slide-out') {
        setLatestSectionClass('slide-in');
      }
    }
  }, [searchQuery, latestSectionClass]);

  // State to track which sections are visible
  const [visibleSections, setVisibleSections] = useState({
    hero: true,
    latest: true,
    filters: true,
    timeline: true,
  });

  // Refs for the sections
  const heroRef = useRef(null);
  const latestRef = useRef(null);
  const filtersRef = useRef(null);
  const timelineRef = useRef(null);

  const latestVersion = getLatestVersion();

  // Add intersection observer to check when sections are visible
  useEffect(() => {
    const observerOptions = {
      root: null,
      rootMargin: '0px',
      threshold: 0.1,
    };

    const observerCallback = (entries: IntersectionObserverEntry[]) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const sectionId = entry.target.getAttribute('data-section');
          if (sectionId) {
            setVisibleSections(prev => ({
              ...prev,
              [sectionId]: true,
            }));
          }
        }
      });
    };

    const observer = new IntersectionObserver(
      observerCallback,
      observerOptions
    );

    // Observe each section
    if (heroRef.current) observer.observe(heroRef.current);
    if (latestRef.current) observer.observe(latestRef.current);
    if (filtersRef.current) observer.observe(filtersRef.current);
    if (timelineRef.current) observer.observe(timelineRef.current);

    return () => {
      observer.disconnect();
    };
  }, []);

  useEffect(() => {
    let filtered = [...changelog];

    // Apply search filter
    if (searchQuery) {
      filtered = searchChangelog(searchQuery);
    }

    // Apply category filter
    if (filters.category) {
      filtered = getChangelogByCategory(filters.category);
    }

    // Apply version filter
    if (filters.version) {
      filtered = getChangelogByVersion(filters.version);
    }

    // Apply featured filter
    if (filters.featured !== undefined) {
      filtered = filtered.filter(entry => entry.featured === filters.featured);
    }

    // Apply breaking changes filter
    if (filters.breaking !== undefined) {
      filtered = filtered.filter(entry => entry.breaking === filters.breaking);
    }

    // Apply prerelease filter
    if (filters.prerelease !== undefined) {
      filtered = filtered.filter(
        entry => entry.prerelease === filters.prerelease
      );
    }

    // Apply change type filter
    if (filters.type) {
      filtered = filtered.filter(entry =>
        entry.changes.some(change => change.type === filters.type)
      );
    }

    setFilteredChangelog(filtered);
  }, [searchQuery, filters]);

  const getChangeTypeIcon = (type: string) => {
    switch (type) {
      case 'feature':
        return <Star className="h-4 w-4 text-blue-500" />;
      case 'improvement':
        return <Zap className="h-4 w-4 text-green-500" />;
      case 'bugfix':
        return <Bug className="h-4 w-4 text-orange-500" />;
      case 'security':
        return <Shield className="h-4 w-4 text-red-500" />;
      case 'deprecated':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case 'removed':
        return <Trash2 className="h-4 w-4 text-red-600" />;
      default:
        return <CheckCircle className="h-4 w-4 text-gray-500" />;
    }
  };

  const getChangeTypeText = (type: string) => {
    switch (type) {
      case 'feature':
        return intl.formatMessage({
          id: 'changelog.type.feature',
          defaultMessage: 'Feature',
        });
      case 'improvement':
        return intl.formatMessage({
          id: 'changelog.type.improvement',
          defaultMessage: 'Improvement',
        });
      case 'bugfix':
        return intl.formatMessage({
          id: 'changelog.type.bugfix',
          defaultMessage: 'Bug Fix',
        });
      case 'security':
        return intl.formatMessage({
          id: 'changelog.type.security',
          defaultMessage: 'Security',
        });
      case 'deprecated':
        return intl.formatMessage({
          id: 'changelog.type.deprecated',
          defaultMessage: 'Deprecated',
        });
      case 'removed':
        return intl.formatMessage({
          id: 'changelog.type.removed',
          defaultMessage: 'Removed',
        });
      default:
        return type;
    }
  };

  const formatDate = (dateString: string): string => {
    const options: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    };
    return new Date(dateString).toLocaleDateString(locale, options);
  };

  return (
    <div className="bg-background">
      {/* Hero Section */}
      <div
        ref={heroRef}
        data-section="hero"
        className={`section-reveal bg-gradient-to-r from-primary to-primary/80 text-white ${visibleSections.hero ? 'visible' : ''}`}
      >
        <div className="container mx-auto px-4 py-16 md:py-24">
          <div className="animate-hero-content mx-auto max-w-4xl text-center">
            <h1 className="animate-fadeIn mb-6 text-4xl font-bold">
              {intl.formatMessage({
                id: 'changelog.title',
                defaultMessage: 'Product Changelog',
              })}
            </h1>
            <p className="animate-fadeIn animation-delay-200 mb-8 text-xl opacity-90">
              {intl.formatMessage({
                id: 'changelog.subtitle',
                defaultMessage:
                  'Stay up to date with the latest product updates, features, and improvements',
              })}
            </p>

            {/* Search Bar */}
            <div className="animate-fadeIn animation-delay-400 relative mx-auto max-w-2xl">
              <div className="relative">
                <Search className="absolute left-4 top-1/2 h-5 w-5 -translate-y-1/2 transform text-muted-foreground" />
                <input
                  type="text"
                  placeholder={intl.formatMessage({
                    id: 'changelog.search.placeholder',
                    defaultMessage: 'Search changelog...',
                  })}
                  value={searchQuery}
                  onChange={e => setSearchQuery(e.target.value)}
                  className="w-full rounded-xl border border-border bg-background py-4 pl-12 pr-4 text-lg text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-primary/50"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Latest Version */}
      {latestVersion && (
        <div
          ref={latestRef}
          data-section="latest"
          className={`section-reveal bg-muted py-16 ${latestSectionClass ? `slide-collapse ${latestSectionClass}` : ''} ${visibleSections.latest ? 'visible' : ''}`}
        >
          <div className="container mx-auto px-4">
            <div className="mx-auto max-w-4xl">
              <div className="animate-slideUp mb-12 text-center">
                <div className="mb-4 inline-flex items-center rounded-full bg-primary/10 px-3 py-1 text-sm font-medium text-primary">
                  <TagIcon className="mr-2 h-4 w-4" />
                  {intl.formatMessage({
                    id: 'changelog.latest.version',
                    defaultMessage: 'Latest Version',
                  })}
                </div>
                <h2 className="mb-4 text-3xl font-bold md:text-4xl">
                  {latestVersion.version} - {getText(latestVersion.title)}
                </h2>
                <p className="mb-2 text-lg text-muted-foreground">
                  {getText(latestVersion.description)}
                </p>
                <p className="text-sm text-muted-foreground">
                  {intl.formatMessage({
                    id: 'changelog.released',
                    defaultMessage: 'Released',
                  })}
                  : {formatDate(latestVersion.releaseDate)}
                </p>
              </div>

              <div className="animate-fadeIn rounded-xl bg-card p-8 shadow-lg">
                <div className="mb-6 flex flex-wrap gap-4">
                  <div className="rounded-lg bg-primary/10 p-2 text-primary">
                    {getText(latestVersion.category.name)}
                  </div>

                  {latestVersion.breaking && (
                    <div className="flex items-center gap-2 rounded-lg bg-destructive/10 p-2 text-destructive">
                      <AlertTriangle className="h-4 w-4" />
                      {intl.formatMessage({
                        id: 'changelog.breaking.changes',
                        defaultMessage: 'Breaking Changes',
                      })}
                    </div>
                  )}

                  {latestVersion.prerelease && (
                    <div className="flex items-center gap-2 rounded-lg bg-yellow-500/10 p-2 text-yellow-600">
                      <AlertTriangle className="h-4 w-4" />
                      {intl.formatMessage({
                        id: 'changelog.prerelease',
                        defaultMessage: 'Pre-release',
                      })}
                    </div>
                  )}
                </div>

                <div className="space-y-6">
                  {latestVersion.changes.map((change, index) => (
                    <div
                      key={change.id}
                      className="stagger-card animate-fadeIn border-b border-border pb-6 last:border-0"
                      style={{ animationDelay: `${index * 100}ms` }}
                    >
                      <div className="flex items-start gap-3">
                        <div className="mt-1">
                          {getChangeTypeIcon(change.type)}
                        </div>
                        <div>
                          <div className="mb-1 flex items-center gap-2">
                            <h3 className="font-semibold text-foreground">
                              {getText(change.title)}
                            </h3>
                            <span className="rounded-full bg-muted px-2 py-1 text-xs font-medium text-muted-foreground">
                              {getChangeTypeText(change.type)}
                            </span>
                          </div>
                          <p className="text-muted-foreground">
                            {getText(change.description)}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Version History */}
      <section className="bg-background py-16">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl">
            <h2 className="animate-slideUp mb-8 text-2xl font-bold">
              {intl.formatMessage({
                id: 'changelog.version.history',
                defaultMessage: 'Version History',
              })}
            </h2>

            <div className="space-y-8">
              {filteredChangelog.map((entry, index) => (
                <div
                  key={entry.id}
                  className="stagger-card animate-fadeIn rounded-lg bg-muted p-6"
                  style={{ animationDelay: `${index * 150}ms` }}
                >
                  <div className="mb-4 flex items-center justify-between">
                    <h3 className="text-xl font-bold text-foreground">
                      {entry.version} - {getText(entry.title)}
                    </h3>
                    <span className="text-sm text-muted-foreground">
                      {formatDate(entry.releaseDate)}
                    </span>
                  </div>
                  <p className="mb-4 text-muted-foreground">
                    {getText(entry.description)}
                  </p>
                  <div className="space-y-2">
                    {entry.changes.map(change => (
                      <div key={change.id} className="flex items-start gap-2">
                        {getChangeTypeIcon(change.type)}
                        <span className="text-sm text-foreground">
                          {getText(change.title)}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}

import { SITE_INFO, PAGE_METADATA } from '@/constants/site-config';
import { useState, useEffect, useRef } from 'react';
import { updatePageMetadata } from '@/utils';
import { useIntl } from 'react-intl';
import {
  services,
  serviceCategories,
  getServicesByCategory,
  getFeaturedServices,
} from '@/data/public/services';
import { Service } from '@/types/public/services';
import '@/styles/animations.css';

/**
 * Services Page Component
 *
 * Displays a showcase of services organized by categories.
 * Features include:
 * - Featured services section
 * - Category filtering
 * - Service cards with features and pricing
 * - Call-to-action buttons
 *
 * @returns {JSX.Element} The Services Page component
 */
export default function Page() {
  const intl = useIntl();
  const locale = intl.locale;
  const metadata = PAGE_METADATA.services;
  const companyName = SITE_INFO.organization.name;
  const displayName = SITE_INFO.organization.displayName || companyName;

  // Update document metadata
  useEffect(() => {
    const translatedMetadata = {
      ...metadata,
      title: intl.formatMessage({
        id: 'page.services.title',
        defaultMessage: 'Our Services',
      }),
      description: intl.formatMessage({
        id: 'page.services.subtitle',
        defaultMessage:
          'Comprehensive solutions designed to help your business grow and succeed',
      }),
    };
    updatePageMetadata(translatedMetadata);
  }, [metadata, intl]);

  const [selectedCategory, setSelectedCategory] = useState('all');

  // State to track which sections are visible
  const [visibleSections, setVisibleSections] = useState({
    hero: false,
    featuredServices: false,
    servicesGrid: false,
    cta: false,
  });

  // Refs for the sections
  const heroRef = useRef(null);
  const featuredServicesRef = useRef(null);
  const servicesGridRef = useRef(null);
  const ctaRef = useRef(null);

  // Add intersection observer to check when sections are visible
  useEffect(() => {
    const observerOptions = {
      root: null,
      rootMargin: '0px',
      threshold: 0.1,
    };

    const observerCallback = (entries: IntersectionObserverEntry[]) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const sectionId = entry.target.getAttribute('data-section');
          if (sectionId) {
            setVisibleSections(prev => ({
              ...prev,
              [sectionId]: true,
            }));
          }
        }
      });
    };

    const observer = new IntersectionObserver(
      observerCallback,
      observerOptions
    );

    // Observe each section
    if (heroRef.current) observer.observe(heroRef.current);
    if (featuredServicesRef.current)
      observer.observe(featuredServicesRef.current);
    if (servicesGridRef.current) observer.observe(servicesGridRef.current);
    if (ctaRef.current) observer.observe(ctaRef.current);

    return () => {
      observer.disconnect();
    };
  }, []);

  // Filter services by category
  const filteredServices =
    selectedCategory === 'all'
      ? services
      : getServicesByCategory(selectedCategory);

  // Featured services
  const featuredServices = getFeaturedServices();

  // Get translated text helper
  const getTranslatedText = (text: any, locale: string) => {
    if (typeof text === 'string') return text;
    return text[locale] || text.en || '';
  };

  // Format price helper
  const formatPrice = (pricing: Service['pricing']) => {
    if (!pricing) return null;

    const { startingPrice, currency, period } = pricing;
    const periodText =
      period === 'month' ? '/mo' : period === 'year' ? '/yr' : '';

    return `${currency} $${startingPrice.toLocaleString()}${periodText}`;
  };

  // Get icon component (placeholder for now)
  const getIcon = (iconName: string) => {
    // In a real implementation, you'd map icon names to actual icon components
    return (
      <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary/10 text-primary">
        <span className="text-xl font-bold">
          {iconName.charAt(0).toUpperCase()}
        </span>
      </div>
    );
  };

  return (
    <div className="bg-background">
      {/* Hero section */}
      <div
        ref={heroRef}
        data-section="hero"
        className={`section-reveal bg-gradient-to-r from-primary to-primary/80 text-white ${visibleSections.hero ? 'visible' : ''}`}
      >
        <div className="container mx-auto px-4 py-16 md:py-24">
          <div className="animate-hero-content mx-auto max-w-4xl text-center">
            <h1 className="animate-fadeIn mb-6 text-4xl font-bold">
              {intl.formatMessage(
                { id: 'page.services.title' },
                { defaultMessage: 'Our Services' }
              )}
            </h1>
            <p className="hero-element hero-delay-1 mb-8 text-xl opacity-90">
              {intl.formatMessage(
                { id: 'page.services.subtitle' },
                {
                  company: displayName,
                  defaultMessage:
                    'Comprehensive solutions designed to help your business grow and succeed.',
                }
              )}
            </p>
          </div>
        </div>
      </div>

      {/* Featured Services */}
      {featuredServices.length > 0 && (
        <div
          ref={featuredServicesRef}
          data-section="featuredServices"
          className={`section-reveal container mx-auto px-4 py-16 ${visibleSections.featuredServices ? 'visible' : ''}`}
        >
          <div className="mx-auto max-w-6xl">
            <div className="animate-slideUp mb-12 text-center">
              <h2 className="mb-4 text-3xl font-bold">
                {intl.formatMessage({
                  id: 'page.services.featured.title',
                  defaultMessage: 'Featured Services',
                })}
              </h2>
              <p className="text-muted-foreground">
                {intl.formatMessage({
                  id: 'page.services.featured.description',
                  defaultMessage:
                    'Our most popular and comprehensive service offerings',
                })}
              </p>
            </div>

            <div className="grid gap-8 md:grid-cols-2">
              {featuredServices.map((service, index) => (
                <div
                  key={service.id}
                  className="animate-fadeIn transform overflow-hidden rounded-xl border border-border bg-card shadow-sm transition-all hover:-translate-y-1 hover:shadow-md"
                  style={{ animationDelay: `${index * 200}ms` }}
                >
                  <div className="p-8">
                    <div className="mb-6 flex items-start justify-between">
                      <div className="flex items-center">
                        {getIcon(service.icon)}
                        <div className="ml-4">
                          <h3 className="text-xl font-bold text-card-foreground">
                            {getTranslatedText(service.name, locale)}
                          </h3>
                          {service.pricing && (
                            <p className="text-lg font-semibold text-primary">
                              {formatPrice(service.pricing)}
                            </p>
                          )}
                        </div>
                      </div>
                      {service.popular && (
                        <span className="rounded-full bg-primary px-3 py-1 text-xs font-medium text-white">
                          {intl.formatMessage({
                            id: 'page.services.popular',
                            defaultMessage: 'Popular',
                          })}
                        </span>
                      )}
                    </div>

                    <p className="mb-6 text-muted-foreground">
                      {getTranslatedText(service.description, locale)}
                    </p>

                    <div className="mb-6">
                      <h4 className="mb-3 font-semibold text-card-foreground">
                        {intl.formatMessage({
                          id: 'page.services.features',
                          defaultMessage: 'Key Features:',
                        })}
                      </h4>
                      <ul className="space-y-2">
                        {service.features.map((feature, featureIndex) => (
                          <li
                            key={featureIndex}
                            className="flex items-center text-sm"
                          >
                            <svg
                              className="mr-2 h-4 w-4 text-primary"
                              fill="none"
                              viewBox="0 0 24 24"
                              stroke="currentColor"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M5 13l4 4L19 7"
                              />
                            </svg>
                            {getTranslatedText(feature, locale)}
                          </li>
                        ))}
                      </ul>
                    </div>

                    <a
                      href={service.ctaLink}
                      className="shimmer-effect inline-flex w-full items-center justify-center rounded-lg bg-primary px-6 py-3 font-medium text-white transition-colors hover:bg-primary/80"
                    >
                      {getTranslatedText(service.ctaText, locale)}
                      <svg
                        className="ml-2 h-4 w-4"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M14 5l7 7m0 0l-7 7m7-7H3"
                        />
                      </svg>
                    </a>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Services Grid with Category Filter */}
      <div
        ref={servicesGridRef}
        data-section="servicesGrid"
        className={`section-reveal container mx-auto px-4 py-16 ${visibleSections.servicesGrid ? 'visible' : ''}`}
      >
        <div className="mx-auto max-w-6xl">
          <div className="animate-slideUp mb-12 text-center">
            <h2 className="mb-4 text-3xl font-bold">
              {intl.formatMessage({
                id: 'page.services.all.title',
                defaultMessage: 'All Services',
              })}
            </h2>
            <p className="text-muted-foreground">
              {intl.formatMessage({
                id: 'page.services.all.description',
                defaultMessage:
                  'Explore our complete range of professional services',
              })}
            </p>
          </div>

          {/* Category Filter */}
          <div className="animate-fadeIn mb-10">
            <div className="flex justify-center">
              <div className="flex overflow-x-auto pb-1 md:pb-0">
                <button
                  onClick={() => setSelectedCategory('all')}
                  className={`shimmer-effect mr-4 whitespace-nowrap rounded-lg px-6 py-3 font-medium transition-all duration-200 ${
                    selectedCategory === 'all'
                      ? 'bg-primary text-primary-foreground shadow-md'
                      : 'bg-card text-muted-foreground hover:bg-muted hover:text-foreground hover:shadow-sm'
                  }`}
                >
                  {intl.formatMessage({
                    id: 'page.services.category.all',
                    defaultMessage: 'All Services',
                  })}
                </button>
                {serviceCategories.map(category => (
                  <button
                    key={category.id}
                    onClick={() => setSelectedCategory(category.id)}
                    className={`shimmer-effect mr-4 whitespace-nowrap rounded-lg px-6 py-3 font-medium transition-all duration-200 ${
                      selectedCategory === category.id
                        ? 'bg-primary text-primary-foreground shadow-md'
                        : 'bg-card text-muted-foreground hover:bg-muted hover:text-foreground hover:shadow-sm'
                    }`}
                  >
                    {getTranslatedText(category.name, locale)}
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* Services Grid */}
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {filteredServices.map((service, index) => (
              <div
                key={service.id}
                className="stagger-card animate-fadeIn overflow-hidden rounded-lg border border-border bg-card shadow-sm transition-all hover:shadow-md"
                style={{ animationDelay: `${index * 100}ms` }}
              >
                <div className="p-6">
                  <div className="mb-4 flex items-start justify-between">
                    {getIcon(service.icon)}
                    {service.popular && (
                      <span className="rounded-full bg-primary/10 px-2 py-1 text-xs font-medium text-primary">
                        {intl.formatMessage({
                          id: 'page.services.popular',
                          defaultMessage: 'Popular',
                        })}
                      </span>
                    )}
                  </div>

                  <h3 className="mb-2 text-lg font-bold text-card-foreground">
                    {getTranslatedText(service.name, locale)}
                  </h3>

                  {service.pricing && (
                    <p className="mb-3 text-sm font-semibold text-primary">
                      {intl.formatMessage({
                        id: 'page.services.starting.at',
                        defaultMessage: 'Starting at',
                      })}{' '}
                      {formatPrice(service.pricing)}
                    </p>
                  )}

                  <p className="mb-4 text-sm text-muted-foreground">
                    {getTranslatedText(service.description, locale)}
                  </p>

                  <div className="mb-4">
                    <ul className="space-y-1">
                      {service.features
                        .slice(0, 3)
                        .map((feature, featureIndex) => (
                          <li
                            key={featureIndex}
                            className="flex items-center text-xs"
                          >
                            <svg
                              className="mr-2 h-3 w-3 text-primary"
                              fill="none"
                              viewBox="0 0 24 24"
                              stroke="currentColor"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M5 13l4 4L19 7"
                              />
                            </svg>
                            {getTranslatedText(feature, locale)}
                          </li>
                        ))}
                      {service.features.length > 3 && (
                        <li className="text-xs text-muted-foreground">
                          +{service.features.length - 3} more features
                        </li>
                      )}
                    </ul>
                  </div>

                  <a
                    href={service.ctaLink}
                    className="shimmer-effect inline-flex w-full items-center justify-center rounded-md bg-primary px-4 py-2 text-sm font-medium text-white transition-colors hover:bg-primary/80"
                  >
                    {getTranslatedText(service.ctaText, locale)}
                    <svg
                      className="ml-1 h-3 w-3"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M14 5l7 7m0 0l-7 7m7-7H3"
                      />
                    </svg>
                  </a>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Call to Action */}
      <div
        ref={ctaRef}
        data-section="cta"
        className={`section-reveal bg-muted py-16 ${visibleSections.cta ? 'visible' : ''}`}
      >
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-3xl text-center">
            <h2 className="animate-slideUp mb-4 text-3xl font-bold">
              {intl.formatMessage({
                id: 'page.services.cta.title',
                defaultMessage: 'Ready to Get Started?',
              })}
            </h2>
            <p className="animate-slideUp animation-delay-200 mb-8 text-muted-foreground">
              {intl.formatMessage({
                id: 'page.services.cta.description',
                defaultMessage:
                  "Let's discuss how our services can help your business achieve its goals. Contact us for a free consultation.",
              })}
            </p>
            <div className="animate-fadeIn animation-delay-300 flex flex-col gap-4 sm:flex-row sm:justify-center">
              <a
                href="/contact"
                className="shimmer-effect inline-flex items-center justify-center rounded-lg bg-primary px-8 py-3 font-medium text-white transition-colors hover:bg-primary/80"
              >
                {intl.formatMessage({
                  id: 'page.services.cta.contact',
                  defaultMessage: 'Contact Us',
                })}
                <svg
                  className="ml-2 h-4 w-4"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M14 5l7 7m0 0l-7 7m7-7H3"
                  />
                </svg>
              </a>
              <a
                href="/help"
                className="shimmer-effect inline-flex items-center justify-center rounded-lg border border-border bg-background px-8 py-3 font-medium text-foreground transition-colors hover:bg-muted"
              >
                {intl.formatMessage({
                  id: 'page.services.cta.learn.more',
                  defaultMessage: 'Learn More',
                })}
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

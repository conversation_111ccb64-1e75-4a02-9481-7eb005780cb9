import { PAGE_METADATA } from '@/constants/site-config';
import { useState, useEffect, useRef } from 'react';
import { updatePageMetadata } from '@/utils';
import { useIntl } from 'react-intl';
import { Link } from 'react-router-dom';
import {
  Search,
  Calendar,
  MapPin,
  Users,
  Filter,
  ChevronRight,
  Clock,
  Tag,
  Star,
} from 'lucide-react';
import {
  events,
  eventCategories,
  getFeaturedEvents,
  getEventsByCategory,
  searchEvents,
} from '@/data/public/events';
import { Event, EventFilters } from '@/types/public/events';
import '@/styles/animations.css';

/**
 * Events Page Component
 *
 * Displays events with filtering and search functionality.
 * Features include:
 * - Featured events section
 * - Category and status filtering
 * - Search functionality
 * - Upcoming/past events toggle
 *
 * @returns {JSX.Element} The Events Page component
 */
export default function Page() {
  const intl = useIntl();
  const metadata = PAGE_METADATA.events;

  // Update document metadata
  useEffect(() => {
    const translatedMetadata = {
      ...metadata,
      title: intl.formatMessage({
        id: 'events.title',
        defaultMessage: 'Events',
      }),
      description: intl.formatMessage({
        id: 'events.subtitle',
        defaultMessage:
          'Join our upcoming events, workshops, and conferences to learn, network, and grow with industry experts',
      }),
    };
    updatePageMetadata(translatedMetadata);
  }, [metadata, intl]);

  const [filteredEvents, setFilteredEvents] = useState<Event[]>(events);
  const [filters, setFilters] = useState<EventFilters>({});
  const [searchQuery, setSearchQuery] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [activeTab, setActiveTab] = useState<'upcoming' | 'past'>('upcoming');

  // State to track which sections are visible
  const [visibleSections, setVisibleSections] = useState({
    hero: true,
    featured: true,
    filters: true,
    grid: true,
  });

  // Refs for the sections
  const heroRef = useRef(null);
  const featuredRef = useRef(null);
  const filtersRef = useRef(null);
  const gridRef = useRef(null);

  const featuredEvents = getFeaturedEvents();

  // Add intersection observer to check when sections are visible
  useEffect(() => {
    const observerOptions = {
      root: null,
      rootMargin: '0px',
      threshold: 0.1,
    };

    const observerCallback = (entries: IntersectionObserverEntry[]) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const sectionId = entry.target.getAttribute('data-section');
          if (sectionId) {
            setVisibleSections(prev => ({
              ...prev,
              [sectionId]: true,
            }));
          }
        }
      });
    };

    const observer = new IntersectionObserver(
      observerCallback,
      observerOptions
    );

    // Observe each section
    if (heroRef.current) observer.observe(heroRef.current);
    if (featuredRef.current) observer.observe(featuredRef.current);
    if (filtersRef.current) observer.observe(filtersRef.current);
    if (gridRef.current) observer.observe(gridRef.current);

    return () => {
      observer.disconnect();
    };
  }, []);

  useEffect(() => {
    let filtered = events.filter(event => event.status !== 'cancelled');

    // Apply tab filter
    const now = new Date();
    if (activeTab === 'upcoming') {
      filtered = filtered.filter(
        event => new Date(event.startDate) > now || event.status === 'ongoing'
      );
    } else {
      filtered = filtered.filter(
        event => new Date(event.endDate) < now && event.status === 'completed'
      );
    }

    // Apply search filter
    if (searchQuery) {
      filtered = searchEvents(searchQuery).filter(event => {
        if (activeTab === 'upcoming') {
          return new Date(event.startDate) > now || event.status === 'ongoing';
        } else {
          return new Date(event.endDate) < now && event.status === 'completed';
        }
      });
    }

    // Apply category filter
    if (filters.category) {
      filtered = getEventsByCategory(filters.category).filter(event => {
        if (activeTab === 'upcoming') {
          return new Date(event.startDate) > now || event.status === 'ongoing';
        } else {
          return new Date(event.endDate) < now && event.status === 'completed';
        }
      });
    }

    // Apply featured filter
    if (filters.featured) {
      filtered = filtered.filter(event => event.featured);
    }

    // Sort events
    if (activeTab === 'upcoming') {
      filtered.sort(
        (a, b) =>
          new Date(a.startDate).getTime() - new Date(b.startDate).getTime()
      );
    } else {
      filtered.sort(
        (a, b) => new Date(b.endDate).getTime() - new Date(a.endDate).getTime()
      );
    }

    setFilteredEvents(filtered);
  }, [filters, searchQuery, activeTab]);

  const handleFilterChange = (
    key: keyof EventFilters,
    value: string | boolean
  ) => {
    setFilters(prev => ({
      ...prev,
      [key]: value === prev[key] ? undefined : value,
    }));
  };

  const clearFilters = () => {
    setFilters({});
    setSearchQuery('');
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString(intl.locale, {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString(intl.locale, {
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getEventStatus = (event: Event) => {
    const now = new Date();
    const startDate = new Date(event.startDate);
    const endDate = new Date(event.endDate);

    if (now < startDate) return 'upcoming';
    if (now >= startDate && now <= endDate) return 'ongoing';
    return 'completed';
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'upcoming':
        return 'bg-blue-100 text-blue-800';
      case 'ongoing':
        return 'bg-green-100 text-green-800';
      case 'completed':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="bg-background">
      {/* Hero Section */}
      <div
        ref={heroRef}
        data-section="hero"
        className={`section-reveal bg-gradient-to-r from-primary to-primary/80 text-white ${visibleSections.hero ? 'visible' : ''}`}
      >
        <div className="container mx-auto px-4 py-16 md:py-24">
          <div className="animate-hero-content mx-auto max-w-4xl text-center">
            <h1 className="animate-fadeIn mb-6 text-4xl font-bold">
              {intl.formatMessage({
                id: 'events.title',
                defaultMessage: 'Events',
              })}
            </h1>
            <p className="hero-element hero-delay-1 mb-8 text-xl opacity-90">
              {intl.formatMessage({
                id: 'events.subtitle',
                defaultMessage:
                  'Join our upcoming events, workshops, and conferences to learn, network, and grow with industry experts',
              })}
            </p>
          </div>
        </div>
      </div>

      {/* Featured Events */}
      {featuredEvents.length > 0 && (
        <div
          ref={featuredRef}
          data-section="featured"
          className={`section-reveal bg-muted py-16 ${visibleSections.featured ? 'visible' : ''}`}
        >
          <div className="container mx-auto px-4">
            <div className="mx-auto max-w-7xl">
              <h2 className="animate-slideUp mb-12 text-center text-3xl font-bold md:text-4xl">
                {intl.formatMessage({
                  id: 'events.featured',
                  defaultMessage: 'Featured Events',
                })}
              </h2>
              <div className="grid grid-cols-1 gap-8 lg:grid-cols-2">
                {featuredEvents.slice(0, 2).map((event, index) => (
                  <article
                    key={event.id}
                    className="stagger-card animate-fadeIn overflow-hidden rounded-lg bg-card shadow-sm transition-all duration-300 hover:-translate-y-1 hover:shadow-md"
                    style={{ animationDelay: `${index * 150}ms` }}
                  >
                    <img
                      src={event.featuredImage}
                      alt={event.title[intl.locale as keyof typeof event.title]}
                      className="h-64 w-full object-cover"
                    />
                    <div className="p-6">
                      <div className="mb-3 flex items-center justify-between">
                        <span
                          className="rounded-full px-3 py-1 text-xs font-medium"
                          style={{
                            backgroundColor: `${event.category.color}20`,
                            color: event.category.color,
                          }}
                        >
                          {
                            event.category.name[
                              intl.locale as keyof typeof event.category.name
                            ]
                          }
                        </span>
                        <div className="flex items-center">
                          <Star className="mr-1 h-4 w-4 text-yellow-500" />
                          <span className="text-sm text-muted-foreground">
                            {intl.formatMessage({
                              id: 'events.featured',
                              defaultMessage: 'Featured',
                            })}
                          </span>
                        </div>
                      </div>

                      <h3 className="mb-3 text-xl font-bold text-foreground">
                        {event.title[intl.locale as keyof typeof event.title]}
                      </h3>

                      <p className="mb-4 text-muted-foreground">
                        {
                          event.excerpt[
                            intl.locale as keyof typeof event.excerpt
                          ]
                        }
                      </p>

                      <div className="mb-4 space-y-2">
                        <div className="flex items-center text-sm text-muted-foreground">
                          <Calendar className="mr-2 h-4 w-4" />
                          <span>{formatDate(event.startDate)}</span>
                          {event.startDate !== event.endDate && (
                            <span> - {formatDate(event.endDate)}</span>
                          )}
                        </div>
                        <div className="flex items-center text-sm text-muted-foreground">
                          <Clock className="mr-2 h-4 w-4" />
                          <span>{formatTime(event.startDate)}</span>
                        </div>
                        <div className="flex items-center text-sm text-muted-foreground">
                          <MapPin className="mr-2 h-4 w-4" />
                          <span>
                            {event.location.city}, {event.location.country}
                          </span>
                        </div>
                        {event.registration.maxAttendees && (
                          <div className="flex items-center text-sm text-muted-foreground">
                            <Users className="mr-2 h-4 w-4" />
                            <span>
                              {event.registration.currentAttendees || 0} /{' '}
                              {event.registration.maxAttendees}
                              {intl.formatMessage({
                                id: 'events.attendees',
                                defaultMessage: ' attendees',
                              })}
                            </span>
                          </div>
                        )}
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          {event.registration.price &&
                          event.registration.price.amount > 0 ? (
                            <span className="text-lg font-bold text-primary">
                              ${event.registration.price.amount}
                            </span>
                          ) : (
                            <span className="text-lg font-bold text-green-600">
                              {intl.formatMessage({
                                id: 'events.free',
                                defaultMessage: 'Free',
                              })}
                            </span>
                          )}
                        </div>
                        <Link
                          to={`/events/${event.slug}`}
                          className="inline-flex items-center font-medium text-primary transition-colors hover:text-primary/80"
                        >
                          {intl.formatMessage({
                            id: 'caseStudies.learnMore',
                            defaultMessage: 'Learn More',
                          })}
                          <ChevronRight className="ml-1 h-4 w-4" />
                        </Link>
                      </div>
                    </div>
                  </article>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Search and Filters */}
      <div
        ref={filtersRef}
        data-section="filters"
        className={`section-reveal container mx-auto px-4 py-8 ${visibleSections.filters ? 'visible' : ''}`}
      >
        <div className="mx-auto max-w-7xl">
          <div className="animate-fadeIn rounded-lg bg-card p-6 shadow-sm">
            {/* Tabs */}
            <div className="mb-6 flex space-x-1 rounded-lg bg-muted p-1">
              <button
                onClick={() => setActiveTab('upcoming')}
                className={`shimmer-effect flex-1 rounded-md px-4 py-2 text-sm font-medium transition-colors ${
                  activeTab === 'upcoming'
                    ? 'bg-background text-foreground shadow-sm'
                    : 'text-muted-foreground hover:text-foreground'
                }`}
              >
                {intl.formatMessage({
                  id: 'events.upcoming',
                  defaultMessage: 'Upcoming Events',
                })}
              </button>
              <button
                onClick={() => setActiveTab('past')}
                className={`shimmer-effect flex-1 rounded-md px-4 py-2 text-sm font-medium transition-colors ${
                  activeTab === 'past'
                    ? 'bg-background text-foreground shadow-sm'
                    : 'text-muted-foreground hover:text-foreground'
                }`}
              >
                {intl.formatMessage({
                  id: 'events.past',
                  defaultMessage: 'Past Events',
                })}
              </button>
            </div>

            <div className="flex flex-col gap-4 lg:flex-row">
              {/* Search */}
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 transform text-muted-foreground" />
                <input
                  type="text"
                  placeholder={intl.formatMessage({
                    id: 'events.searchPlaceholder',
                    defaultMessage: 'Search events...',
                  })}
                  value={searchQuery}
                  onChange={e => setSearchQuery(e.target.value)}
                  className="w-full rounded-lg border border-input bg-background py-2 pl-10 pr-4 text-foreground focus:border-transparent focus:ring-2 focus:ring-ring"
                />
              </div>

              {/* Filter Toggle */}
              <button
                onClick={() => setShowFilters(!showFilters)}
                className="flex items-center rounded-lg bg-muted px-4 py-2 text-muted-foreground transition-colors hover:bg-muted/80"
              >
                <Filter className="mr-2 h-4 w-4" />
                {intl.formatMessage({
                  id: 'common.filters',
                  defaultMessage: 'Filters',
                })}
              </button>
            </div>

            {/* Filters */}
            {showFilters && (
              <div className="animate-slideUp mt-6 border-t border-border pt-6">
                <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                  {/* Category Filter */}
                  <div>
                    <label className="mb-2 block text-sm font-medium text-foreground">
                      {intl.formatMessage({
                        id: 'events.category',
                        defaultMessage: 'Category',
                      })}
                    </label>
                    <div className="space-y-2">
                      {eventCategories.map(category => (
                        <label key={category.id} className="flex items-center">
                          <input
                            type="checkbox"
                            checked={filters.category === category.slug}
                            onChange={() =>
                              handleFilterChange('category', category.slug)
                            }
                            className="rounded border-input text-primary focus:ring-ring"
                          />
                          <span className="ml-2 text-sm text-foreground">
                            {
                              category.name[
                                intl.locale as keyof typeof category.name
                              ]
                            }
                          </span>
                        </label>
                      ))}
                    </div>
                  </div>

                  {/* Featured Filter */}
                  <div>
                    <label className="mb-2 block text-sm font-medium text-foreground">
                      {intl.formatMessage({
                        id: 'events.type',
                        defaultMessage: 'Type',
                      })}
                    </label>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={filters.featured || false}
                        onChange={() => handleFilterChange('featured', true)}
                        className="rounded border-input text-primary focus:ring-ring"
                      />
                      <span className="ml-2 text-sm text-foreground">
                        {intl.formatMessage({
                          id: 'events.featuredOnly',
                          defaultMessage: 'Featured Only',
                        })}
                      </span>
                    </label>
                  </div>

                  {/* Clear Filters */}
                  <div className="flex items-end">
                    <button
                      onClick={clearFilters}
                      className="px-4 py-2 text-sm text-muted-foreground underline transition-colors hover:text-foreground"
                    >
                      {intl.formatMessage({
                        id: 'common.clearFilters',
                        defaultMessage: 'Clear Filters',
                      })}
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Events Grid */}
      <div
        ref={gridRef}
        data-section="grid"
        className={`section-reveal container mx-auto px-4 py-16 ${visibleSections.grid ? 'visible' : ''}`}
      >
        <div className="mx-auto max-w-7xl">
          {filteredEvents.length === 0 ? (
            <div className="animate-fadeIn py-12 text-center">
              <p className="text-lg text-muted-foreground">
                {intl.formatMessage({
                  id: 'events.noResults',
                  defaultMessage: 'No events found matching your criteria.',
                })}
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
              {filteredEvents.map((event, index) => {
                const status = getEventStatus(event);
                return (
                  <article
                    key={event.id}
                    className="stagger-card animate-fadeIn overflow-hidden rounded-lg bg-card shadow-sm transition-all duration-300 hover:-translate-y-1 hover:shadow-md"
                    style={{ animationDelay: `${index * 100}ms` }}
                  >
                    <img
                      src={event.featuredImage}
                      alt={event.title[intl.locale as keyof typeof event.title]}
                      className="h-48 w-full object-cover"
                    />
                    <div className="p-6">
                      <div className="mb-3 flex items-center justify-between">
                        <span
                          className="rounded-full px-2 py-1 text-xs font-medium"
                          style={{
                            backgroundColor: `${event.category.color}20`,
                            color: event.category.color,
                          }}
                        >
                          {
                            event.category.name[
                              intl.locale as keyof typeof event.category.name
                            ]
                          }
                        </span>
                        <span
                          className={`rounded-full px-2 py-1 text-xs font-medium ${getStatusColor(status)}`}
                        >
                          {intl.formatMessage({
                            id: `events.status.${status}`,
                            defaultMessage:
                              status.charAt(0).toUpperCase() + status.slice(1),
                          })}
                        </span>
                      </div>

                      <h3 className="mb-3 text-lg font-bold text-foreground">
                        {event.title[intl.locale as keyof typeof event.title]}
                      </h3>

                      <p className="mb-4 line-clamp-3 text-muted-foreground">
                        {
                          event.excerpt[
                            intl.locale as keyof typeof event.excerpt
                          ]
                        }
                      </p>

                      <div className="mb-4 space-y-2">
                        <div className="flex items-center text-sm text-muted-foreground">
                          <Calendar className="mr-2 h-4 w-4" />
                          <span>{formatDate(event.startDate)}</span>
                        </div>
                        <div className="flex items-center text-sm text-muted-foreground">
                          <MapPin className="mr-2 h-4 w-4" />
                          <span>{event.location.city}</span>
                        </div>
                        {event.registration.price && (
                          <div className="flex items-center text-sm text-muted-foreground">
                            <span className="font-medium">
                              {event.registration.price.amount > 0
                                ? `$${event.registration.price.amount}`
                                : intl.formatMessage({
                                    id: 'events.free',
                                    defaultMessage: 'Free',
                                  })}
                            </span>
                          </div>
                        )}
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          {event.speakers.length > 0 && (
                            <div className="flex -space-x-2">
                              {event.speakers.slice(0, 3).map(speaker => (
                                <img
                                  key={speaker.id}
                                  src={
                                    speaker.avatar ||
                                    'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=32&h=32&fit=crop&crop=face'
                                  }
                                  alt={speaker.name}
                                  className="h-8 w-8 rounded-full border-2 border-background"
                                />
                              ))}
                              {event.speakers.length > 3 && (
                                <div className="flex h-8 w-8 items-center justify-center rounded-full border-2 border-background bg-muted">
                                  <span className="text-xs text-muted-foreground">
                                    +{event.speakers.length - 3}
                                  </span>
                                </div>
                              )}
                            </div>
                          )}
                        </div>
                        <Link
                          to={`/events/${event.slug}`}
                          className="text-sm font-medium text-primary transition-colors hover:text-primary/80"
                        >
                          {intl.formatMessage({
                            id: 'caseStudies.learnMore',
                            defaultMessage: 'Learn More',
                          })}
                        </Link>
                      </div>

                      {event.tags.length > 0 && (
                        <div className="mt-4 border-t border-border pt-4">
                          <div className="flex flex-wrap items-center gap-2">
                            <Tag className="h-4 w-4 text-muted-foreground" />
                            {event.tags.slice(0, 3).map(tag => (
                              <span
                                key={tag}
                                className="rounded-full bg-muted px-2 py-1 text-xs text-muted-foreground"
                              >
                                {tag}
                              </span>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </article>
                );
              })}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

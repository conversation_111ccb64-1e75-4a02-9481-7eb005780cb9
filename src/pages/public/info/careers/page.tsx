import { SITE_INFO, PAGE_METADATA } from '@/constants/site-config';
import { useState, useEffect, useRef } from 'react';
import { updatePageMetadata } from '@/utils';
import { useIntl } from 'react-intl';
import {
  filterJobsByDepartment,
  departments,
  getTranslatedText,
  getTranslatedArray,
} from '@/data/public/careers';
import '@/styles/animations.css'; // Import the centralized animations

/**
 * Careers Page
 *
 * Displays current job openings and information about working at the company.
 * This page showcases career opportunities and employee benefits.
 *
 * @returns {JSX.Element} The Careers Page component
 */
export default function Page() {
  const intl = useIntl();
  const locale = intl.locale;
  const metadata = PAGE_METADATA.careers;
  const companyName = SITE_INFO.organization.name;
  const displayName = SITE_INFO.organization.displayName || companyName;
  const [selectedDepartment, setSelectedDepartment] = useState('all');

  // State to track which sections are visible
  const [visibleSections, setVisibleSections] = useState({
    whyWork: false,
    benefits: false,
    openings: false,
  });

  // Refs for the sections
  const whyWorkRef = useRef(null);
  const benefitsRef = useRef(null);
  const openingsRef = useRef(null);

  // Update document metadata
  useEffect(() => {
    const translatedTitle = intl.formatMessage({ id: 'page.careers.title' });
    const translatedDescription = intl.formatMessage({
      id: 'page.careers.subtitle',
    });

    updatePageMetadata({
      ...metadata,
      title: translatedTitle,
      description: translatedDescription,
    });
  }, [metadata, intl]);

  // Add intersection observer to check when sections are visible
  useEffect(() => {
    const observerOptions = {
      root: null,
      rootMargin: '0px',
      threshold: 0.1,
    };

    const observerCallback = (entries: IntersectionObserverEntry[]) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const sectionId = entry.target.getAttribute('data-section');
          if (sectionId) {
            setVisibleSections(prev => ({
              ...prev,
              [sectionId]: true,
            }));
          }
        }
      });
    };

    const observer = new IntersectionObserver(
      observerCallback,
      observerOptions
    );

    // Observe each section
    if (whyWorkRef.current) observer.observe(whyWorkRef.current);
    if (benefitsRef.current) observer.observe(benefitsRef.current);
    if (openingsRef.current) observer.observe(openingsRef.current);

    return () => {
      observer.disconnect();
    };
  }, []);

  // Filter job openings by selected department
  const filteredJobs = filterJobsByDepartment(selectedDepartment);

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mx-auto max-w-5xl">
        <h1 className="animate-fadeIn mb-2 text-3xl font-bold">
          {intl.formatMessage(
            { id: 'page.careers.title' },
            { company: displayName }
          )}
        </h1>
        <p className="animate-fadeIn animation-delay-200 mb-8 text-lg text-muted-foreground">
          {intl.formatMessage({ id: 'page.careers.subtitle' })}
        </p>

        {/* Why work with us section */}
        <section
          ref={whyWorkRef}
          data-section="whyWork"
          className={`section-reveal mb-12 ${visibleSections.whyWork ? 'visible' : ''}`}
        >
          <h2 className="animate-slideUp mb-6 text-2xl font-semibold">
            {intl.formatMessage({ id: 'page.careers.why.title' })}
          </h2>
          <div className="grid gap-6 md:grid-cols-3">
            <div className="stagger-card animate-fadeIn rounded-lg border border-border bg-card p-6 shadow-sm">
              <h3 className="mb-3 text-xl font-medium">
                {intl.formatMessage({
                  id: 'page.careers.why.meaningful.title',
                })}
              </h3>
              <p className="text-muted-foreground">
                {intl.formatMessage({
                  id: 'page.careers.why.meaningful.description',
                })}
              </p>
            </div>
            <div className="stagger-card animate-fadeIn animation-delay-200 rounded-lg border border-border bg-card p-6 shadow-sm">
              <h3 className="mb-3 text-xl font-medium">
                {intl.formatMessage({ id: 'page.careers.why.growth.title' })}
              </h3>
              <p className="text-muted-foreground">
                {intl.formatMessage({
                  id: 'page.careers.why.growth.description',
                })}
              </p>
            </div>
            <div className="stagger-card animate-fadeIn animation-delay-300 rounded-lg border border-border bg-card p-6 shadow-sm">
              <h3 className="mb-3 text-xl font-medium">
                {intl.formatMessage({ id: 'page.careers.why.culture.title' })}
              </h3>
              <p className="text-muted-foreground">
                {intl.formatMessage({
                  id: 'page.careers.why.culture.description',
                })}
              </p>
            </div>
          </div>
        </section>

        {/* Benefits section */}
        <section
          ref={benefitsRef}
          data-section="benefits"
          className={`section-reveal mb-12 ${visibleSections.benefits ? 'visible' : ''}`}
        >
          <h2 className="animate-slideUp mb-6 text-2xl font-semibold">
            {intl.formatMessage({ id: 'page.careers.benefits.title' })}
          </h2>
          <div className="animate-fadeIn animation-delay-200 rounded-lg bg-primary/5 p-6">
            <div className="grid gap-6 md:grid-cols-2">
              <div>
                <h3 className="mb-3 text-xl font-medium">
                  {intl.formatMessage({
                    id: 'page.careers.benefits.health.title',
                  })}
                </h3>
                <ul className="space-y-2">
                  <li className="flex items-start">
                    <svg
                      className="mr-2 mt-0.5 h-5 w-5 text-primary"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M5 13l4 4L19 7"
                      />
                    </svg>
                    <span>
                      {intl.formatMessage({
                        id: 'page.careers.benefits.health.insurance',
                      })}
                    </span>
                  </li>
                  <li className="flex items-start">
                    <svg
                      className="mr-2 mt-0.5 h-5 w-5 text-primary"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M5 13l4 4L19 7"
                      />
                    </svg>
                    <span>
                      {intl.formatMessage({
                        id: 'page.careers.benefits.health.mental',
                      })}
                    </span>
                  </li>
                  <li className="flex items-start">
                    <svg
                      className="mr-2 mt-0.5 h-5 w-5 text-primary"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M5 13l4 4L19 7"
                      />
                    </svg>
                    <span>
                      {intl.formatMessage({
                        id: 'page.careers.benefits.health.wellness',
                      })}
                    </span>
                  </li>
                </ul>
              </div>
              <div>
                <h3 className="mb-3 text-xl font-medium">
                  {intl.formatMessage({
                    id: 'page.careers.benefits.work.title',
                  })}
                </h3>
                <ul className="space-y-2">
                  <li className="flex items-start">
                    <svg
                      className="mr-2 mt-0.5 h-5 w-5 text-primary"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M5 13l4 4L19 7"
                      />
                    </svg>
                    <span>
                      {intl.formatMessage({
                        id: 'page.careers.benefits.work.flexible',
                      })}
                    </span>
                  </li>
                  <li className="flex items-start">
                    <svg
                      className="mr-2 mt-0.5 h-5 w-5 text-primary"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M5 13l4 4L19 7"
                      />
                    </svg>
                    <span>
                      {intl.formatMessage({
                        id: 'page.careers.benefits.work.remote',
                      })}
                    </span>
                  </li>
                  <li className="flex items-start">
                    <svg
                      className="mr-2 mt-0.5 h-5 w-5 text-primary"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M5 13l4 4L19 7"
                      />
                    </svg>
                    <span>
                      {intl.formatMessage({
                        id: 'page.careers.benefits.work.pto',
                      })}
                    </span>
                  </li>
                </ul>
              </div>
              <div>
                <h3 className="mb-3 text-xl font-medium">
                  {intl.formatMessage({
                    id: 'page.careers.benefits.financial.title',
                  })}
                </h3>
                <ul className="space-y-2">
                  <li className="flex items-start">
                    <svg
                      className="mr-2 mt-0.5 h-5 w-5 text-primary"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M5 13l4 4L19 7"
                      />
                    </svg>
                    <span>
                      {intl.formatMessage({
                        id: 'page.careers.benefits.financial.salary',
                      })}
                    </span>
                  </li>
                  <li className="flex items-start">
                    <svg
                      className="mr-2 mt-0.5 h-5 w-5 text-primary"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M5 13l4 4L19 7"
                      />
                    </svg>
                    <span>
                      {intl.formatMessage({
                        id: 'page.careers.benefits.financial.equity',
                      })}
                    </span>
                  </li>
                  <li className="flex items-start">
                    <svg
                      className="mr-2 mt-0.5 h-5 w-5 text-primary"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M5 13l4 4L19 7"
                      />
                    </svg>
                    <span>
                      {intl.formatMessage({
                        id: 'page.careers.benefits.financial.401k',
                      })}
                    </span>
                  </li>
                </ul>
              </div>
              <div>
                <h3 className="mb-3 text-xl font-medium">
                  {intl.formatMessage({
                    id: 'page.careers.benefits.development.title',
                  })}
                </h3>
                <ul className="space-y-2">
                  <li className="flex items-start">
                    <svg
                      className="mr-2 mt-0.5 h-5 w-5 text-primary"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M5 13l4 4L19 7"
                      />
                    </svg>
                    <span>
                      {intl.formatMessage({
                        id: 'page.careers.benefits.development.stipend',
                      })}
                    </span>
                  </li>
                  <li className="flex items-start">
                    <svg
                      className="mr-2 mt-0.5 h-5 w-5 text-primary"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M5 13l4 4L19 7"
                      />
                    </svg>
                    <span>
                      {intl.formatMessage({
                        id: 'page.careers.benefits.development.conference',
                      })}
                    </span>
                  </li>
                  <li className="flex items-start">
                    <svg
                      className="mr-2 mt-0.5 h-5 w-5 text-primary"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M5 13l4 4L19 7"
                      />
                    </svg>
                    <span>
                      {intl.formatMessage({
                        id: 'page.careers.benefits.development.mentorship',
                      })}
                    </span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </section>

        {/* Current openings section */}
        <section
          ref={openingsRef}
          data-section="openings"
          className={`section-reveal ${visibleSections.openings ? 'visible' : ''}`}
        >
          <h2 className="animate-slideUp mb-6 text-2xl font-semibold">
            {intl.formatMessage({ id: 'page.careers.openings.title' })}
          </h2>

          {/* Department filter */}
          <div className="animate-fadeIn animation-delay-200 mb-6">
            <div className="flex overflow-x-auto pb-1">
              {departments.map(dept => (
                <button
                  key={dept.id}
                  onClick={() => setSelectedDepartment(dept.id)}
                  className={`mr-4 whitespace-nowrap px-4 py-2 font-medium ${
                    selectedDepartment === dept.id
                      ? 'border-b-2 border-primary text-primary'
                      : 'text-muted-foreground hover:text-foreground'
                  }`}
                >
                  {getTranslatedText(dept.name, locale)}
                </button>
              ))}
            </div>
          </div>

          {/* Job listings */}
          {filteredJobs.length > 0 ? (
            <div className="animate-fadeIn animation-delay-300 space-y-6">
              {filteredJobs.map((job, index) => (
                <div
                  key={job.id}
                  className="stagger-card rounded-lg border border-border bg-card p-6 shadow-sm"
                  style={{ animationDelay: `${index * 150}ms` }}
                >
                  <div className="mb-4 border-b pb-4">
                    <h3 className="mb-1 text-xl font-semibold">
                      {getTranslatedText(job.title, locale)}
                    </h3>
                    <div className="mb-2 flex flex-wrap gap-2">
                      <span className="inline-block rounded bg-primary/10 px-2 py-1 text-sm text-primary">
                        {getTranslatedText(job.location, locale)}
                      </span>
                      <span className="inline-block rounded bg-green-100 px-2 py-1 text-sm text-green-800">
                        {getTranslatedText(job.type, locale)}
                      </span>
                    </div>
                    <p className="text-muted-foreground">
                      {getTranslatedText(job.description, locale)}
                    </p>
                  </div>

                  <div className="grid gap-6 md:grid-cols-2">
                    <div>
                      <h4 className="mb-2 font-medium">
                        {intl.formatMessage({
                          id: 'page.careers.openings.responsibilities',
                        })}
                      </h4>
                      <ul className="space-y-1">
                        {getTranslatedArray(job.responsibilities, locale).map(
                          (item, index) => (
                            <li key={index} className="flex items-start">
                              <svg
                                className="mr-2 mt-0.5 h-5 w-5 flex-shrink-0 text-primary"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke="currentColor"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M5 13l4 4L19 7"
                                />
                              </svg>
                              <span>{item}</span>
                            </li>
                          )
                        )}
                      </ul>
                    </div>
                    <div>
                      <h4 className="mb-2 font-medium">
                        {intl.formatMessage({
                          id: 'page.careers.openings.requirements',
                        })}
                      </h4>
                      <ul className="space-y-1">
                        {getTranslatedArray(job.requirements, locale).map(
                          (item, index) => (
                            <li key={index} className="flex items-start">
                              <svg
                                className="mr-2 mt-0.5 h-5 w-5 flex-shrink-0 text-primary"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke="currentColor"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M5 13l4 4L19 7"
                                />
                              </svg>
                              <span>{item}</span>
                            </li>
                          )
                        )}
                      </ul>
                    </div>
                  </div>

                  <div className="mt-6">
                    <a
                      href={`/careers/apply/${job.id}`}
                      className="shimmer-effect inline-block rounded-md bg-primary px-6 py-2 text-white transition-colors hover:bg-primary/80"
                    >
                      {intl.formatMessage({
                        id: 'page.careers.openings.apply',
                      })}
                    </a>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="animate-fadeIn animation-delay-300 rounded-lg bg-muted py-12 text-center">
              <p className="mb-2 text-muted-foreground">
                {intl.formatMessage({ id: 'page.careers.openings.none' })}
              </p>
              <p className="text-muted-foreground">
                {intl.formatMessage({ id: 'page.careers.openings.contact' })}{' '}
                <a
                  href="mailto:<EMAIL>"
                  className="text-primary hover:underline"
                >
                  <EMAIL>
                </a>
              </p>
            </div>
          )}
        </section>
      </div>
    </div>
  );
}

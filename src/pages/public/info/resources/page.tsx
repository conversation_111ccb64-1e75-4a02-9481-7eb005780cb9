import { SITE_INFO, PAGE_METADATA } from '@/constants/site-config';
import { useState, useEffect, useRef } from 'react';
import { updatePageMetadata } from '@/utils';
import { useIntl } from 'react-intl';
import { resources, categories } from '@/data/public/resources';
import { getTranslation } from '@/utils/translation';
import '@/styles/animations.css'; // Import the centralized animations

/**
 * Resources Page Component
 *
 * Displays downloadable content and resources for users such as:
 * - Whitepapers
 * - Case studies
 * - E-books
 * - Templates
 * - Documentation
 */
export default function Page() {
  const intl = useIntl();
  const metadata = PAGE_METADATA.resources;
  const companyName = SITE_INFO.organization.name;
  const locale = intl.locale;

  // Helper function to get translated text
  const getText = (
    text: string | { en: string; [key: string]: string | undefined }
  ) => {
    return getTranslation(text, locale);
  };

  const displayName = SITE_INFO.organization.displayName || companyName;

  const [activeTab, setActiveTab] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');

  // State to track which sections are visible
  const [visibleSections, setVisibleSections] = useState({
    hero: false,
    featured: false,
    mainContent: false,
    customRequest: false,
  });

  // Refs for the sections
  const heroRef = useRef(null);
  const featuredRef = useRef(null);
  const mainContentRef = useRef(null);
  const customRequestRef = useRef(null);

  // Update document metadata
  useEffect(() => {
    const translatedTitle = intl.formatMessage({ id: 'page.resources.title' });
    const translatedDescription = intl.formatMessage({
      id: 'page.resources.subtitle',
    });

    updatePageMetadata({
      ...metadata,
      title: translatedTitle,
      description: translatedDescription,
    });
  }, [metadata, intl]);

  // Add intersection observer to check when sections are visible
  useEffect(() => {
    const observerOptions = {
      root: null,
      rootMargin: '0px',
      threshold: 0.1,
    };

    const observerCallback = (entries: IntersectionObserverEntry[]) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const sectionId = entry.target.getAttribute('data-section');
          if (sectionId) {
            setVisibleSections(prev => ({
              ...prev,
              [sectionId]: true,
            }));
          }
        }
      });
    };

    const observer = new IntersectionObserver(
      observerCallback,
      observerOptions
    );

    // Observe each section
    if (heroRef.current) observer.observe(heroRef.current);
    if (featuredRef.current) observer.observe(featuredRef.current);
    if (mainContentRef.current) observer.observe(mainContentRef.current);
    if (customRequestRef.current) observer.observe(customRequestRef.current);

    return () => {
      observer.disconnect();
    };
  }, []);

  // Filter resources by category and search query
  const filteredResources = resources.filter(resource => {
    const matchesCategory =
      activeTab === 'all' || resource.category === activeTab;
    const title = getText(resource.title);
    const description = getText(resource.description);
    const matchesSearch =
      searchQuery === '' ||
      title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      description.toLowerCase().includes(searchQuery.toLowerCase());

    return matchesCategory && matchesSearch;
  });

  // Get featured resources
  const featuredResources = resources.filter(resource => resource.featured);

  // Format date function
  const formatDate = (dateString: string): string => {
    const options: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    };
    return new Date(dateString).toLocaleDateString('en-US', options);
  };

  return (
    <div className="bg-background">
      {/* Hero section */}
      <div
        ref={heroRef}
        data-section="hero"
        className={`section-reveal border-b bg-background ${visibleSections.hero ? 'visible' : ''}`}
      >
        <div className="container mx-auto px-4 py-16">
          <div className="mx-auto max-w-4xl text-center">
            <h1 className="animate-fadeIn mb-4 text-4xl font-bold text-foreground">
              {intl.formatMessage({ id: 'page.resources.title' })}
            </h1>
            <p className="animate-fadeIn animation-delay-200 text-xl text-muted-foreground">
              {intl.formatMessage(
                { id: 'page.resources.subtitle' },
                { company: displayName }
              )}
            </p>
          </div>
        </div>
      </div>

      {/* Featured resources section */}
      {featuredResources.length > 0 && (
        <div
          ref={featuredRef}
          data-section="featured"
          className={`section-reveal bg-muted py-16 ${visibleSections.featured ? 'visible' : ''}`}
        >
          <div className="container mx-auto px-4">
            <div className="mx-auto max-w-6xl">
              <h2 className="animate-slideUp mb-10 text-center text-3xl font-bold">
                {intl.formatMessage({ id: 'page.resources.featured' })}
              </h2>
              <div className="grid gap-8 md:grid-cols-3">
                {featuredResources.map((resource, index) => (
                  <div
                    key={resource.id}
                    className="stagger-card flex flex-col overflow-hidden rounded-lg bg-card shadow-sm"
                    style={{ animationDelay: `${index * 150}ms` }}
                  >
                    <div className="relative h-48 bg-muted">
                      {/* In a real app, you would use an actual image here */}
                      <div className="absolute inset-0 flex items-center justify-center">
                        <p className="text-muted-foreground">
                          {intl.formatMessage({
                            id: 'page.resources.image.placeholder',
                          })}
                        </p>
                      </div>
                    </div>
                    <div className="flex-grow p-6">
                      <div className="mb-2">
                        <span className="rounded-full bg-primary/10 px-2 py-1 text-xs font-medium text-primary">
                          {intl.formatMessage({
                            id: `page.resources.category.${resource.category}`,
                          })}
                        </span>
                      </div>
                      <h3 className="mb-2 text-xl font-bold">
                        {getText(resource.title)}
                      </h3>
                      <p className="mb-4 text-muted-foreground">
                        {getText(resource.description)}
                      </p>
                    </div>
                    <div className="px-6 pb-6">
                      <a
                        href={resource.downloadUrl || resource.videoUrl || '#'}
                        className="shimmer-effect block w-full rounded-md bg-primary px-4 py-2 text-center font-medium text-white transition-colors hover:bg-primary/80"
                      >
                        {resource.category === 'webinars'
                          ? intl.formatMessage({
                              id: 'page.resources.watch.webinar',
                            })
                          : intl.formatMessage({
                              id: 'page.resources.download.now',
                            })}
                      </a>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Main content */}
      <div
        ref={mainContentRef}
        data-section="mainContent"
        className={`section-reveal container mx-auto px-4 py-16 ${visibleSections.mainContent ? 'visible' : ''}`}
      >
        <div className="mx-auto max-w-6xl">
          {/* Search and filter */}
          <div className="animate-fadeIn mb-12">
            <div className="mb-8 flex flex-col gap-6 md:flex-row md:items-center md:justify-between">
              <div className="relative md:w-96">
                <input
                  type="text"
                  placeholder={intl.formatMessage({
                    id: 'page.resources.search.placeholder',
                  })}
                  value={searchQuery}
                  onChange={e => setSearchQuery(e.target.value)}
                  className="w-full rounded-lg border border-input bg-background py-2 pl-10 pr-4 focus:border-transparent focus:ring-2 focus:ring-primary"
                />
                <div className="absolute left-3 top-2.5 text-muted-foreground">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                    />
                  </svg>
                </div>
              </div>

              <div className="flex flex-wrap gap-2">
                {categories.map(category => (
                  <button
                    key={category.id}
                    onClick={() => setActiveTab(category.id)}
                    className={`rounded-md px-4 py-2 ${
                      activeTab === category.id
                        ? 'shimmer-effect bg-primary text-white'
                        : 'bg-secondary text-secondary-foreground hover:bg-secondary/80'
                    }`}
                  >
                    {intl.formatMessage({
                      id: `page.resources.category.${category.id}`,
                    })}
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* Resources grid */}
          {filteredResources.length > 0 ? (
            <div className="animate-fadeIn animation-delay-200 grid gap-8 md:grid-cols-2 lg:grid-cols-3">
              {filteredResources.map((resource, index) => (
                <div
                  key={resource.id}
                  className="stagger-card overflow-hidden rounded-lg border border-border bg-card shadow-sm transition-shadow hover:shadow-md"
                  style={{ animationDelay: `${index * 100}ms` }}
                >
                  <div className="relative h-40 bg-muted">
                    {/* In a real app, you would use an actual image here */}
                    <div className="absolute inset-0 flex items-center justify-center">
                      <p className="text-muted-foreground">
                        {intl.formatMessage({
                          id: 'page.resources.image.placeholder',
                        })}
                      </p>
                    </div>
                  </div>
                  <div className="p-6">
                    <div className="mb-3 flex items-center justify-between">
                      <span className="rounded-full bg-secondary px-2 py-1 text-xs font-medium text-secondary-foreground">
                        {intl.formatMessage({
                          id: `page.resources.category.${resource.category}`,
                        })}
                      </span>
                      <span className="text-xs text-muted-foreground">
                        {formatDate(resource.date)}
                      </span>
                    </div>
                    <h3 className="mb-2 text-lg font-bold">
                      {getText(resource.title)}
                    </h3>
                    <p className="mb-4 line-clamp-3 text-sm text-muted-foreground">
                      {getText(resource.description)}
                    </p>
                    <a
                      href={resource.downloadUrl || resource.videoUrl || '#'}
                      className="shimmer-effect inline-block text-sm font-medium text-primary hover:text-primary/80"
                    >
                      {resource.category === 'webinars' ? (
                        <>
                          {intl.formatMessage({
                            id: 'page.resources.watch.webinar',
                          })}
                          {resource.duration && (
                            <span className="ml-2 text-xs text-muted-foreground">
                              ({resource.duration})
                            </span>
                          )}
                        </>
                      ) : (
                        intl.formatMessage({
                          id: 'page.resources.download.now',
                        })
                      )}
                    </a>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="animate-fadeIn animation-delay-200 rounded-lg bg-muted py-12 text-center">
              <p className="mb-2 text-muted-foreground">
                {intl.formatMessage({ id: 'page.resources.no.results' })}
              </p>
              <p className="text-muted-foreground">
                {intl.formatMessage({ id: 'page.resources.adjust.search' })}
                <button
                  onClick={() => {
                    setSearchQuery('');
                    setActiveTab('all');
                  }}
                  className="ml-1 text-primary hover:underline"
                >
                  {intl.formatMessage({ id: 'page.resources.view.all' })}
                </button>
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Request custom resource section */}
      <div
        ref={customRequestRef}
        data-section="customRequest"
        className={`section-reveal bg-muted py-16 ${visibleSections.customRequest ? 'visible' : ''}`}
      >
        <div className="container mx-auto px-4">
          <div className="animate-fadeIn mx-auto max-w-4xl rounded-xl border border-border bg-card p-8 shadow-sm">
            <div className="flex flex-col items-center gap-8 md:flex-row">
              <div className="md:w-2/3">
                <h2 className="animate-slideUp mb-4 text-2xl font-bold">
                  {intl.formatMessage({ id: 'page.resources.custom.title' })}
                </h2>
                <p className="animate-fadeIn animation-delay-200 mb-6 text-muted-foreground">
                  {intl.formatMessage(
                    { id: 'page.resources.custom.description' },
                    { company: displayName }
                  )}
                </p>
                <div className="animate-fadeIn animation-delay-300 flex flex-col gap-4 sm:flex-row">
                  <a
                    href="/contact"
                    className="shimmer-effect inline-flex items-center justify-center rounded-md bg-primary px-6 py-2 font-medium text-white transition-colors hover:bg-primary/80"
                  >
                    <svg
                      className="mr-2 h-5 w-5"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z"
                      />
                    </svg>
                    {intl.formatMessage({ id: 'page.resources.contact.us' })}
                  </a>
                  <a
                    href="/schedule-demo"
                    className="shimmer-effect inline-flex items-center justify-center rounded-md border border-primary bg-background px-6 py-2 font-medium text-primary transition-colors hover:bg-accent"
                  >
                    <svg
                      className="mr-2 h-5 w-5"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
                      />
                    </svg>
                    {intl.formatMessage({ id: 'page.resources.schedule.demo' })}
                  </a>
                </div>
              </div>
              <div className="animate-fadeIn animation-delay-400 md:w-1/3">
                <div className="mx-auto flex h-48 w-48 items-center justify-center rounded-full bg-primary">
                  <svg
                    className="h-24 w-24 text-white"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={1.5}
                      d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"
                    />
                  </svg>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

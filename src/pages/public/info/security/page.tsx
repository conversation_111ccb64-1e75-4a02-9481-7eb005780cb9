import { useState, useEffect, useRef, JSX } from 'react';
import { PAGE_METADATA } from '@/constants/site-config';

import { updatePageMetadata } from '@/utils';

import { useIntl } from 'react-intl';
import {
  Shield,
  Lock,
  Eye,
  Server,
  Key,
  FileCheck,
  Users,
  Globe,
  CheckCircle,
  Award,
  Zap,
  Database,
  Cloud,
  AlertTriangle,
  ChevronRight,
} from 'lucide-react';
import '@/styles/animations.css';

/**
 * Security Page Component
 *
 * Displays security practices, certifications, and compliance information.
 * Features include:
 * - Security measures overview
 * - Compliance certifications
 * - Data protection practices
 * - Security policies and procedures
 *
 * @returns {JSX.Element} The Security Page component
 */
export default function Page(): JSX.Element {
  const intl = useIntl();
  const metadata = PAGE_METADATA.security;

  // State to track which sections are visible
  const [visibleSections, setVisibleSections] = useState({
    hero: true,
    overview: true,
    measures: true,
    compliance: true,
    policies: true,
    contact: true,
  });

  // Refs for the sections
  const heroRef = useRef(null);
  const overviewRef = useRef(null);
  const measuresRef = useRef(null);
  const complianceRef = useRef(null);
  const policiesRef = useRef(null);
  const contactRef = useRef(null);

  // Update document metadata
  useEffect(() => {
    const translatedMetadata = {
      ...metadata,
      title: intl.formatMessage({
        id: 'security.title',
        defaultMessage: 'Security & Compliance',
      }),
      description: intl.formatMessage({
        id: 'security.subtitle',
        defaultMessage:
          'Learn about our comprehensive security measures, compliance certifications, and data protection practices',
      }),
    };
    updatePageMetadata(translatedMetadata);
  }, [metadata, intl]);

  // Add intersection observer to check when sections are visible
  useEffect(() => {
    const observerOptions = {
      root: null,
      rootMargin: '0px',
      threshold: 0.1,
    };

    const observerCallback = (entries: IntersectionObserverEntry[]) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const sectionId = entry.target.getAttribute('data-section');
          if (sectionId) {
            setVisibleSections(prev => ({
              ...prev,
              [sectionId]: true,
            }));
          }
        }
      });
    };

    const observer = new IntersectionObserver(
      observerCallback,
      observerOptions
    );

    // Observe each section
    if (heroRef.current) observer.observe(heroRef.current);
    if (overviewRef.current) observer.observe(overviewRef.current);
    if (measuresRef.current) observer.observe(measuresRef.current);
    if (complianceRef.current) observer.observe(complianceRef.current);
    if (policiesRef.current) observer.observe(policiesRef.current);
    if (contactRef.current) observer.observe(contactRef.current);

    return () => {
      observer.disconnect();
    };
  }, []);

  const securityMeasures = [
    {
      icon: <Lock className="h-8 w-8" />,
      title: intl.formatMessage({
        id: 'security.encryption.title',
        defaultMessage: 'End-to-End Encryption',
      }),
      description: intl.formatMessage({
        id: 'security.encryption.description',
        defaultMessage:
          'All data is encrypted in transit and at rest using industry-standard AES-256 encryption.',
      }),
      features: [
        intl.formatMessage({
          id: 'security.encryption.feature1',
          defaultMessage: 'TLS 1.3 for data in transit',
        }),
        intl.formatMessage({
          id: 'security.encryption.feature2',
          defaultMessage: 'AES-256 for data at rest',
        }),
        intl.formatMessage({
          id: 'security.encryption.feature3',
          defaultMessage: 'Key rotation every 90 days',
        }),
      ],
    },
    {
      icon: <Eye className="h-8 w-8" />,
      title: intl.formatMessage({
        id: 'security.monitoring.title',
        defaultMessage: '24/7 Security Monitoring',
      }),
      description: intl.formatMessage({
        id: 'security.monitoring.description',
        defaultMessage:
          'Continuous monitoring and threat detection to identify and respond to security incidents.',
      }),
      features: [
        intl.formatMessage({
          id: 'security.monitoring.feature1',
          defaultMessage: 'Real-time threat detection',
        }),
        intl.formatMessage({
          id: 'security.monitoring.feature2',
          defaultMessage: 'Automated incident response',
        }),
        intl.formatMessage({
          id: 'security.monitoring.feature3',
          defaultMessage: 'Security operations center (SOC)',
        }),
      ],
    },
    {
      icon: <Server className="h-8 w-8" />,
      title: intl.formatMessage({
        id: 'security.infrastructure.title',
        defaultMessage: 'Secure Infrastructure',
      }),
      description: intl.formatMessage({
        id: 'security.infrastructure.description',
        defaultMessage:
          'Enterprise-grade infrastructure with multiple layers of security controls and redundancy.',
      }),
      features: [
        intl.formatMessage({
          id: 'security.infrastructure.feature1',
          defaultMessage: 'Multi-region data centers',
        }),
        intl.formatMessage({
          id: 'security.infrastructure.feature2',
          defaultMessage: 'Network segmentation',
        }),
        intl.formatMessage({
          id: 'security.infrastructure.feature3',
          defaultMessage: 'DDoS protection',
        }),
      ],
    },
    {
      icon: <Key className="h-8 w-8" />,
      title: intl.formatMessage({
        id: 'security.access.title',
        defaultMessage: 'Access Control',
      }),
      description: intl.formatMessage({
        id: 'security.access.description',
        defaultMessage:
          'Strict access controls and multi-factor authentication to protect sensitive data and systems.',
      }),
      features: [
        intl.formatMessage({
          id: 'security.access.feature1',
          defaultMessage: 'Multi-factor authentication (MFA)',
        }),
        intl.formatMessage({
          id: 'security.access.feature2',
          defaultMessage: 'Role-based access control (RBAC)',
        }),
        intl.formatMessage({
          id: 'security.access.feature3',
          defaultMessage: 'Single sign-on (SSO) integration',
        }),
      ],
    },
    {
      icon: <Database className="h-8 w-8" />,
      title: intl.formatMessage({
        id: 'security.backup.title',
        defaultMessage: 'Data Backup & Recovery',
      }),
      description: intl.formatMessage({
        id: 'security.backup.description',
        defaultMessage:
          'Automated backups and disaster recovery procedures to ensure business continuity.',
      }),
      features: [
        intl.formatMessage({
          id: 'security.backup.feature1',
          defaultMessage: 'Daily automated backups',
        }),
        intl.formatMessage({
          id: 'security.backup.feature2',
          defaultMessage: 'Point-in-time recovery',
        }),
        intl.formatMessage({
          id: 'security.backup.feature3',
          defaultMessage: '99.9% uptime guarantee',
        }),
      ],
    },
    {
      icon: <Users className="h-8 w-8" />,
      title: intl.formatMessage({
        id: 'security.training.title',
        defaultMessage: 'Security Training',
      }),
      description: intl.formatMessage({
        id: 'security.training.description',
        defaultMessage:
          'Regular security training and awareness programs for all team members.',
      }),
      features: [
        intl.formatMessage({
          id: 'security.training.feature1',
          defaultMessage: 'Monthly security training',
        }),
        intl.formatMessage({
          id: 'security.training.feature2',
          defaultMessage: 'Phishing simulation tests',
        }),
        intl.formatMessage({
          id: 'security.training.feature3',
          defaultMessage: 'Security incident drills',
        }),
      ],
    },
  ];

  const certifications = [
    {
      name: intl.formatMessage({
        id: 'security.cert.soc2.name',
        defaultMessage: 'SOC 2 Type II',
      }),
      description: intl.formatMessage({
        id: 'security.cert.soc2.description',
        defaultMessage:
          'Audited controls for security, availability, and confidentiality',
      }),
      icon: <Award className="h-12 w-12 text-blue-600" />,
      status: intl.formatMessage({
        id: 'security.cert.status.certified',
        defaultMessage: 'Certified',
      }),
      validUntil: '2024-12-31',
    },
    {
      name: intl.formatMessage({
        id: 'security.cert.iso27001.name',
        defaultMessage: 'ISO 27001',
      }),
      description: intl.formatMessage({
        id: 'security.cert.iso27001.description',
        defaultMessage:
          'International standard for information security management',
      }),
      icon: <Shield className="h-12 w-12 text-green-600" />,
      status: intl.formatMessage({
        id: 'security.cert.status.certified',
        defaultMessage: 'Certified',
      }),
      validUntil: '2025-06-30',
    },
    {
      name: intl.formatMessage({
        id: 'security.cert.gdpr.name',
        defaultMessage: 'GDPR Compliant',
      }),
      description: intl.formatMessage({
        id: 'security.cert.gdpr.description',
        defaultMessage:
          'Full compliance with European data protection regulations',
      }),
      icon: <Globe className="h-12 w-12 text-purple-600" />,
      status: intl.formatMessage({
        id: 'security.cert.status.compliant',
        defaultMessage: 'Compliant',
      }),
      validUntil: intl.formatMessage({
        id: 'security.cert.ongoing',
        defaultMessage: 'Ongoing',
      }),
    },
    {
      name: intl.formatMessage({
        id: 'security.cert.hipaa.name',
        defaultMessage: 'HIPAA Compliant',
      }),
      description: intl.formatMessage({
        id: 'security.cert.hipaa.description',
        defaultMessage: 'Healthcare data protection and privacy compliance',
      }),
      icon: <FileCheck className="h-12 w-12 text-red-600" />,
      status: intl.formatMessage({
        id: 'security.cert.status.compliant',
        defaultMessage: 'Compliant',
      }),
      validUntil: intl.formatMessage({
        id: 'security.cert.ongoing',
        defaultMessage: 'Ongoing',
      }),
    },
  ];

  const securityPolicies = [
    {
      title: intl.formatMessage({
        id: 'security.policy.privacy.title',
        defaultMessage: 'Privacy Policy',
      }),
      description: intl.formatMessage({
        id: 'security.policy.privacy.description',
        defaultMessage:
          'How we collect, use, and protect your personal information',
      }),
      link: '/privacy',
      updated: '2024-01-15',
    },
    {
      title: intl.formatMessage({
        id: 'security.policy.terms.title',
        defaultMessage: 'Terms of Service',
      }),
      description: intl.formatMessage({
        id: 'security.policy.terms.description',
        defaultMessage: 'Terms and conditions for using our services',
      }),
      link: '/terms',
      updated: '2024-01-10',
    },
    {
      title: intl.formatMessage({
        id: 'security.policy.data.title',
        defaultMessage: 'Data Processing Agreement',
      }),
      description: intl.formatMessage({
        id: 'security.policy.data.description',
        defaultMessage:
          'How we process and handle your data in compliance with regulations',
      }),
      link: '/dpa',
      updated: '2024-01-20',
    },
    {
      title: intl.formatMessage({
        id: 'security.policy.incident.title',
        defaultMessage: 'Incident Response Plan',
      }),
      description: intl.formatMessage({
        id: 'security.policy.incident.description',
        defaultMessage:
          'Our procedures for handling security incidents and breaches',
      }),
      link: '/incident-response',
      updated: '2024-01-05',
    },
  ];

  return (
    <div className="min-h-screen bg-background">
      {/* Hero Section */}
      <section
        ref={heroRef}
        data-section="hero"
        className={`section-reveal bg-gradient-to-r from-primary to-primary/80 text-white ${visibleSections.hero ? 'visible' : ''}`}
      >
        <div className="container mx-auto px-4 py-16">
          <div className="mx-auto max-w-4xl text-center">
            <div className="animate-fadeIn mb-6">
              <Shield className="mx-auto mb-4 h-20 w-20 text-white" />
            </div>
            <h1 className="animate-fadeIn animation-delay-200 mb-6 text-4xl font-bold">
              {intl.formatMessage({
                id: 'security.title',
                defaultMessage: 'Security & Compliance',
              })}
            </h1>
            <p className="animate-fadeIn animation-delay-300 mb-8 text-xl opacity-90">
              {intl.formatMessage({
                id: 'security.subtitle',
                defaultMessage:
                  'Your data security is our top priority. Learn about our comprehensive security measures and compliance certifications.',
              })}
            </p>

            <div className="animate-fadeIn animation-delay-400 flex flex-wrap justify-center gap-4">
              <div className="rounded-lg bg-white/10 px-6 py-3">
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-5 w-5 text-green-400" />
                  <span className="font-medium text-white">
                    {intl.formatMessage({
                      id: 'security.certification.soc2',
                      defaultMessage: 'SOC 2 Certified',
                    })}
                  </span>
                </div>
              </div>
              <div className="rounded-lg bg-white/10 px-6 py-3">
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-5 w-5 text-green-400" />
                  <span className="font-medium text-white">
                    {intl.formatMessage({
                      id: 'security.certification.iso27001',
                      defaultMessage: 'ISO 27001 Certified',
                    })}
                  </span>
                </div>
              </div>
              <div className="rounded-lg bg-white/10 px-6 py-3">
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-5 w-5 text-green-400" />
                  <span className="font-medium text-white">
                    {intl.formatMessage({
                      id: 'security.certification.gdpr',
                      defaultMessage: 'GDPR Compliant',
                    })}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Security Overview */}
      <section
        ref={overviewRef}
        data-section="overview"
        className={`section-reveal bg-background py-16 ${visibleSections.overview ? 'visible' : ''}`}
      >
        <div className="container mx-auto px-4">
          <div className="animate-slideUp mx-auto mb-12 max-w-4xl text-center">
            <h2 className="mb-6 text-3xl font-bold text-foreground">
              {intl.formatMessage({
                id: 'security.overview.title',
                defaultMessage: 'Enterprise-Grade Security',
              })}
            </h2>
            <p className="text-xl text-muted-foreground">
              {intl.formatMessage({
                id: 'security.overview.description',
                defaultMessage:
                  'We implement multiple layers of security controls to protect your data and ensure the highest levels of availability and performance.',
              })}
            </p>
          </div>

          <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-4">
            <div
              className="stagger-card animate-fadeIn text-center"
              style={{ animationDelay: '0ms' }}
            >
              <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-primary/10">
                <Zap className="h-8 w-8 text-primary" />
              </div>
              <h3 className="mb-2 text-2xl font-bold text-foreground">99.9%</h3>
              <p className="text-muted-foreground">
                {intl.formatMessage({
                  id: 'security.stat.uptime',
                  defaultMessage: 'Uptime Guarantee',
                })}
              </p>
            </div>

            <div
              className="stagger-card animate-fadeIn text-center"
              style={{ animationDelay: '150ms' }}
            >
              <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-green-500/10">
                <Shield className="h-8 w-8 text-green-600" />
              </div>
              <h3 className="mb-2 text-2xl font-bold text-foreground">24/7</h3>
              <p className="text-muted-foreground">
                {intl.formatMessage({
                  id: 'security.stat.monitoring',
                  defaultMessage: 'Security Monitoring',
                })}
              </p>
            </div>

            <div
              className="stagger-card animate-fadeIn text-center"
              style={{ animationDelay: '300ms' }}
            >
              <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-purple-500/10">
                <Lock className="h-8 w-8 text-purple-600" />
              </div>
              <h3 className="mb-2 text-2xl font-bold text-foreground">
                256-bit
              </h3>
              <p className="text-muted-foreground">
                {intl.formatMessage({
                  id: 'security.stat.encryption',
                  defaultMessage: 'AES Encryption',
                })}
              </p>
            </div>

            <div
              className="stagger-card animate-fadeIn text-center"
              style={{ animationDelay: '450ms' }}
            >
              <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-red-500/10">
                <Cloud className="h-8 w-8 text-red-600" />
              </div>
              <h3 className="mb-2 text-2xl font-bold text-foreground">
                {intl.formatMessage({
                  id: 'security.stat.multiregion',
                  defaultMessage: 'Multi-Region',
                })}
              </h3>
              <p className="text-muted-foreground">
                {intl.formatMessage({
                  id: 'security.stat.backup',
                  defaultMessage: 'Data Backup',
                })}
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Security Measures */}
      <section
        ref={measuresRef}
        data-section="measures"
        className={`section-reveal bg-muted py-16 ${visibleSections.measures ? 'visible' : ''}`}
      >
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-6xl">
            <div className="animate-slideUp mb-12 text-center">
              <h2 className="mb-6 text-3xl font-bold text-foreground">
                {intl.formatMessage({
                  id: 'security.measures.title',
                  defaultMessage: 'Security Measures',
                })}
              </h2>
              <p className="text-xl text-muted-foreground">
                {intl.formatMessage({
                  id: 'security.measures.description',
                  defaultMessage:
                    'Comprehensive security controls designed to protect your data at every level.',
                })}
              </p>
            </div>

            <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
              {securityMeasures.map((measure, index) => (
                <div
                  key={index}
                  className="stagger-card animate-fadeIn rounded-xl bg-card p-6 shadow-sm transition-all duration-300 hover:-translate-y-1 hover:shadow-md"
                  style={{ animationDelay: `${index * 150}ms` }}
                >
                  <div className="mb-4 text-primary">{measure.icon}</div>

                  <h3 className="mb-3 text-xl font-bold text-foreground">
                    {measure.title}
                  </h3>

                  <p className="mb-4 text-muted-foreground">
                    {measure.description}
                  </p>

                  <ul className="space-y-2">
                    {measure.features.map((feature, featureIndex) => (
                      <li
                        key={featureIndex}
                        className="flex items-center gap-2 text-sm text-muted-foreground"
                      >
                        <CheckCircle className="h-4 w-4 flex-shrink-0 text-green-600" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Compliance Certifications */}
      <section
        ref={complianceRef}
        data-section="compliance"
        className={`section-reveal bg-muted py-16 ${visibleSections.compliance ? 'visible' : ''}`}
      >
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-6xl">
            <div className="animate-slideUp mb-12 text-center">
              <h2 className="mb-6 text-3xl font-bold text-foreground">
                {intl.formatMessage({
                  id: 'security.compliance.title',
                  defaultMessage: 'Compliance Certifications',
                })}
              </h2>
              <p className="text-xl text-muted-foreground">
                {intl.formatMessage({
                  id: 'security.compliance.description',
                  defaultMessage:
                    'We maintain the highest standards of compliance with industry regulations and frameworks.',
                })}
              </p>
            </div>

            <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-4">
              {certifications.map((cert, index) => (
                <div
                  key={index}
                  className={`animate-fadeIn rounded-xl bg-card p-6 text-center shadow-sm transition-all duration-300 hover:-translate-y-1 hover:shadow-md`}
                  style={{ animationDelay: `${index * 150}ms` }}
                >
                  <div className="mb-4">{cert.icon}</div>

                  <h3 className="mb-2 text-xl font-bold text-foreground">
                    {cert.name}
                  </h3>

                  <p className="mb-4 text-sm text-muted-foreground">
                    {cert.description}
                  </p>

                  <div className="border-t border-border pt-4">
                    <div className="mb-2 flex items-center justify-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                      <span className="text-sm font-medium text-green-600">
                        {cert.status}
                      </span>
                    </div>
                    <p className="text-xs text-muted-foreground">
                      {intl.formatMessage({
                        id: 'security.cert.valid.until',
                        defaultMessage: 'Valid until',
                      })}
                      : {cert.validUntil}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Security Policies */}
      <section
        ref={policiesRef}
        data-section="policies"
        className={`section-reveal bg-background py-16 ${visibleSections.policies ? 'visible' : ''}`}
      >
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl">
            <div className="animate-slideUp mb-12 text-center">
              <h2 className="mb-6 text-3xl font-bold text-foreground">
                {intl.formatMessage({
                  id: 'security.policies.title',
                  defaultMessage: 'Security Policies & Documentation',
                })}
              </h2>
              <p className="text-xl text-muted-foreground">
                {intl.formatMessage({
                  id: 'security.policies.description',
                  defaultMessage:
                    'Transparent policies and procedures that govern how we handle your data and maintain security.',
                })}
              </p>
            </div>

            <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
              {securityPolicies.map((policy, index) => (
                <div
                  key={index}
                  className="stagger-card animate-fadeIn rounded-xl border border-border bg-card p-6 transition-all duration-300 hover:shadow-lg"
                  style={{ animationDelay: `${index * 100}ms` }}
                >
                  <div className="mb-4 flex items-start justify-between">
                    <div className="flex-1">
                      <h3 className="mb-2 text-lg font-bold text-foreground">
                        {policy.title}
                      </h3>
                      <p className="mb-3 text-sm text-muted-foreground">
                        {policy.description}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        {intl.formatMessage({
                          id: 'security.policy.updated',
                          defaultMessage: 'Last updated',
                        })}
                        : {policy.updated}
                      </p>
                    </div>
                    <FileCheck className="ml-4 h-6 w-6 flex-shrink-0 text-primary" />
                  </div>

                  <a
                    href={policy.link}
                    className="shimmer-effect inline-flex items-center gap-2 text-sm font-medium text-primary transition-colors hover:text-primary/80"
                  >
                    {intl.formatMessage({
                      id: 'security.policy.read',
                      defaultMessage: 'Read Policy',
                    })}
                    <ChevronRight className="h-4 w-4" />
                  </a>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Security Contact */}
      <section
        ref={contactRef}
        data-section="contact"
        className={`section-reveal bg-primary py-16 text-white ${visibleSections.contact ? 'visible' : ''}`}
      >
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl text-center">
            <div className="mb-6">
              <AlertTriangle className="mx-auto mb-4 h-16 w-16 text-red-400" />
            </div>

            <h2 className="mb-6 text-3xl font-bold md:text-4xl">
              {intl.formatMessage({
                id: 'security.contact.title',
                defaultMessage: 'Report Security Issues',
              })}
            </h2>

            <p className="mb-8 text-xl text-gray-300">
              {intl.formatMessage({
                id: 'security.contact.description',
                defaultMessage:
                  'If you discover a security vulnerability, please report it to us immediately through our responsible disclosure program.',
              })}
            </p>

            <div className="mb-8 grid grid-cols-1 gap-8 md:grid-cols-2">
              <div className="rounded-lg bg-white/10 p-6">
                <h3 className="mb-3 text-lg font-bold text-white">
                  {intl.formatMessage({
                    id: 'security.contact.email.title',
                    defaultMessage: 'Security Email',
                  })}
                </h3>
                <p className="mb-4 text-white/80">
                  {intl.formatMessage({
                    id: 'security.contact.email.description',
                    defaultMessage:
                      'For security-related inquiries and vulnerability reports',
                  })}
                </p>
                <a
                  href="mailto:<EMAIL>"
                  className="shimmer-effect inline-flex items-center gap-2 font-medium text-white transition-colors hover:text-white/80"
                >
                  <EMAIL>
                </a>
              </div>

              <div className="rounded-lg bg-white/10 p-6">
                <h3 className="mb-3 text-lg font-bold text-white">
                  {intl.formatMessage({
                    id: 'security.contact.response.title',
                    defaultMessage: 'Response Time',
                  })}
                </h3>
                <p className="mb-4 text-white/80">
                  {intl.formatMessage({
                    id: 'security.contact.response.description',
                    defaultMessage:
                      'We acknowledge all security reports within 24 hours and provide regular updates',
                  })}
                </p>
                <div className="font-medium text-white">&lt; 24 hours</div>
              </div>
            </div>

            <div className="rounded-lg border border-yellow-600/30 bg-yellow-900/20 p-6">
              <div className="flex items-start gap-3">
                <AlertTriangle className="mt-0.5 h-6 w-6 flex-shrink-0 text-yellow-400" />
                <div className="text-left">
                  <h4 className="mb-2 font-bold text-yellow-400">
                    {intl.formatMessage({
                      id: 'security.contact.responsible.title',
                      defaultMessage: 'Responsible Disclosure',
                    })}
                  </h4>
                  <p className="text-sm text-yellow-100">
                    {intl.formatMessage({
                      id: 'security.contact.responsible.description',
                      defaultMessage:
                        'Please do not publicly disclose security vulnerabilities until we have had a chance to address them. We are committed to working with security researchers to resolve issues quickly and responsibly.',
                    })}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}

import { PAGE_METADATA } from '@/constants/site-config';
import { useState, useEffect, useRef } from 'react';
import { useParams, Navigate } from 'react-router-dom';
import { updatePageMetadata } from '@/utils';
import { useIntl } from 'react-intl';
import {
  getBlogPostBySlug,
  getRelatedPosts,
  getTranslatedText,
  getTranslatedTableOfContents,
  headingIdMap,
} from '@/data/public/blog';
import MarkdownRender from '@/components/common/markdown-renderer';
import '@/styles/animations.css';

/**
 * Blog Detail Page Component
 *
 * Displays individual blog post with full content, table of contents,
 * social sharing, and related posts.
 */
export default function Page() {
  const { slug } = useParams<{ slug: string }>();
  const intl = useIntl();
  const locale = intl.locale;

  // Get blog post by slug
  const blogPost = slug ? getBlogPostBySlug(slug) : undefined;
  const relatedPosts = blogPost ? getRelatedPosts(blogPost.id) : [];
  const tableOfContents = blogPost
    ? getTranslatedTableOfContents(blogPost.tableOfContents, locale)
    : [];

  // State to track which sections are visible
  const [visibleSections, setVisibleSections] = useState({
    header: false,
    content: false,
    related: false,
    social: false,
  });

  // Refs for the sections
  const headerRef = useRef(null);
  const contentRef = useRef(null);
  const relatedRef = useRef(null);
  const socialRef = useRef(null);

  // Update document metadata
  useEffect(() => {
    if (blogPost) {
      const dynamicMetadata = {
        ...PAGE_METADATA.blogDetail,
        title: getTranslatedText(blogPost.title, locale),
        description: getTranslatedText(blogPost.excerpt, locale),
      };
      updatePageMetadata(dynamicMetadata);
    }
  }, [blogPost, locale]);

  // Add intersection observer to check when sections are visible
  useEffect(() => {
    const observerOptions = {
      root: null,
      rootMargin: '0px',
      threshold: 0.1,
    };

    const observerCallback = (entries: IntersectionObserverEntry[]) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const sectionId = entry.target.getAttribute('data-section');
          if (sectionId) {
            setVisibleSections(prev => ({
              ...prev,
              [sectionId]: true,
            }));
          }
        }
      });
    };

    const observer = new IntersectionObserver(
      observerCallback,
      observerOptions
    );

    // Observe each section
    if (headerRef.current) observer.observe(headerRef.current);
    if (contentRef.current) observer.observe(contentRef.current);
    if (relatedRef.current) observer.observe(relatedRef.current);
    if (socialRef.current) observer.observe(socialRef.current);

    return () => {
      observer.disconnect();
    };
  }, []);

  // Format date function
  const formatDate = (dateString: string) => {
    const options: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    };
    return new Date(dateString).toLocaleDateString(locale, options);
  };

  // If blog post not found, redirect to 404
  if (!blogPost) {
    return <Navigate to="/404" replace />;
  }

  return (
    <div className="bg-background">
      {/* Header section */}
      <div
        ref={headerRef}
        data-section="header"
        className={`section-reveal bg-gradient-to-r from-primary to-primary/80 text-white ${visibleSections.header ? 'visible' : ''}`}
      >
        <div className="container mx-auto px-4 py-12 md:py-16">
          <div className="mx-auto max-w-4xl">
            <div className="animate-fadeIn mb-4">
              <span className="rounded-full bg-white/20 px-3 py-1 text-sm font-medium">
                {blogPost.category}
              </span>
            </div>
            <h1 className="animate-slideUp mb-6 text-2xl font-bold leading-tight md:text-3xl lg:text-4xl">
              {getTranslatedText(blogPost.title, locale)}
            </h1>
            <div className="animate-fadeIn animation-delay-200 flex flex-col gap-4 text-white/90 sm:flex-row sm:flex-wrap sm:items-center sm:gap-6">
              <div className="flex items-center">
                <div className="mr-3 flex h-10 w-10 items-center justify-center overflow-hidden rounded-full bg-white/20 md:h-12 md:w-12">
                  <span className="text-sm font-medium">
                    {blogPost.author.name.charAt(0)}
                  </span>
                </div>
                <div>
                  <p className="font-medium">{blogPost.author.name}</p>
                  <p className="text-sm text-white/80">
                    {blogPost.author.title}
                  </p>
                </div>
              </div>
              <div className="text-sm">
                <p>{formatDate(blogPost.publishDate)}</p>
                <p>{getTranslatedText(blogPost.readTime, locale)}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main content */}
      <div className="container mx-auto px-4 py-8 md:py-12">
        <div className="mx-auto max-w-6xl">
          <div className="grid gap-8 lg:grid-cols-4">
            {/* Table of Contents - Hidden on mobile, shown on desktop */}
            {tableOfContents && tableOfContents.length > 0 && (
              <div className="hidden lg:col-span-1 lg:block">
                <div className="animate-fadeIn sticky top-8 rounded-lg border border-border bg-card p-4 lg:p-6">
                  <h3 className="mb-4 text-sm font-semibold text-card-foreground lg:text-base">
                    {intl.formatMessage({
                      id: 'public.blog.detail.toc',
                      defaultMessage: 'Table of Contents',
                    })}
                  </h3>
                  <nav className="space-y-1 lg:space-y-2">
                    {tableOfContents.map((item, index) => (
                      <a
                        key={item.id}
                        href={`#${item.id}`}
                        className={`stagger-card block text-xs transition-colors hover:text-primary lg:text-sm ${
                          item.level === 2
                            ? 'font-medium'
                            : 'ml-3 text-muted-foreground lg:ml-4'
                        }`}
                        style={{ animationDelay: `${index * 50}ms` }}
                        onClick={e => {
                          e.preventDefault();
                          const element = document.getElementById(item.id);
                          if (element) {
                            element.scrollIntoView({
                              behavior: 'smooth',
                              block: 'start',
                            });
                          }
                        }}
                      >
                        {item.title}
                      </a>
                    ))}
                  </nav>
                </div>
              </div>
            )}

            {/* Article content */}
            <div
              ref={contentRef}
              data-section="content"
              className={`section-reveal ${tableOfContents.length > 0 ? 'lg:col-span-3' : 'lg:col-span-4'} ${visibleSections.content ? 'visible' : ''}`}
            >
              {/* Mobile Table of Contents */}
              {tableOfContents && tableOfContents.length > 0 && (
                <div className="mb-8 lg:hidden">
                  <details className="animate-fadeIn rounded-lg border border-border bg-card">
                    <summary className="cursor-pointer p-4 font-semibold text-card-foreground hover:bg-accent">
                      {intl.formatMessage({
                        id: 'public.blog.detail.toc',
                        defaultMessage: 'Table of Contents',
                      })}
                    </summary>
                    <nav className="space-y-2 p-4 pt-0">
                      {tableOfContents.map((item, index) => (
                        <a
                          key={item.id}
                          href={`#${item.id}`}
                          className={`stagger-card block text-sm transition-colors hover:text-primary ${
                            item.level === 2
                              ? 'font-medium'
                              : 'ml-4 text-muted-foreground'
                          }`}
                          style={{ animationDelay: `${index * 50}ms` }}
                          onClick={e => {
                            e.preventDefault();
                            const element = document.getElementById(item.id);
                            if (element) {
                              element.scrollIntoView({
                                behavior: 'smooth',
                                block: 'start',
                              });
                            }
                          }}
                        >
                          {item.title}
                        </a>
                      ))}
                    </nav>
                  </details>
                </div>
              )}

              {/* Article content using enhanced generic markdown renderer */}
              <div className="animate-fadeIn">
                <MarkdownRender
                  content={getTranslatedText(blogPost.content, locale)}
                  headingIdMap={headingIdMap}
                  className="prose-base md:prose-lg"
                />
              </div>

              {/* Tags */}
              {blogPost.tags && blogPost.tags.length > 0 && (
                <div className="animate-fadeIn animation-delay-300 mt-8 border-t border-border pt-8">
                  <h4 className="mb-4 font-semibold">
                    {intl.formatMessage({
                      id: 'public.blog.detail.tags',
                      defaultMessage: 'Tags',
                    })}
                  </h4>
                  <div className="flex flex-wrap gap-2">
                    {blogPost.tags.map((tag, index) => (
                      <span
                        key={tag}
                        className="stagger-card rounded-full bg-secondary px-3 py-1 text-sm text-secondary-foreground"
                        style={{ animationDelay: `${index * 100}ms` }}
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Social sharing */}
          <div
            ref={socialRef}
            data-section="social"
            className={`section-reveal mt-12 border-t border-border pt-8 ${visibleSections.social ? 'visible' : ''}`}
          >
            <div className="animate-fadeIn">
              <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                <div className="flex-1">
                  <h4 className="mb-3 font-semibold">
                    {intl.formatMessage({
                      id: 'public.blog.detail.share',
                      defaultMessage: 'Share this article',
                    })}
                  </h4>
                  <div className="flex flex-wrap gap-2 sm:gap-4">
                    <button className="shimmer-effect flex items-center gap-2 rounded-md bg-blue-600 px-3 py-2 text-sm text-white hover:bg-blue-700 sm:px-4">
                      <svg
                        className="h-4 w-4"
                        fill="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z" />
                      </svg>
                      <span className="hidden sm:inline">Twitter</span>
                    </button>
                    <button className="shimmer-effect flex items-center gap-2 rounded-md bg-blue-800 px-3 py-2 text-sm text-white hover:bg-blue-900 sm:px-4">
                      <svg
                        className="h-4 w-4"
                        fill="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z" />
                      </svg>
                      <span className="hidden sm:inline">LinkedIn</span>
                    </button>
                    <button className="shimmer-effect flex items-center gap-2 rounded-md bg-blue-500 px-3 py-2 text-sm text-white hover:bg-blue-600 sm:px-4">
                      <svg
                        className="h-4 w-4"
                        fill="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z" />
                      </svg>
                      <span className="hidden sm:inline">Facebook</span>
                    </button>
                  </div>
                </div>
                {blogPost.socialShares && (
                  <div className="text-sm text-muted-foreground sm:text-right">
                    <p>
                      {intl.formatMessage({
                        id: 'public.blog.detail.shares',
                        defaultMessage: 'Total shares:',
                      })}{' '}
                      {Object.values(blogPost.socialShares).reduce(
                        (a, b) => a + b,
                        0
                      )}
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Related posts */}
          {relatedPosts.length > 0 && (
            <div
              ref={relatedRef}
              data-section="related"
              className={`section-reveal mt-12 border-t border-border pt-8 ${visibleSections.related ? 'visible' : ''}`}
            >
              <h3 className="animate-slideUp mb-6 text-xl font-bold md:text-2xl">
                {intl.formatMessage({
                  id: 'public.blog.detail.related',
                  defaultMessage: 'Related Articles',
                })}
              </h3>
              <div className="grid gap-4 sm:grid-cols-2 sm:gap-6 lg:grid-cols-3">
                {relatedPosts.map((post, index) => (
                  <a
                    key={post.id}
                    href={`/blog/${post.title.en
                      .toLowerCase()
                      .replace(/[^a-z0-9]+/g, '-')
                      .replace(/(^-|-$)/g, '')}`}
                    className="stagger-card group overflow-hidden rounded-lg border border-border bg-card shadow-sm transition-shadow hover:shadow-md"
                    style={{ animationDelay: `${index * 150}ms` }}
                  >
                    <div className="relative h-32 w-full bg-muted sm:h-48">
                      <div className="absolute inset-0 flex items-center justify-center">
                        <span className="text-sm text-muted-foreground">
                          {intl.formatMessage({
                            id: 'blog.article.image',
                            defaultMessage: 'Article Image',
                          })}
                        </span>
                      </div>
                    </div>
                    <div className="p-3 sm:p-4">
                      <h4 className="mb-2 line-clamp-2 text-sm font-semibold text-card-foreground group-hover:text-primary sm:text-base">
                        {getTranslatedText(post.title, locale)}
                      </h4>
                      <p className="line-clamp-2 text-xs text-muted-foreground sm:text-sm">
                        {getTranslatedText(post.excerpt, locale)}
                      </p>
                      <div className="mt-2 flex items-center text-xs text-muted-foreground sm:mt-3">
                        <span>{formatDate(post.publishDate)}</span>
                        <span className="mx-2">•</span>
                        <span>{getTranslatedText(post.readTime, locale)}</span>
                      </div>
                    </div>
                  </a>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

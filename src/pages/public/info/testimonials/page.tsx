import { useState, useEffect, useRef } from 'react';
import { useIntl } from 'react-intl';
import { Star, Quote } from 'lucide-react';
import { PAGE_METADATA } from '@/constants/site-config';
import { updatePageMetadata } from '@/utils';
import { Button } from '@/components/ui/shadcn/button';
import { Card, CardContent } from '@/components/ui/shadcn/card';
import { Badge } from '@/components/ui/shadcn/badge';
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from '@/components/ui/shadcn/avatar';
import {
  testimonials,
  testimonialCategories,
  getFeaturedTestimonials,
  getTestimonialsByCategory,
} from '@/data/public/testimonials';
import { Testimonial } from '@/types/public/testimonials';
import '@/styles/animations.css'; // Import the centralized animations

export default function Page() {
  const intl = useIntl();
  const metadata = PAGE_METADATA.testimonials;
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [filteredTestimonials, setFilteredTestimonials] =
    useState<Testimonial[]>(testimonials);

  // State to track which sections are visible
  const [visibleSections, setVisibleSections] = useState({
    hero: false,
    featured: false,
    testimonials: false,
  });

  // Refs for the sections
  const heroRef = useRef(null);
  const featuredRef = useRef(null);
  const testimonialsRef = useRef(null);

  const featuredTestimonials = getFeaturedTestimonials();

  // Update document metadata
  useEffect(() => {
    const translatedMetadata = {
      ...metadata,
      title: intl.formatMessage({
        id: 'page.testimonials.title',
        defaultMessage: 'Customer Testimonials',
      }),
      description: intl.formatMessage({
        id: 'page.testimonials.subtitle',
        defaultMessage:
          'What our customers say about our services and solutions',
      }),
    };
    updatePageMetadata(translatedMetadata);
  }, [metadata, intl]);

  // Add intersection observer to check when sections are visible
  useEffect(() => {
    const observerOptions = {
      root: null,
      rootMargin: '0px',
      threshold: 0.1,
    };

    const observerCallback = (entries: IntersectionObserverEntry[]) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const sectionId = entry.target.getAttribute('data-section');
          if (sectionId) {
            setVisibleSections(prev => ({
              ...prev,
              [sectionId]: true,
            }));
          }
        }
      });
    };

    const observer = new IntersectionObserver(
      observerCallback,
      observerOptions
    );

    // Observe each section
    if (heroRef.current) observer.observe(heroRef.current);
    if (featuredRef.current) observer.observe(featuredRef.current);
    if (testimonialsRef.current) observer.observe(testimonialsRef.current);

    return () => {
      observer.disconnect();
    };
  }, []);

  useEffect(() => {
    setFilteredTestimonials(getTestimonialsByCategory(selectedCategory));
  }, [selectedCategory]);

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, index) => (
      <Star
        key={index}
        className={`h-4 w-4 ${
          index < rating ? 'fill-yellow-400 text-yellow-400' : 'text-gray-300'
        }`}
      />
    ));
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase();
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString(intl.locale, {
      year: 'numeric',
      month: 'long',
    });
  };

  const TestimonialCard = ({
    testimonial,
    featured = false,
  }: {
    testimonial: Testimonial;
    featured?: boolean;
  }) => (
    <Card
      className={`h-full transition-all duration-300 hover:shadow-lg ${featured ? 'border-primary' : ''}`}
    >
      <CardContent className="p-6">
        <div className="mb-4 flex items-start gap-4">
          <Quote className="mt-1 h-8 w-8 flex-shrink-0 text-primary/20" />
          <div className="flex-1">
            <div className="mb-2 flex items-center gap-2">
              <div className="flex items-center">
                {renderStars(testimonial.rating)}
              </div>
              <span className="text-sm text-muted-foreground">
                {testimonial.rating}{' '}
                {intl.formatMessage({ id: 'page.testimonials.stars' })}
              </span>
            </div>
            <blockquote className="mb-4 leading-relaxed text-foreground">
              {testimonial.content[
                intl.locale as keyof typeof testimonial.content
              ] || testimonial.content.en}
            </blockquote>
          </div>
        </div>

        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Avatar className="h-12 w-12">
              <AvatarImage
                src={testimonial.author.avatar}
                alt={testimonial.author.name}
              />
              <AvatarFallback className="bg-primary/10 font-medium text-primary">
                {getInitials(testimonial.author.name)}
              </AvatarFallback>
            </Avatar>
            <div>
              <div className="font-semibold text-foreground">
                {testimonial.author.name}
              </div>
              <div className="text-sm text-muted-foreground">
                {testimonial.author.title} at {testimonial.author.company}
              </div>
              <div className="mt-1 text-xs text-muted-foreground">
                {formatDate(testimonial.date)}
              </div>
            </div>
          </div>

          <div className="flex flex-col items-end gap-2">
            {featured && (
              <Badge variant="secondary" className="text-xs">
                {intl.formatMessage({ id: 'page.services.popular' })}
              </Badge>
            )}
            <Badge variant="outline" className="text-xs capitalize">
              {intl.formatMessage({
                id: `page.testimonials.filter.${testimonial.category}`,
              })}
            </Badge>
          </div>
        </div>
      </CardContent>
    </Card>
  );

  return (
    <div className="bg-background">
      {/* Hero Section */}
      <div
        ref={heroRef}
        data-section="hero"
        className={`section-reveal bg-gradient-to-r from-primary to-primary/80 text-white ${visibleSections.hero ? 'visible' : ''}`}
      >
        <div className="container mx-auto px-4 py-16 md:py-24">
          <div className="animate-hero-content mx-auto max-w-4xl text-center">
            <h1 className="animate-fadeIn mb-6 text-4xl font-bold">
              {intl.formatMessage({ id: 'page.testimonials.title' })}
            </h1>
            <p className="hero-element hero-delay-1 mb-8 text-xl opacity-90">
              {intl.formatMessage({ id: 'page.testimonials.subtitle' })}
            </p>
          </div>
        </div>
      </div>

      {/* Featured Testimonials */}
      <div
        ref={featuredRef}
        data-section="featured"
        className={`section-reveal bg-muted py-16 ${visibleSections.featured ? 'visible' : ''}`}
      >
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-7xl">
            <div className="animate-slideUp mb-12 text-center">
              <h2 className="mb-4 text-3xl font-bold md:text-4xl">
                {intl.formatMessage({ id: 'page.testimonials.featured.title' })}
              </h2>
              <p className="mx-auto max-w-2xl text-lg text-muted-foreground">
                {intl.formatMessage({
                  id: 'page.testimonials.featured.description',
                })}
              </p>
            </div>

            <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
              {featuredTestimonials.map((testimonial, index) => (
                <div
                  key={testimonial.id}
                  className="stagger-card animate-fadeIn"
                  style={{ animationDelay: `${index * 150}ms` }}
                >
                  <TestimonialCard testimonial={testimonial} featured />
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* All Testimonials */}
      <div
        ref={testimonialsRef}
        data-section="testimonials"
        className={`section-reveal container mx-auto px-4 py-16 ${visibleSections.testimonials ? 'visible' : ''}`}
      >
        <div className="mx-auto max-w-7xl">
          <div className="animate-slideUp mb-12 text-center">
            <h2 className="mb-4 text-3xl font-bold md:text-4xl">
              {intl.formatMessage({ id: 'page.testimonials.all.title' })}
            </h2>
            <p className="mx-auto mb-8 max-w-2xl text-lg text-muted-foreground">
              {intl.formatMessage({ id: 'page.testimonials.all.description' })}
            </p>

            {/* Category Filter */}
            <div className="animate-fadeIn animation-delay-200 mb-8 flex flex-wrap justify-center gap-2">
              {testimonialCategories.map(category => (
                <Button
                  key={category.id}
                  variant={
                    selectedCategory === category.id ? 'default' : 'outline'
                  }
                  size="sm"
                  onClick={() => setSelectedCategory(category.id)}
                  className="shimmer-effect transition-all duration-200"
                >
                  {category.name[intl.locale as keyof typeof category.name] ||
                    category.name.en}
                </Button>
              ))}
            </div>
          </div>

          <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
            {filteredTestimonials.map((testimonial, index) => (
              <div
                key={testimonial.id}
                className="stagger-card animate-fadeIn"
                style={{ animationDelay: `${index * 100}ms` }}
              >
                <TestimonialCard testimonial={testimonial} />
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="bg-primary/5 py-16">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl text-center">
            <h2 className="animate-slideUp mb-4 text-3xl font-bold md:text-4xl">
              {intl.formatMessage({ id: 'page.testimonials.cta.title' })}
            </h2>
            <p className="animate-slideUp animation-delay-200 mx-auto mb-8 max-w-2xl text-lg text-muted-foreground">
              {intl.formatMessage({ id: 'page.testimonials.cta.description' })}
            </p>
            <div className="animate-fadeIn animation-delay-300 flex flex-col justify-center gap-4 sm:flex-row">
              <Button size="lg" className="shimmer-effect px-8">
                {intl.formatMessage({ id: 'page.testimonials.cta.contact' })}
              </Button>
              <Button
                variant="outline"
                size="lg"
                className="shimmer-effect px-8"
              >
                {intl.formatMessage({ id: 'page.testimonials.cta.learn.more' })}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

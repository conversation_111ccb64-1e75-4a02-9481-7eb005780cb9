import React, { useState, useEffect, useRef } from 'react';
import { useIntl } from 'react-intl';
import { Check, X, Star, Zap } from 'lucide-react';
import { PAGE_METADATA } from '@/constants/site-config';
import { updatePageMetadata } from '@/utils';
import { But<PERSON> } from '@/components/ui/shadcn/button';
import { Card, CardContent, CardHeader } from '@/components/ui/shadcn/card';
import { Badge } from '@/components/ui/shadcn/badge';
import { Switch } from '@/components/ui/shadcn/switch';
import {
  pricingTiers,
  pricingComparison,
  formatPrice,
  calculateYearlySavingsPercentage,
  getTranslatedText,
} from '@/data/public/pricing';
import { BillingPeriod, PricingTier } from '@/types/public/pricing';
import '@/styles/animations.css'; // Import the centralized animations

export default function Page() {
  const intl = useIntl();
  const metadata = PAGE_METADATA.pricing;
  const [billingPeriod, setBillingPeriod] = useState<BillingPeriod>('monthly');

  // State to track which sections are visible
  const [visibleSections, setVisibleSections] = useState({
    hero: false,
    pricing: false,
    comparison: false,
    cta: false,
  });

  // Refs for the sections
  const heroRef = useRef(null);
  const pricingRef = useRef(null);
  const comparisonRef = useRef(null);
  const ctaRef = useRef(null);

  // Update document metadata
  useEffect(() => {
    const translatedMetadata = {
      ...metadata,
      title: intl.formatMessage({
        id: 'page.pricing.title',
        defaultMessage: 'Pricing Plans',
      }),
      description: intl.formatMessage({
        id: 'page.pricing.subtitle',
        defaultMessage:
          'Choose the perfect plan for your needs with transparent pricing and flexible options',
      }),
    };
    updatePageMetadata(translatedMetadata);
  }, [metadata, intl]);

  // Add intersection observer to check when sections are visible
  useEffect(() => {
    const observerOptions = {
      root: null,
      rootMargin: '0px',
      threshold: 0.1,
    };

    const observerCallback = (entries: IntersectionObserverEntry[]) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const sectionId = entry.target.getAttribute('data-section');
          if (sectionId) {
            setVisibleSections(prev => ({
              ...prev,
              [sectionId]: true,
            }));
          }
        }
      });
    };

    const observer = new IntersectionObserver(
      observerCallback,
      observerOptions
    );

    // Observe each section
    if (heroRef.current) observer.observe(heroRef.current);
    if (pricingRef.current) observer.observe(pricingRef.current);
    if (comparisonRef.current) observer.observe(comparisonRef.current);
    if (ctaRef.current) observer.observe(ctaRef.current);

    return () => {
      observer.disconnect();
    };
  }, []);

  const PricingCard = ({ tier }: { tier: PricingTier }) => {
    const price =
      billingPeriod === 'monthly' ? tier.price.monthly : tier.price.yearly;
    const savings = calculateYearlySavingsPercentage(tier);

    return (
      <Card
        className={`relative h-full transition-all duration-300 hover:shadow-xl ${
          tier.popular
            ? 'scale-105 border-primary shadow-lg'
            : 'hover:scale-105'
        }`}
      >
        {tier.popular && (
          <div className="absolute -top-4 left-1/2 -translate-x-1/2 transform">
            <Badge className="bg-primary px-4 py-1 text-primary-foreground">
              <Star className="mr-1 h-3 w-3" />
              {intl.formatMessage({ id: 'page.pricing.popular' })}
            </Badge>
          </div>
        )}

        <CardHeader className="pb-8 text-center">
          <div className="space-y-2">
            <h3 className="text-2xl font-bold text-foreground">
              {getTranslatedText(tier.name, intl.locale)}
            </h3>
            <p className="text-muted-foreground">
              {getTranslatedText(tier.description, intl.locale)}
            </p>
          </div>

          <div className="space-y-4">
            <div className="flex items-baseline justify-center gap-2">
              <span className="text-4xl font-bold text-foreground">
                {formatPrice(price, tier.price.currency)}
              </span>
              <span className="text-muted-foreground">
                /
                {intl.formatMessage({
                  id:
                    billingPeriod === 'monthly'
                      ? 'page.pricing.per.month'
                      : 'page.pricing.per.year',
                })}
              </span>
            </div>

            {billingPeriod === 'yearly' && savings > 0 && (
              <div className="flex items-center justify-center">
                <Badge
                  variant="secondary"
                  className="bg-green-50 text-green-600"
                >
                  <Zap className="mr-1 h-3 w-3" />
                  {intl.formatMessage(
                    { id: 'page.pricing.billing.save' },
                    { percentage: savings }
                  )}
                </Badge>
              </div>
            )}

            <p className="text-sm text-muted-foreground">
              {intl.formatMessage({
                id:
                  billingPeriod === 'monthly'
                    ? 'page.pricing.billed.monthly'
                    : 'page.pricing.billed.yearly',
              })}
            </p>
          </div>
        </CardHeader>

        <CardContent className="space-y-6">
          <div className="space-y-4">
            <h4 className="font-semibold text-foreground">
              {intl.formatMessage({ id: 'page.pricing.features.all' })}
            </h4>
            <ul className="space-y-3">
              {tier.features.map(feature => (
                <li key={feature.id} className="flex items-start gap-3">
                  {feature.included ? (
                    <Check className="mt-0.5 h-5 w-5 flex-shrink-0 text-green-500" />
                  ) : (
                    <X className="mt-0.5 h-5 w-5 flex-shrink-0 text-gray-300" />
                  )}
                  <span
                    className={`text-sm ${
                      feature.included
                        ? 'text-foreground'
                        : 'text-muted-foreground line-through'
                    } ${feature.highlight ? 'font-medium text-primary' : ''}`}
                  >
                    {getTranslatedText(feature.name, intl.locale)}
                  </span>
                </li>
              ))}
            </ul>
          </div>

          <div className="pt-6">
            <Button
              className="shimmer-effect w-full"
              variant={tier.cta.variant}
              size="lg"
              asChild
            >
              <a href={tier.cta.href}>
                {getTranslatedText(tier.cta.text, intl.locale)}
              </a>
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  };

  const ComparisonTable = () => (
    <div className="overflow-x-auto">
      <table className="w-full border-collapse">
        <thead>
          <tr className="border-b">
            <th className="px-6 py-4 text-left font-semibold text-foreground">
              {intl.formatMessage({ id: 'page.pricing.comparison.feature' })}
            </th>
            {pricingTiers.map(tier => (
              <th
                key={tier.id}
                className="px-6 py-4 text-center font-semibold text-foreground"
              >
                {getTranslatedText(tier.name, intl.locale)}
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {pricingComparison.map((category, categoryIndex) => (
            <React.Fragment key={categoryIndex}>
              <tr className="border-b bg-muted/30">
                <td
                  colSpan={pricingTiers.length + 1}
                  className="px-6 py-3 font-medium text-foreground"
                >
                  {getTranslatedText(category.category, intl.locale)}
                </td>
              </tr>
              {category.features.map(feature => (
                <tr key={feature.id} className="border-b hover:bg-muted/20">
                  <td className="px-6 py-4 text-foreground">
                    {getTranslatedText(feature.name, intl.locale)}
                  </td>
                  {pricingTiers.map(tier => (
                    <td key={tier.id} className="px-6 py-4 text-center">
                      {typeof feature.tiers[tier.id] === 'boolean' ? (
                        feature.tiers[tier.id] ? (
                          <Check className="mx-auto h-5 w-5 text-green-500" />
                        ) : (
                          <X className="mx-auto h-5 w-5 text-gray-300" />
                        )
                      ) : (
                        <span className="font-medium text-foreground">
                          {feature.tiers[tier.id]}
                        </span>
                      )}
                    </td>
                  ))}
                </tr>
              ))}
            </React.Fragment>
          ))}
        </tbody>
      </table>
    </div>
  );

  return (
    <div className="bg-background">
      {/* Hero Section */}
      <div
        ref={heroRef}
        data-section="hero"
        className={`section-reveal bg-gradient-to-r from-primary to-primary/80 text-white ${visibleSections.hero ? 'visible' : ''}`}
      >
        <div className="container mx-auto px-4 py-16 md:py-24">
          <div className="animate-hero-content mx-auto max-w-4xl text-center">
            <h1 className="animate-fadeIn mb-6 text-4xl font-bold">
              {intl.formatMessage({ id: 'page.pricing.title' })}
            </h1>
            <p className="animate-fadeIn animation-delay-200 mb-8 text-xl opacity-90">
              {intl.formatMessage({ id: 'page.pricing.subtitle' })}
            </p>

            {/* Billing Toggle */}
            <div className="animate-fadeIn animation-delay-300 flex items-center justify-center gap-4">
              <span
                className={`text-lg ${billingPeriod === 'monthly' ? 'font-medium text-white' : 'text-white/70'}`}
              >
                {intl.formatMessage({ id: 'page.pricing.billing.monthly' })}
              </span>
              <Switch
                checked={billingPeriod === 'yearly'}
                onCheckedChange={checked =>
                  setBillingPeriod(checked ? 'yearly' : 'monthly')
                }
                className="data-[state=checked]:bg-primary"
              />
              <span
                className={`text-lg ${billingPeriod === 'yearly' ? 'font-medium text-white' : 'text-white/70'}`}
              >
                {intl.formatMessage({ id: 'page.pricing.billing.yearly' })}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Pricing Cards */}
      <div
        ref={pricingRef}
        data-section="pricing"
        className={`section-reveal container mx-auto px-4 py-16 ${visibleSections.pricing ? 'visible' : ''}`}
      >
        <div className="mx-auto max-w-7xl">
          <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
            {pricingTiers.map((tier, index) => (
              <div
                key={tier.id}
                className="stagger-card animate-fadeIn"
                style={{ animationDelay: `${index * 150}ms` }}
              >
                <PricingCard tier={tier} />
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Comparison Table */}
      <div
        ref={comparisonRef}
        data-section="comparison"
        className={`section-reveal bg-muted py-16 ${visibleSections.comparison ? 'visible' : ''}`}
      >
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-7xl">
            <div className="animate-slideUp mb-12 text-center">
              <h2 className="mb-4 text-3xl font-bold md:text-4xl">
                {intl.formatMessage({ id: 'page.pricing.comparison.title' })}
              </h2>
              <p className="mx-auto max-w-2xl text-lg text-muted-foreground">
                {intl.formatMessage({ id: 'page.pricing.comparison.subtitle' })}
              </p>
            </div>

            <Card className="animate-fadeIn animation-delay-200">
              <CardContent className="p-0">
                <ComparisonTable />
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div
        ref={ctaRef}
        data-section="cta"
        className={`section-reveal container mx-auto px-4 py-16 ${visibleSections.cta ? 'visible' : ''}`}
      >
        <div className="mx-auto max-w-4xl text-center">
          <h2 className="animate-slideUp mb-4 text-3xl font-bold md:text-4xl">
            {intl.formatMessage({ id: 'page.pricing.cta.title' })}
          </h2>
          <p className="animate-slideUp animation-delay-200 mx-auto mb-8 max-w-2xl text-lg text-muted-foreground">
            {intl.formatMessage({ id: 'page.pricing.cta.description' })}
          </p>
          <div className="animate-fadeIn animation-delay-300 flex flex-col justify-center gap-4 sm:flex-row">
            <Button size="lg" className="shimmer-effect px-8">
              {intl.formatMessage({ id: 'page.pricing.cta.start.trial' })}
            </Button>
            <Button variant="outline" size="lg" className="shimmer-effect px-8">
              {intl.formatMessage({ id: 'page.pricing.cta.contact.sales' })}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}

import { PAGE_METADATA, SITE_INFO } from '@/constants/site-config';
import React, { useState, useEffect, useRef } from 'react';
import { updatePageMetadata } from '@/utils';
import { useIntl } from 'react-intl';
import { Office } from '@/types';
import '@/styles/animations.css'; // Import the centralized animations

/**
 * Contact Page Component
 *
 * Displays company contact information and a form for users to send messages.
 * Includes office locations, contact methods, and a map visualization.
 * Uses contact information from siteConfig.ts
 */
export default function Page() {
  const intl = useIntl();
  const metadata = PAGE_METADATA.contact;

  // Update document metadata
  useEffect(() => {
    const translatedTitle = intl.formatMessage({ id: 'page.contact.title' });
    const translatedDescription = intl.formatMessage({
      id: 'page.contact.subtitle',
    });

    updatePageMetadata({
      ...metadata,
      title: translatedTitle,
      description: translatedDescription,
    });
  }, [metadata, intl]);

  // State to track which sections are visible
  const [visibleSections, setVisibleSections] = useState({
    contactForm: false,
    contactInfo: false,
    map: false,
  });

  // Refs for the sections
  const contactFormRef = useRef(null);
  const contactInfoRef = useRef(null);
  const mapRef = useRef(null);

  // Add intersection observer to check when sections are visible
  useEffect(() => {
    const observerOptions = {
      root: null,
      rootMargin: '0px',
      threshold: 0.1,
    };

    const observerCallback = (entries: IntersectionObserverEntry[]) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const sectionId = entry.target.getAttribute('data-section');
          if (sectionId) {
            setVisibleSections(prev => ({
              ...prev,
              [sectionId]: true,
            }));
          }
        }
      });
    };

    const observer = new IntersectionObserver(
      observerCallback,
      observerOptions
    );

    // Observe each section
    if (contactFormRef.current) observer.observe(contactFormRef.current);
    if (contactInfoRef.current) observer.observe(contactInfoRef.current);
    if (mapRef.current) observer.observe(mapRef.current);

    return () => {
      observer.disconnect();
    };
  }, []);

  // Form state
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: '',
    department: 'general',
  });
  const [formStatus, setFormStatus] = useState<null | 'success' | 'error'>(
    null
  );
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Get contact info from siteConfig
  const contactInfo = {
    email: SITE_INFO.contact.email,
    phone: SITE_INFO.contact.phone,
    address: {
      headquarters: SITE_INFO.contact.offices[0] || {
        street: 'Main Street',
        city: 'City',
        state: 'State',
        zip: '12345',
        country: 'Country',
        mapLink: '',
      },
      // Use office locations from siteConfig
      offices: SITE_INFO.contact.offices || [],
    },
    socialMedia: SITE_INFO.contact.social,
  };

  // Departments for contact form dropdown
  const departments = [
    {
      id: 'general',
      name: intl.formatMessage({ id: 'page.contact.form.department.general' }),
    },
    {
      id: 'sales',
      name: intl.formatMessage({ id: 'page.contact.form.department.sales' }),
    },
    {
      id: 'support',
      name: intl.formatMessage({ id: 'page.contact.form.department.support' }),
    },
    {
      id: 'partnerships',
      name: intl.formatMessage({
        id: 'page.contact.form.department.partnerships',
      }),
    },
    {
      id: 'careers',
      name: intl.formatMessage({ id: 'page.contact.form.department.careers' }),
    },
  ];

  // Handle form input changes
  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Simulate API call with timeout
    try {
      // In a real application, you would send this data to your backend
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Simulate successful submission
      setFormStatus('success');
      setFormData({
        name: '',
        email: '',
        subject: '',
        message: '',
        department: 'general',
      });
    } catch {
      setFormStatus('error');
    } finally {
      setIsSubmitting(false);

      // Reset form status after 5 seconds
      setTimeout(() => {
        setFormStatus(null);
      }, 5000);
    }
  };

  // Map loading state
  const [mapsLoaded, setMapsLoaded] = useState(false);

  // Function to handle map iframe load
  const handleMapLoad = () => {
    setMapsLoaded(true);
  };

  return (
    <div className="bg-background">
      {/* Hero section */}
      <div className="bg-gradient-to-r from-primary to-primary/80 py-16 text-white">
        <div className="container mx-auto px-4">
          <div className="animate-hero-content mx-auto max-w-4xl text-center">
            <h1 className="hero-element mb-4 text-4xl font-bold">
              {intl.formatMessage({ id: 'page.contact' })}
            </h1>
            <p className="hero-element hero-delay-1 text-xl opacity-90">
              {intl.formatMessage({ id: 'page.contact.subtitle' })}
            </p>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-16">
        <div className="mx-auto max-w-6xl">
          <div className="grid grid-cols-1 gap-12 lg:grid-cols-2">
            {/* Contact form */}
            <div
              ref={contactFormRef}
              data-section="contactForm"
              className={`section-reveal ${visibleSections.contactForm ? 'visible' : ''}`}
            >
              <h2 className="animate-slideUp mb-6 text-2xl font-bold">
                {intl.formatMessage({ id: 'page.contact.form.title' })}
              </h2>

              {formStatus === 'success' && (
                <div className="animate-fadeIn mb-6 rounded border border-green-200 bg-green-50 px-4 py-3 text-green-700">
                  <p>
                    {intl.formatMessage({ id: 'page.contact.form.success' })}
                  </p>
                </div>
              )}

              {formStatus === 'error' && (
                <div className="animate-fadeIn mb-6 rounded border border-red-200 bg-red-50 px-4 py-3 text-red-700">
                  <p>{intl.formatMessage({ id: 'page.contact.form.error' })}</p>
                </div>
              )}

              <form
                onSubmit={handleSubmit}
                className="animate-fadeIn animation-delay-200 space-y-4"
              >
                <div>
                  <label
                    htmlFor="name"
                    className="mb-1 block text-sm font-medium text-foreground"
                  >
                    {intl.formatMessage({ id: 'page.contact.form.name' })} *
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    required
                    className="w-full rounded-md border border-input bg-background px-4 py-2 focus:border-primary focus:ring-primary"
                  />
                </div>

                <div>
                  <label
                    htmlFor="email"
                    className="mb-1 block text-sm font-medium text-foreground"
                  >
                    {intl.formatMessage({ id: 'page.contact.form.email' })} *
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleChange}
                    required
                    className="w-full rounded-md border border-input bg-background px-4 py-2 focus:border-primary focus:ring-primary"
                  />
                </div>

                <div>
                  <label
                    htmlFor="department"
                    className="mb-1 block text-sm font-medium text-foreground"
                  >
                    {intl.formatMessage({ id: 'page.contact.form.department' })}
                  </label>
                  <select
                    id="department"
                    name="department"
                    value={formData.department}
                    onChange={handleChange}
                    className="w-full rounded-md border border-input bg-background px-4 py-2 focus:border-primary focus:ring-primary"
                  >
                    {departments.map(dept => (
                      <option key={dept.id} value={dept.id}>
                        {dept.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label
                    htmlFor="subject"
                    className="mb-1 block text-sm font-medium text-foreground"
                  >
                    {intl.formatMessage({ id: 'page.contact.form.subject' })} *
                  </label>
                  <input
                    type="text"
                    id="subject"
                    name="subject"
                    value={formData.subject}
                    onChange={handleChange}
                    required
                    className="w-full rounded-md border border-input bg-background px-4 py-2 focus:border-primary focus:ring-primary"
                  />
                </div>

                <div>
                  <label
                    htmlFor="message"
                    className="mb-1 block text-sm font-medium text-foreground"
                  >
                    {intl.formatMessage({ id: 'page.contact.form.message' })} *
                  </label>
                  <textarea
                    id="message"
                    name="message"
                    value={formData.message}
                    onChange={handleChange}
                    required
                    rows={6}
                    className="w-full rounded-md border border-input bg-background px-4 py-2 focus:border-primary focus:ring-primary"
                  ></textarea>
                </div>

                <div className="pt-2">
                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className={`shimmer-effect w-full rounded-md bg-primary px-6 py-3 font-medium text-white transition-colors hover:bg-primary/80 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 ${
                      isSubmitting ? 'cursor-not-allowed opacity-70' : ''
                    }`}
                  >
                    {isSubmitting
                      ? intl.formatMessage({ id: 'page.contact.form.sending' })
                      : intl.formatMessage({ id: 'page.contact.form.send' })}
                  </button>
                </div>
              </form>
            </div>

            {/* Contact information */}
            <div
              ref={contactInfoRef}
              data-section="contactInfo"
              className={`section-reveal ${visibleSections.contactInfo ? 'visible' : ''}`}
            >
              <h2 className="animate-slideUp mb-6 text-2xl font-bold">
                {intl.formatMessage({ id: 'page.contact.info.title' })}
              </h2>

              <div className="animate-fadeIn animation-delay-200 space-y-8">
                {/* Contact methods */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-foreground">
                    {intl.formatMessage({
                      id: 'page.contact.info.get.in.touch',
                    })}
                  </h3>

                  <div className="flex items-start space-x-4">
                    <div className="mt-1 flex-shrink-0">
                      <svg
                        className="h-6 w-6 text-primary"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                        />
                      </svg>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-foreground">
                        {intl.formatMessage({ id: 'page.contact.info.email' })}
                      </p>
                      <p className="mt-1">
                        <a
                          href={`mailto:${contactInfo.email.general}`}
                          className="text-primary hover:underline"
                        >
                          {contactInfo.email.general}
                        </a>
                      </p>
                      <p className="mt-1">
                        <span className="text-sm text-muted-foreground">
                          {intl.formatMessage({
                            id: 'page.contact.info.support',
                          })}
                          :
                        </span>
                        <a
                          href={`mailto:${contactInfo.email.support}`}
                          className="text-primary hover:underline"
                        >
                          {contactInfo.email.support}
                        </a>
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-4">
                    <div className="mt-1 flex-shrink-0">
                      <svg
                        className="h-6 w-6 text-primary"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                        />
                      </svg>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-foreground">
                        {intl.formatMessage({ id: 'page.contact.info.phone' })}
                      </p>
                      <p className="mt-1">
                        <a
                          href={`tel:${contactInfo.phone.general.replace(/[^0-9+]/g, '')}`}
                          className="text-foreground"
                        >
                          {contactInfo.phone.general}
                        </a>
                      </p>
                      <p className="mt-1">
                        <span className="text-sm text-muted-foreground">
                          {intl.formatMessage({
                            id: 'page.contact.info.support',
                          })}
                          :
                        </span>
                        <a
                          href={`tel:${contactInfo.phone.support.replace(/[^0-9+]/g, '')}`}
                          className="text-foreground"
                        >
                          {contactInfo.phone.support}
                        </a>
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-4">
                    <div className="mt-1 flex-shrink-0">
                      <svg
                        className="h-6 w-6 text-primary"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                        />
                      </svg>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-foreground">
                        {intl.formatMessage({ id: 'page.contact.info.social' })}
                      </p>
                      <div className="mt-2 flex space-x-4">
                        <a
                          href={contactInfo.socialMedia.twitter}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="scale-effect text-muted-foreground hover:text-primary"
                        >
                          <svg
                            className="h-5 w-5"
                            fill="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"></path>
                          </svg>
                        </a>
                        <a
                          href={contactInfo.socialMedia.linkedin}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="scale-effect text-muted-foreground hover:text-primary"
                        >
                          <svg
                            className="h-5 w-5"
                            fill="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z" />
                          </svg>
                        </a>
                        <a
                          href={contactInfo.socialMedia.facebook}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="scale-effect text-muted-foreground hover:text-primary"
                        >
                          <svg
                            className="h-5 w-5"
                            fill="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path d="M22 12c0-5.523-4.477-10-10-10s-10 4.477-10 10c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54v-2.891h2.54v-2.203c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562v1.876h2.773l-.443 2.891h-2.33v6.988c4.781-.75 8.437-4.887 8.437-9.879z"></path>
                          </svg>
                        </a>
                        <a
                          href={contactInfo.socialMedia.instagram}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="scale-effect text-muted-foreground hover:text-pink-600 dark:hover:text-pink-400"
                        >
                          <svg
                            className="h-5 w-5"
                            fill="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z" />
                          </svg>
                        </a>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Headquarters address */}
                <div>
                  <h3 className="text-lg font-semibold text-foreground">
                    {intl.formatMessage({
                      id: 'page.contact.info.headquarters',
                    })}
                  </h3>
                  <div className="mt-3 flex items-start space-x-4">
                    <div className="mt-1 flex-shrink-0">
                      <svg
                        className="h-6 w-6 text-primary"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                        />
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                        />
                      </svg>
                    </div>
                    <div>
                      <address className="not-italic text-foreground">
                        {contactInfo.address.headquarters.street}
                        <br />
                        {contactInfo.address.headquarters.city},{' '}
                        {contactInfo.address.headquarters.state}{' '}
                        {contactInfo.address.headquarters.zip}
                        <br />
                        {contactInfo.address.headquarters.country}
                      </address>
                    </div>
                  </div>
                </div>

                {/* Office locations */}
                <div>
                  <h3 className="text-lg font-semibold text-foreground">
                    {intl.formatMessage({ id: 'page.contact.info.offices' })}
                  </h3>
                  <div className="mt-3 grid grid-cols-1 gap-4 sm:grid-cols-2">
                    {contactInfo.address.offices.map(
                      (office: Office, index: number) => (
                        <div
                          key={index}
                          className="stagger-card rounded-lg border border-border bg-card p-4"
                          style={{ animationDelay: `${index * 150}ms` }}
                        >
                          <h4 className="font-medium text-card-foreground">
                            {/* Use the office name directly from siteConfig */}
                            {office.name}
                          </h4>
                          <address className="mt-2 text-sm not-italic text-muted-foreground">
                            {office.street}
                            <br />
                            {office.city}, {office.state || ''} {office.zip}
                            <br />
                            {office.country}
                          </address>
                        </div>
                      )
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Map section with actual Google Maps integration */}
          <div
            ref={mapRef}
            data-section="map"
            className={`section-reveal mt-16 ${visibleSections.map ? 'visible' : ''}`}
          >
            <h2 className="animate-slideUp mb-6 text-2xl font-bold">
              {intl.formatMessage({ id: 'page.contact.map.title' })}
            </h2>
            <div className="animate-fadeIn animation-delay-200 relative h-96 overflow-hidden rounded-lg">
              {/* Loading state */}
              {!mapsLoaded && (
                <div className="absolute inset-0 z-10 flex items-center justify-center bg-muted">
                  <div className="flex animate-pulse flex-col items-center">
                    <svg
                      className="mb-2 h-10 w-10 animate-spin text-primary"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                      ></circle>
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      ></path>
                    </svg>
                    <span className="text-muted-foreground">
                      {intl.formatMessage({ id: 'page.contact.map.loading' })}
                    </span>
                  </div>
                </div>
              )}

              {/* Google Maps iframe */}
              <iframe
                src={contactInfo.address.headquarters.mapLink}
                width="100%"
                height="100%"
                style={{ border: 0 }}
                allowFullScreen
                loading="lazy"
                referrerPolicy="no-referrer-when-downgrade"
                onLoad={handleMapLoad}
                title={intl.formatMessage({ id: 'page.contact.map.title' })}
                className="h-full w-full"
              ></iframe>
            </div>

            {/* Office locations with maps */}
            {contactInfo.address.offices.length > 0 && (
              <div className="mt-8">
                <h3 className="mb-4 text-xl font-semibold">
                  {intl.formatMessage({
                    id: 'page.contact.map.office.locations',
                  })}
                </h3>
                <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                  {contactInfo.address.offices.map(
                    (office: Office, index: number) => (
                      <div
                        key={index}
                        className="stagger-card overflow-hidden rounded-lg border border-border"
                        style={{ animationDelay: `${index * 150}ms` }}
                      >
                        <div className="bg-muted p-4">
                          <h4 className="font-medium text-foreground">
                            {office.name}
                          </h4>
                          <address className="mt-1 text-sm not-italic text-muted-foreground">
                            {office.street}, {office.city}, {office.country}
                          </address>
                        </div>
                        <div className="relative h-48">
                          {!mapsLoaded && (
                            <div className="absolute inset-0 z-10 flex items-center justify-center bg-muted">
                              <div className="animate-pulse"></div>
                            </div>
                          )}
                          <iframe
                            src={office.mapLink}
                            width="100%"
                            height="100%"
                            style={{ border: 0 }}
                            allowFullScreen
                            loading="lazy"
                            referrerPolicy="no-referrer-when-downgrade"
                            onLoad={handleMapLoad}
                            title={`${office.name} Map`}
                            className="h-full w-full"
                          ></iframe>
                        </div>
                      </div>
                    )
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

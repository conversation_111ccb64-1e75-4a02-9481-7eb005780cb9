import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useIntl } from 'react-intl';
import { updatePageMetadata } from '@/utils';
import { PAGE_METADATA } from '@/constants/site-config';
import { Button } from '@/components/ui/shadcn/button';
import { Badge } from '@/components/ui/shadcn/badge';
import { Input } from '@/components/ui/shadcn/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/shadcn/select';
import PageLoader from '@/components/base/page-loader';

interface SupportTicket {
  id: string;
  subject: string;
  status: 'open' | 'in-progress' | 'resolved' | 'closed';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  category: string;
  createdAt: string;
  updatedAt: string;
  description: string;
}

export default function Page() {
  const navigate = useNavigate();
  const intl = useIntl();
  const [tickets, setTickets] = useState<SupportTicket[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [priorityFilter, setPriorityFilter] = useState<string>('all');

  useEffect(() => {
    updatePageMetadata({
      ...PAGE_METADATA.support,
      title: intl.formatMessage({ id: 'page.support.tickets.title' }),
      description: intl.formatMessage({
        id: 'page.support.tickets.description',
      }),
    });
  }, [intl]);

  useEffect(() => {
    // Mock loading tickets - in real app this would fetch from API
    const loadTickets = async () => {
      setLoading(true);

      // Mock data - in real app this would come from API
      const mockTickets: SupportTicket[] = [
        {
          id: 'TICK-001',
          subject: 'Unable to access dashboard',
          status: 'open',
          priority: 'high',
          category: 'technical',
          createdAt: '2023-11-20T10:30:00Z',
          updatedAt: '2023-11-20T10:30:00Z',
          description: 'I cannot access my dashboard after the recent update.',
        },
        {
          id: 'TICK-002',
          subject: 'Billing question about subscription',
          status: 'in-progress',
          priority: 'medium',
          category: 'billing',
          createdAt: '2023-11-19T14:15:00Z',
          updatedAt: '2023-11-20T09:45:00Z',
          description:
            'I have questions about my monthly subscription charges.',
        },
        {
          id: 'TICK-003',
          subject: 'Feature request: Dark mode',
          status: 'resolved',
          priority: 'low',
          category: 'feature',
          createdAt: '2023-11-18T16:20:00Z',
          updatedAt: '2023-11-19T11:30:00Z',
          description: 'Would love to see a dark mode option in the interface.',
        },
      ];

      setTickets(mockTickets);
      setLoading(false);
    };

    loadTickets();
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open':
        return 'bg-blue-100 text-blue-800';
      case 'in-progress':
        return 'bg-yellow-100 text-yellow-800';
      case 'resolved':
        return 'bg-green-100 text-green-800';
      case 'closed':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'bg-red-100 text-red-800';
      case 'high':
        return 'bg-orange-100 text-orange-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'low':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const filteredTickets = tickets.filter(ticket => {
    const matchesSearch =
      ticket.subject.toLowerCase().includes(searchQuery.toLowerCase()) ||
      ticket.description.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesStatus =
      statusFilter === 'all' || ticket.status === statusFilter;
    const matchesPriority =
      priorityFilter === 'all' || ticket.priority === priorityFilter;

    return matchesSearch && matchesStatus && matchesPriority;
  });

  if (loading) {
    return <PageLoader />;
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="border-b bg-muted/10">
        <div className="container mx-auto px-4 py-6">
          <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
            <div>
              <h1 className="text-2xl font-bold">
                {intl.formatMessage({ id: 'page.support.tickets.title' })}
              </h1>
              <p className="text-muted-foreground">
                {intl.formatMessage({ id: 'page.support.tickets.subtitle' })}
              </p>
            </div>
            <Button
              onClick={() => navigate('/support/tickets/create')}
              className="shimmer-effect"
            >
              <svg
                className="mr-2 h-4 w-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 4v16m8-8H4"
                />
              </svg>
              {intl.formatMessage({ id: 'page.support.tickets.create' })}
            </Button>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        {/* Filters */}
        <div className="mb-6 flex flex-col gap-4 sm:flex-row">
          <div className="flex-1">
            <Input
              type="text"
              placeholder={intl.formatMessage({
                id: 'page.support.tickets.search.placeholder',
              })}
              value={searchQuery}
              onChange={e => setSearchQuery(e.target.value)}
              className="w-full"
            />
          </div>
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-full sm:w-48">
              <SelectValue
                placeholder={intl.formatMessage({
                  id: 'page.support.tickets.filter.status',
                })}
              />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">
                {intl.formatMessage({ id: 'common.all' })}
              </SelectItem>
              <SelectItem value="open">
                {intl.formatMessage({ id: 'page.support.tickets.status.open' })}
              </SelectItem>
              <SelectItem value="in-progress">
                {intl.formatMessage({
                  id: 'page.support.tickets.status.in-progress',
                })}
              </SelectItem>
              <SelectItem value="resolved">
                {intl.formatMessage({
                  id: 'page.support.tickets.status.resolved',
                })}
              </SelectItem>
              <SelectItem value="closed">
                {intl.formatMessage({
                  id: 'page.support.tickets.status.closed',
                })}
              </SelectItem>
            </SelectContent>
          </Select>
          <Select value={priorityFilter} onValueChange={setPriorityFilter}>
            <SelectTrigger className="w-full sm:w-48">
              <SelectValue
                placeholder={intl.formatMessage({
                  id: 'page.support.tickets.filter.priority',
                })}
              />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">
                {intl.formatMessage({ id: 'common.all' })}
              </SelectItem>
              <SelectItem value="urgent">
                {intl.formatMessage({
                  id: 'page.support.tickets.priority.urgent',
                })}
              </SelectItem>
              <SelectItem value="high">
                {intl.formatMessage({
                  id: 'page.support.tickets.priority.high',
                })}
              </SelectItem>
              <SelectItem value="medium">
                {intl.formatMessage({
                  id: 'page.support.tickets.priority.medium',
                })}
              </SelectItem>
              <SelectItem value="low">
                {intl.formatMessage({
                  id: 'page.support.tickets.priority.low',
                })}
              </SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Tickets list */}
        {filteredTickets.length === 0 ? (
          <div className="rounded-lg border bg-muted/10 py-12 text-center">
            <div className="mb-4">
              <svg
                className="mx-auto h-12 w-12 text-muted-foreground"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={1}
                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                />
              </svg>
            </div>
            <h3 className="mb-2 text-lg font-medium">
              {searchQuery || statusFilter !== 'all' || priorityFilter !== 'all'
                ? intl.formatMessage({
                    id: 'page.support.tickets.no-results.title',
                  })
                : intl.formatMessage({
                    id: 'page.support.tickets.empty.title',
                  })}
            </h3>
            <p className="mb-6 text-muted-foreground">
              {searchQuery || statusFilter !== 'all' || priorityFilter !== 'all'
                ? intl.formatMessage({
                    id: 'page.support.tickets.no-results.message',
                  })
                : intl.formatMessage({
                    id: 'page.support.tickets.empty.message',
                  })}
            </p>
            <Button
              onClick={() => navigate('/support/tickets/create')}
              variant="outline"
            >
              {intl.formatMessage({ id: 'page.support.tickets.create' })}
            </Button>
          </div>
        ) : (
          <div className="space-y-4">
            {filteredTickets.map(ticket => (
              <div
                key={ticket.id}
                onClick={() => navigate(`/support/tickets/${ticket.id}`)}
                className="cursor-pointer rounded-lg border p-4 transition-shadow hover:shadow-md"
              >
                <div className="mb-2 flex items-start justify-between">
                  <div className="flex-1">
                    <div className="mb-1 flex items-center gap-2">
                      <span className="font-mono text-sm text-muted-foreground">
                        {ticket.id}
                      </span>
                      <Badge className={getStatusColor(ticket.status)}>
                        {intl.formatMessage({
                          id: `page.support.tickets.status.${ticket.status}`,
                        })}
                      </Badge>
                      <Badge className={getPriorityColor(ticket.priority)}>
                        {intl.formatMessage({
                          id: `page.support.tickets.priority.${ticket.priority}`,
                        })}
                      </Badge>
                    </div>
                    <h3 className="mb-1 text-lg font-medium">
                      {ticket.subject}
                    </h3>
                    <p className="mb-2 line-clamp-2 text-sm text-muted-foreground">
                      {ticket.description}
                    </p>
                  </div>
                  <div className="ml-4">
                    <svg
                      className="h-5 w-5 text-muted-foreground"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 5l7 7-7 7"
                      />
                    </svg>
                  </div>
                </div>
                <div className="flex items-center justify-between text-xs text-muted-foreground">
                  <span className="capitalize">
                    {intl.formatMessage({
                      id: `page.support.category.${ticket.category}`,
                    })}
                  </span>
                  <div className="flex items-center gap-4">
                    <span>
                      {intl.formatMessage({ id: 'common.created' })}:{' '}
                      {new Date(ticket.createdAt).toLocaleDateString(
                        intl.locale
                      )}
                    </span>
                    <span>
                      {intl.formatMessage({ id: 'common.updated' })}:{' '}
                      {new Date(ticket.updatedAt).toLocaleDateString(
                        intl.locale
                      )}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Back to support */}
        <div className="mt-8 text-center">
          <Button variant="outline" onClick={() => navigate('/support')}>
            <svg
              className="mr-2 h-4 w-4"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M10 19l-7-7m0 0l7-7m-7 7h18"
              />
            </svg>
            {intl.formatMessage({ id: 'page.support.back-to-support' })}
          </Button>
        </div>
      </div>
    </div>
  );
}

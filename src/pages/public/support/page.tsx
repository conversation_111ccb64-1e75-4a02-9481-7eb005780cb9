import { PAGE_METADATA } from '@/constants/site-config';
import { useState, useEffect } from 'react';
import { updatePageMetadata } from '@/utils';
import { useIntl } from 'react-intl';
import { useNavigate } from 'react-router-dom';
import {
  getSupportCategories,
  getPopularArticles,
} from '@/data/public/support';
import '@/styles/animations.css';

export default function SupportPage() {
  const intl = useIntl();
  const navigate = useNavigate();
  const metadata = PAGE_METADATA.support;
  const [searchQuery, setSearchQuery] = useState('');
  const currentLocale = intl.locale as 'en' | 'zh' | 'zh-TW';

  useEffect(() => {
    const translatedTitle = intl.formatMessage({ id: 'page.support.title' });
    const translatedDescription = intl.formatMessage({
      id: 'page.support.subtitle',
    });

    updatePageMetadata({
      ...metadata,
      title: translatedTitle,
      description: translatedDescription,
    });
  }, [metadata, intl]);

  const categories = getSupportCategories();
  const popularArticles = getPopularArticles();

  const filteredCategories = categories.filter(category => {
    if (!searchQuery) return true;
    const categoryName = category.name[currentLocale] || category.name.en;
    const categoryDesc =
      category.description[currentLocale] || category.description.en;

    return (
      categoryName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      categoryDesc.toLowerCase().includes(searchQuery.toLowerCase())
    );
  });

  const popularArticlesData = popularArticles
    .map(article => ({
      ...article,
      title: article.title[currentLocale] || article.title.en,
      excerpt: article.excerpt[currentLocale] || article.excerpt.en,
    }))
    .filter(article => {
      if (!searchQuery) return true;
      return article.title.toLowerCase().includes(searchQuery.toLowerCase());
    });

  return (
    <div className="bg-background">
      {/* Hero section */}
      <div className="bg-gradient-to-r from-primary to-primary/80 text-white">
        <div className="container mx-auto px-4 py-16">
          <div className="mx-auto max-w-4xl text-center">
            <h1 className="animate-fadeIn mb-6 text-4xl font-bold">
              {intl.formatMessage({ id: 'page.support.title' })}
            </h1>
            <p className="animate-fadeIn animation-delay-100 mb-8 text-xl opacity-90">
              {intl.formatMessage({ id: 'page.support.subtitle' })}
            </p>

            <div className="animate-fadeIn animation-delay-200 relative mx-auto max-w-2xl">
              <div className="absolute left-3 top-3 text-muted-foreground">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-6 w-6"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                  />
                </svg>
              </div>
              <input
                type="text"
                placeholder={intl.formatMessage({
                  id: 'page.support.search.placeholder',
                })}
                value={searchQuery}
                onChange={e => setSearchQuery(e.target.value)}
                className="w-full rounded-lg bg-background py-3 pl-12 pr-4 text-foreground shadow-md focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Help categories */}
      <div className="container mx-auto px-4 py-16">
        <div className="mx-auto max-w-6xl">
          <h2 className="animate-slideUp mb-8 text-center text-2xl font-bold">
            {intl.formatMessage({ id: 'page.support.categories.title' })}
          </h2>

          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
            {filteredCategories.map((category, index) => (
              <div
                key={category.id}
                className="cursor-pointer rounded-lg border border-border bg-background p-6 transition-shadow hover:shadow-md"
                style={{ animationDelay: `${index * 150}ms` }}
                onClick={() => navigate(`/support/category/${category.id}`)}
              >
                <div className="mb-4 text-primary">
                  <svg
                    className="h-6 w-6"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d={category.icon}
                    />
                  </svg>
                </div>
                <h3 className="mb-2 text-xl font-semibold">
                  {category.name[currentLocale] || category.name.en}
                </h3>
                <p className="mb-4 text-sm text-muted-foreground">
                  {category.description[currentLocale] ||
                    category.description.en}
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Popular articles */}
      <div className="bg-muted/50 py-16">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl">
            <h2 className="animate-slideUp mb-8 text-center text-2xl font-bold">
              {intl.formatMessage({ id: 'page.support.popular.title' })}
            </h2>

            <div className="overflow-hidden rounded-lg border border-border bg-background shadow-sm">
              {popularArticlesData.map((article, index) => (
                <div
                  key={article.id}
                  className={`${index !== 0 ? 'border-t border-border' : ''} cursor-pointer`}
                  onClick={() => navigate(`/support/article/${article.id}`)}
                >
                  <div className="block p-4 transition-colors hover:bg-muted/50">
                    <div className="flex items-center">
                      <div className="mr-4 flex-shrink-0 rounded-full bg-primary/10 p-2">
                        <svg
                          className="h-5 w-5 text-primary"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                          />
                        </svg>
                      </div>
                      <div>
                        <h3 className="text-lg font-medium text-foreground">
                          {article.title}
                        </h3>
                        <p className="mt-1 text-sm text-muted-foreground">
                          {article.excerpt}
                        </p>
                        <span className="mt-1 block text-xs text-muted-foreground">
                          {intl.formatMessage(
                            { id: 'page.support.article.reading-time' },
                            { minutes: article.readingTime }
                          )}
                        </span>
                      </div>
                      <div className="ml-auto">
                        <svg
                          className="h-5 w-5 text-muted-foreground"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M9 5l7 7-7 7"
                          />
                        </svg>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Support ticket section - ADDED NAVIGATION TO TICKETS */}
      <div className="bg-muted/50 py-16">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-3xl rounded-lg border border-border bg-background p-8 shadow-md">
            <div className="flex flex-col items-center md:flex-row">
              <div className="mb-6 md:mb-0 md:mr-8 md:w-1/2">
                <h2 className="mb-2 text-2xl font-bold">
                  {intl.formatMessage({ id: 'page.support.tickets.title' })}
                </h2>
                <p className="mb-4 text-muted-foreground">
                  {intl.formatMessage({
                    id: 'page.support.tickets.description',
                  })}
                </p>
                <div className="flex gap-4">
                  <button
                    onClick={() => navigate('/support/tickets/create')}
                    className="inline-block rounded-md bg-primary px-4 py-2 font-medium text-white transition-colors hover:bg-primary/80"
                  >
                    {intl.formatMessage({ id: 'page.support.tickets.create' })}
                  </button>
                  <button
                    onClick={() => navigate('/support/tickets')}
                    className="inline-block rounded-md border border-primary px-4 py-2 font-medium text-primary transition-colors hover:bg-primary/10"
                  >
                    {intl.formatMessage({
                      id: 'page.support.tickets.view-all',
                    })}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useIntl } from 'react-intl';
import { updatePageMetadata } from '@/utils';
import { PAGE_METADATA } from '@/constants/site-config';
import { getSupportArticleById } from '@/data/public/support';
import { SupportArticleDetail } from '@/types/public/support';
import { ChevronLeft, ThumbsUp, ThumbsDown, Star } from 'lucide-react';
import PageLoader from '@/components/base/page-loader';

export default function Page() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const intl = useIntl();
  const [article, setArticle] = useState<SupportArticleDetail | null>(null);
  const [relatedArticles, setRelatedArticles] = useState<
    SupportArticleDetail[]
  >([]);
  const [rating, setRating] = useState<number>(0);
  const [feedback, setFeedback] = useState<'helpful' | 'not-helpful' | null>(
    null
  );
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadArticle = async () => {
      if (!id) return;

      try {
        const articleData = await getSupportArticleById(id);
        if (articleData) {
          setArticle(articleData);
          // Related articles functionality can be added later
          const related: any[] = [];
          setRelatedArticles(related);
        }
      } catch (error) {
        console.error('Error loading support article:', error);
      } finally {
        setLoading(false);
      }
    };

    loadArticle();
  }, [id]);

  useEffect(() => {
    if (article) {
      const locale = intl.locale;
      const title =
        article.title[locale as keyof typeof article.title] || article.title.en;
      const description =
        article.excerpt[locale as keyof typeof article.excerpt] ||
        article.excerpt.en;

      updatePageMetadata({
        ...PAGE_METADATA.supportArticle,
        title: title,
        description: description,
      });
    }
  }, [article, intl.locale]);

  const handleRating = (value: number) => {
    setRating(value);
    console.log('Article rated:', value);
  };

  const handleFeedback = (type: 'helpful' | 'not-helpful') => {
    setFeedback(type);
    console.log('Feedback submitted:', type);
  };

  if (loading) {
    return <PageLoader />;
  }

  if (!article) {
    return (
      <div className="min-h-screen bg-background">
        <div className="bg-gradient-to-r from-primary to-primary/80 text-white">
          <div className="container mx-auto px-4 py-16">
            <div className="mx-auto max-w-4xl text-center">
              <div className="mb-6">
                <svg
                  className="mx-auto h-16 w-16 text-white/60"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={1}
                    d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                  />
                </svg>
              </div>
              <h1 className="mb-4 text-3xl font-bold">
                {intl.formatMessage({ id: 'page.support.article.not-found' })}
              </h1>
              <p className="mb-8 text-xl opacity-90">
                {intl.formatMessage({
                  id: 'page.support.article.not-found.description',
                  defaultMessage:
                    "The support article you're looking for could not be found.",
                })}
              </p>
            </div>
          </div>
        </div>

        <div className="container mx-auto px-4 py-12">
          <div className="mx-auto max-w-2xl text-center">
            <div className="grid gap-4 md:grid-cols-2">
              <button
                onClick={() => navigate('/support')}
                className="flex items-center justify-center gap-3 rounded-lg border border-border p-4 transition-colors hover:bg-accent"
              >
                <ChevronLeft className="h-5 w-5 text-primary" />
                <span className="font-medium">
                  {intl.formatMessage({
                    id: 'page.support.article.back-to-support',
                  })}
                </span>
              </button>
              <button
                onClick={() => navigate('/support/tickets/create')}
                className="flex items-center justify-center gap-3 rounded-lg border border-border p-4 transition-colors hover:bg-accent"
              >
                <svg
                  className="h-5 w-5 text-primary"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 4v16m8-8H4"
                  />
                </svg>
                <span className="font-medium">
                  {intl.formatMessage({
                    id: 'page.support.article.create-ticket',
                    defaultMessage: 'Create Support Ticket',
                  })}
                </span>
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const getTranslatedContent = (content: any) => {
    if (typeof content === 'string') return content;
    return content[intl.locale as keyof typeof content] || content.en;
  };

  return (
    <div className="container mx-auto px-4 py-12">
      {/* Back Navigation */}
      <button
        onClick={() => navigate('/support')}
        className="mb-6 flex items-center gap-2 text-blue-600 hover:text-blue-800"
      >
        <ChevronLeft className="h-4 w-4" />
        {intl.formatMessage({ id: 'page.support.article.back-to-support' })}
      </button>

      {/* Article Header */}
      <div className="mb-8">
        <div className="mb-2 flex items-center gap-2 text-sm text-muted-foreground">
          <span className="capitalize">{article.category}</span>
          <span>•</span>
          <span>
            {intl.formatMessage(
              { id: 'page.support.article.reading-time' },
              { minutes: article.readingTime }
            )}
          </span>
          <span>•</span>
          <span>{article.views} views</span>
        </div>
        <h1 className="mb-4 text-3xl font-bold">
          {article.title[intl.locale as keyof typeof article.title] ||
            article.title.en}
        </h1>
        <p className="text-lg text-gray-600">
          {article.excerpt[intl.locale as keyof typeof article.excerpt] ||
            article.excerpt.en}
        </p>
      </div>

      {/* Article Content */}
      <div className="prose dark:prose-invert mb-12 max-w-none">
        {article.content.map((section, index) => (
          <div key={index} className="mb-8">
            {section.type === 'heading' && (
              <h2 className="mb-4 text-2xl font-semibold">
                {getTranslatedContent(section.content)}
              </h2>
            )}
            {section.type === 'paragraph' && (
              <p className="mb-4 leading-relaxed">
                {getTranslatedContent(section.content)}
              </p>
            )}
            {section.type === 'list' && (
              <div className="mb-4">
                <p className="mb-2 font-medium">
                  {getTranslatedContent(section.content)}
                </p>
                <ul className="list-disc space-y-2 pl-6">
                  {section.items?.map((item, itemIndex) => (
                    <li key={itemIndex}>{getTranslatedContent(item)}</li>
                  ))}
                </ul>
              </div>
            )}
            {section.type === 'code' && (
              <pre className="mb-4 overflow-x-auto rounded-lg bg-gray-100 p-4 dark:bg-gray-800">
                <code>{getTranslatedContent(section.content)}</code>
              </pre>
            )}
            {section.type === 'note' && (
              <div className="mb-4 border-l-4 border-blue-400 bg-blue-50 p-4 dark:bg-blue-900/20">
                <p className="text-blue-800 dark:text-blue-200">
                  {getTranslatedContent(section.content)}
                </p>
              </div>
            )}
            {section.type === 'warning' && (
              <div className="mb-4 border-l-4 border-yellow-400 bg-yellow-50 p-4 dark:bg-yellow-900/20">
                <p className="text-yellow-800 dark:text-yellow-200">
                  {getTranslatedContent(section.content)}
                </p>
              </div>
            )}
            {section.type === 'image' && section.src && (
              <div className="mb-4">
                <img
                  src={section.src}
                  alt={getTranslatedContent(section.alt || '')}
                  className="h-auto max-w-full rounded-lg"
                />
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Article Rating */}
      <div className="mb-8 border-t pt-8">
        <h3 className="mb-4 text-lg font-semibold">
          {intl.formatMessage({ id: 'page.support.article.rate-article' })}
        </h3>
        <div className="mb-4 flex items-center gap-2">
          {[1, 2, 3, 4, 5].map(star => (
            <button
              key={star}
              onClick={() => handleRating(star)}
              className={`p-1 ${
                star <= rating ? 'text-yellow-400' : 'text-gray-300'
              } transition-colors hover:text-yellow-400`}
            >
              <Star className="h-6 w-6 fill-current" />
            </button>
          ))}
        </div>

        <div className="flex items-center gap-4">
          <span className="text-sm text-gray-600">
            {intl.formatMessage({ id: 'page.support.article.was-helpful' })}
          </span>
          <button
            onClick={() => handleFeedback('helpful')}
            className={`flex items-center gap-2 rounded-lg border px-4 py-2 transition-colors ${
              feedback === 'helpful'
                ? 'border-green-200 bg-green-50 text-green-700'
                : 'border-gray-200 hover:bg-gray-50'
            }`}
          >
            <ThumbsUp className="h-4 w-4" />
            {intl.formatMessage({ id: 'page.support.article.helpful' })}
          </button>
          <button
            onClick={() => handleFeedback('not-helpful')}
            className={`flex items-center gap-2 rounded-lg border px-4 py-2 transition-colors ${
              feedback === 'not-helpful'
                ? 'border-red-200 bg-red-50 text-red-700'
                : 'border-gray-200 hover:bg-gray-50'
            }`}
          >
            <ThumbsDown className="h-4 w-4" />
            {intl.formatMessage({ id: 'page.support.article.not-helpful' })}
          </button>
        </div>
      </div>

      {/* Related Articles */}
      {relatedArticles.length > 0 && (
        <div className="border-t pt-8">
          <h3 className="mb-6 text-lg font-semibold">
            {intl.formatMessage({
              id: 'page.support.article.related-articles',
            })}
          </h3>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {relatedArticles.map(relatedArticle => (
              <div
                key={relatedArticle.id}
                onClick={() =>
                  navigate(`/support/article/${relatedArticle.id}`)
                }
                className="cursor-pointer rounded-lg border p-4 transition-shadow hover:shadow-md"
              >
                <h4 className="mb-2 font-medium">
                  {relatedArticle.title[
                    intl.locale as keyof typeof relatedArticle.title
                  ] || relatedArticle.title.en}
                </h4>
                <p className="mb-2 text-sm text-gray-600">
                  {relatedArticle.excerpt[
                    intl.locale as keyof typeof relatedArticle.excerpt
                  ] || relatedArticle.excerpt.en}
                </p>
                <div className="flex items-center justify-between text-xs text-muted-foreground">
                  <span className="capitalize">{relatedArticle.category}</span>
                  <span>
                    {intl.formatMessage(
                      { id: 'page.support.article.reading-time' },
                      { minutes: relatedArticle.readingTime }
                    )}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}

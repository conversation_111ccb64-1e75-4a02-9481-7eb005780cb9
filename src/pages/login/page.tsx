import { LoginForm } from '@/components/forms/login-form';
import { Link } from 'react-router-dom';
import { useIntl } from 'react-intl';
import { SITE_INFO } from '@/constants/site-config';
import { useEffect, useRef, useState } from 'react';
import { FormErrorBoundary } from '@/components/base/boundary/form-error-boundary';
import '@/styles/animations.css'; // Import the centralized animations

/**
 * Login Page Component
 *
 * @description Provides the main login page with a link to return to home page
 * via the application logo/name instead of a back button
 *
 * @returns {JSX.Element} The Login Page component
 */
export default function Page() {
  const intl = useIntl();
  const companyName = SITE_INFO.organization.name;
  const displayName = SITE_INFO.organization.displayName || companyName;
  const appName = SITE_INFO.application.name;

  // State to track which sections are visible
  const [visibleSections, setVisibleSections] = useState({
    logo: false,
    form: false,
    footer: false,
  });

  // Refs for the sections
  const logoRef = useRef(null);
  const formRef = useRef(null);
  const footerRef = useRef(null);

  // Add intersection observer to check when sections are visible
  useEffect(() => {
    const observerOptions = {
      root: null,
      rootMargin: '0px',
      threshold: 0.1,
    };

    const observerCallback = (entries: IntersectionObserverEntry[]) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const sectionId = entry.target.getAttribute('data-section');
          if (sectionId) {
            setVisibleSections(prev => ({
              ...prev,
              [sectionId]: true,
            }));
          }
        }
      });
    };

    const observer = new IntersectionObserver(
      observerCallback,
      observerOptions
    );

    // Observe each section
    if (logoRef.current) observer.observe(logoRef.current);
    if (formRef.current) observer.observe(formRef.current);
    if (footerRef.current) observer.observe(footerRef.current);

    return () => {
      observer.disconnect();
    };
  }, []);

  return (
    <div className="flex min-h-screen w-full flex-col items-center justify-center px-4 md:px-6 lg:px-8">
      <div
        ref={logoRef}
        data-section="logo"
        className={`section-reveal mb-6 w-full max-w-sm text-center ${visibleSections.logo ? 'visible' : ''}`}
      >
        <Link
          to="/"
          className="animate-fadeIn inline-flex flex-col items-center transition-opacity hover:opacity-80"
          aria-label={intl.formatMessage({ id: 'navigation.home' })}
        >
          <span className="mb-1 text-xl font-bold text-primary">
            {displayName}
          </span>
          <span className="text-sm text-muted-foreground">{appName}</span>
        </Link>
      </div>
      <div
        ref={formRef}
        data-section="form"
        className={`section-reveal w-full max-w-sm ${visibleSections.form ? 'visible' : ''}`}
      >
        <div className="animate-fadeIn animation-delay-200">
          <FormErrorBoundary>
            <LoginForm showBackButton={false} />
          </FormErrorBoundary>
        </div>
      </div>
      <div
        ref={footerRef}
        data-section="footer"
        className={`section-reveal mt-4 w-full max-w-sm text-center ${visibleSections.footer ? 'visible' : ''}`}
      >
        <Link
          to="/"
          className="shimmer-effect animate-fadeIn animation-delay-300 text-sm text-muted-foreground transition-colors hover:text-primary"
        >
          {intl.formatMessage({ id: 'navigation.returnHome' })}
        </Link>
      </div>
    </div>
  );
}

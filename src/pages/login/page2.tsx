import {
  <PERSON>,
  CardContent,
  CardDescription,
  <PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ooter,
} from '@/components/ui/shadcn/card';
import { Button } from '@/components/ui/shadcn/button';
import { Separator } from '@/components/ui/shadcn/separator';
import { LoginFormAlt } from '@/components/forms/login-form-alt';
import { Link } from 'react-router-dom';
import { useIntl } from 'react-intl';
import { SITE_INFO } from '@/constants/site-config';

export default function Login() {
  const intl = useIntl();
  const companyName = SITE_INFO.organization.name;
  const displayName = SITE_INFO.organization.displayName || companyName;
  const appName = SITE_INFO.application.name;
  return (
    <div className="min-h-screen w-full lg:grid lg:grid-cols-2">
      {/* Left side - Brand/Testimonial - Hidden on mobile, shown on desktop */}
      <div className="relative hidden h-full flex-col bg-muted p-10 text-white dark:border-r lg:flex">
        <div className="absolute inset-0 bg-zinc-900" />
        <div className="relative z-20 flex items-center text-lg font-medium">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="mr-2 h-6 w-6"
          >
            <path d="M15 6v12a3 3 0 1 0 3-3H6a3 3 0 1 0 3 3V6a3 3 0 1 0-3 3h12a3 3 0 1 0-3-3" />
          </svg>
          <div className="flex flex-col">
            <span>{displayName}</span>
            <span className="text-sm font-normal text-gray-300">{appName}</span>
          </div>
        </div>
        <div className="relative z-20 mt-auto">
          <blockquote className="space-y-2">
            <p className="text-lg">
              &ldquo;
              {intl.formatMessage({ id: 'page.login.testimonial.quote' })}
              &rdquo;
            </p>
            <footer className="text-sm">
              {intl.formatMessage({ id: 'page.login.testimonial.author' })}
            </footer>
          </blockquote>
        </div>
      </div>

      {/* Right side - Login Form */}
      <div className="flex min-h-screen items-center justify-center p-4 lg:p-8">
        <div className="w-full max-w-sm space-y-6">
          {/* Mobile Brand Header - Only shown on mobile */}
          <div className="flex flex-col items-center space-y-2 text-center lg:hidden">
            <div className="flex items-center space-x-2">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="h-6 w-6"
              >
                <path d="M15 6v12a3 3 0 1 0 3-3H6a3 3 0 1 0 3 3V6a3 3 0 1 0-3 3h12a3 3 0 1 0-3-3" />
              </svg>
              <span className="text-xl font-bold">{displayName}</span>
            </div>
            <span className="text-sm text-muted-foreground">{appName}</span>
          </div>

          <Card className="border-0 shadow-lg lg:border lg:shadow-md">
            <CardHeader className="space-y-1 pb-4 text-center lg:text-left">
              <CardTitle className="text-2xl font-bold tracking-tight">
                {intl.formatMessage({ id: 'page.login.welcome' })}
              </CardTitle>
              <CardDescription>
                {intl.formatMessage({ id: 'page.login.welcomeDescription' })}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Social Login Buttons - Stack on mobile, side by side on tablet+ */}
              <div className="flex flex-col gap-3 sm:grid sm:grid-cols-2">
                <Button variant="outline" className="w-full">
                  <svg
                    className="mr-2 h-4 w-4 flex-shrink-0"
                    viewBox="0 0 24 24"
                  >
                    <path
                      d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                      fill="#4285F4"
                    />
                    <path
                      d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                      fill="#34A853"
                    />
                    <path
                      d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                      fill="#FBBC05"
                    />
                    <path
                      d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                      fill="#EA4335"
                    />
                  </svg>
                  <span className="truncate">
                    {intl.formatMessage({ id: 'page.login.googleSignIn' })}
                  </span>
                </Button>
                <Button variant="outline" className="w-full">
                  <svg
                    className="mr-2 h-4 w-4 flex-shrink-0"
                    viewBox="0 0 24 24"
                  >
                    <path
                      d="M12 0C5.37 0 0 5.37 0 12c0 5.31 3.435 9.795 8.205 11.385c.6.105.825-.255.825-.57c0-.285-.015-1.23-.015-2.235c-3.015.555-3.795-.735-4.035-1.41c-.135-.345-.72-1.41-1.23-1.695c-.42-.225-1.02-.78-.015-.795c.945-.015 1.62.87 1.845 1.23c1.08 1.815 2.805 1.305 3.495.99c.105-.78.42-1.305.765-1.605c-2.67-.3-5.46-1.335-5.46-5.925c0-1.305.465-2.385 1.23-3.225c-.12-.3-.54-1.53.12-3.18c0 0 1.005-.315 3.3 1.23c.96-.27 1.98-.405 3-.405s2.04.135 3 .405c2.295-1.56 3.3-1.23 3.3-1.23c.66 1.65.24 2.88.12 3.18c.765.84 1.23 1.905 1.23 3.225c0 4.605-2.805 5.625-5.475 5.925c.435.375.81 1.095.81 2.22c0 1.605-.015 2.895-.015 3.3c0 .315.225.69.825.57A12.02 12.02 0 0 0 24 12c0-6.63-5.37-12-12-12z"
                      fill="currentColor"
                    />
                  </svg>
                  <span className="truncate">
                    {intl.formatMessage({ id: 'page.login.githubSignIn' })}
                  </span>
                </Button>
              </div>
              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <Separator className="w-full" />
                </div>
                <div className="relative flex justify-center text-xs uppercase">
                  <span className="bg-card px-2 text-muted-foreground">
                    {intl.formatMessage({ id: 'page.login.orContinueWith' })}
                  </span>
                </div>
              </div>
              <LoginFormAlt />
            </CardContent>
            <CardFooter className="flex flex-col items-center p-6 pt-0">
              <div className="text-center text-sm text-muted-foreground">
                {intl.formatMessage({ id: 'page.login.noAccount' })}{' '}
                <Link
                  to="/register"
                  className="text-primary underline-offset-4 transition-colors hover:underline"
                >
                  {intl.formatMessage({ id: 'page.login.signUp' })}
                </Link>
              </div>
            </CardFooter>
          </Card>
        </div>
      </div>
    </div>
  );
}

/**
 * Site Configuration File
 *
 * This file contains all configurable elements of the site that may change between projects.
 * It serves as a central location for site-wide information and settings.
 *
 * Note: Moved from stores to constants to maintain unidirectional architecture.
 * Layout components and other shared modules can safely import from constants.
 */

// Basic Site Information
export const SITE_INFO = {
  // Organization/Company Information
  organization: {
    name: 'Company Name', // Legal entity name
    displayName: 'Brand Name', // Marketing/brand name (if different from legal name)
    description: 'Site description for SEO',
    logo: {
      main: '/assets/images/logo.svg',
      light: '/assets/images/logo-light.svg',
      dark: '/assets/images/logo-dark.svg',
      favicon: '/favicon.ico',
    },
    foundedYear: 2023,
    industry: 'Technology', // e.g., Technology, Healthcare, Education
  },

  // Contact Information
  contact: {
    email: {
      general: '<EMAIL>', // General inquiries
      support: '<EMAIL>', // Customer support
      sales: '<EMAIL>', // Sales team
      careers: '<EMAIL>', // HR/Recruiting
      privacy: '<EMAIL>', // Privacy and data protection
    },
    phone: {
      general: '+****************', // General inquiries
      support: '+****************', // Customer support
      sales: '+****************', // Sales team
      international: '+****************', // International support
      tollfree: '1-************', // Toll-free number
    },
    // Physical office locations
    offices: [
      {
        name: 'New York Office',
        street: '456 W 34th St',
        city: 'New York',
        state: 'NY',
        zip: '10001',
        country: 'United States',
        mapLink:
          'https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3022.6175404114356!2d-73.9856!3d40.7484!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x89c259a4f42178e3%3A0xb52871bf7bead705!2s456%20W%2034th%20St%2C%20New%20York%2C%20NY%2010001!5e0!3m2!1sen!2sus!4v1623858442187!5m2!1sen!2sus',
      },
      {
        name: 'London Office',
        street: '78 Digital Lane',
        city: 'London',
        state: '',
        zip: 'EC1V 9BW',
        country: 'United Kingdom',
        mapLink:
          'https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d2482.7669024390257!2d-0.08658!3d51.5225!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x48761b563a137eb3%3A0xce13f617f934b3a7!2s78%20Old%20St%2C%20London%20EC1V%209HU%2C%20UK!5e0!3m2!1sen!2sus!4v1623859442187!5m2!1sen!2sus',
      },
    ],
    social: {
      twitter: 'https://twitter.com/example',
      facebook: 'https://facebook.com/example',
      linkedin: 'https://linkedin.com/company/example',
      instagram: 'https://instagram.com/example',
      github: 'https://github.com/example',
    },
  },

  // Legal Information
  legal: {
    companyNumber: '*********', // Registration/incorporation number
    vatNumber: 'VAT*********', // Tax/VAT number
    dataProtectionOfficer: '<EMAIL>',
    termsLastUpdated: '2023-01-01', // Date when Terms of Service was last updated
    privacyLastUpdated: '2023-01-01', // Date when Privacy Policy was last updated
    cookiesLastUpdated: '2023-01-01', // Date when Cookie Policy was last updated
    gdprLastUpdated: '2023-01-01', // Date when GDPR Compliance page was last updated
  },

  // Application Information
  application: {
    name: 'Dashboard Portal', // Application name
    version: '1.0.0', // Application version
    environment: 'production', // production, development, staging
    baseUrl: 'https://example.com', // Base URL for the application
    apiUrl: 'https://api.example.com', // Base API URL
    copyright: `© ${new Date().getFullYear()} Company Name. All rights reserved.`,
    supportedLanguages: ['en', 'es', 'fr'], // List of supported languages/locales
    defaultLanguage: 'en',
  },
};

// Page Metadata (for SEO and social sharing)
export const PAGE_METADATA = {
  home: {
    title: 'Dashboard Portal - Home',
    description:
      'Welcome to the Dashboard Portal - manage your data efficiently.',
    keywords: ['dashboard', 'portal', 'management', 'data'],
    ogImage: '/assets/images/og-home.jpg',
  },
  about: {
    title: 'About Us',
    description: 'Learn about our company and mission.',
    keywords: ['about', 'company', 'mission', 'values'],
    ogImage: '/assets/images/og-about.jpg',
  },
  privacy: {
    title: 'Privacy Policy',
    description: 'Our privacy policy details how we handle your data.',
    keywords: ['privacy', 'policy', 'data', 'GDPR'],
    ogImage: '/assets/images/og-privacy.jpg',
  },
  terms: {
    title: 'Terms of Service',
    description: 'Terms and conditions for using our services.',
    keywords: ['terms', 'conditions', 'service', 'agreement'],
    ogImage: '/assets/images/og-terms.jpg',
  },
  contact: {
    title: 'Contact Us',
    description: 'Get in touch with our team for support or inquiries.',
    keywords: ['contact', 'support', 'help', 'inquiry'],
    ogImage: '/assets/images/og-contact.jpg',
  },
  faq: {
    title: 'Frequently Asked Questions',
    description: 'Find answers to common questions about our services.',
    keywords: ['FAQ', 'help', 'questions', 'answers'],
    ogImage: '/assets/images/og-faq.jpg',
  },
  help: {
    title: 'Help Center',
    description:
      'Find comprehensive guides, tutorials and resources to help you use our platform effectively.',
    keywords: [
      'help',
      'support',
      'guides',
      'tutorials',
      'documentation',
      'resources',
    ],
    ogImage: '/assets/images/og-help.jpg',
  },
  cookies: {
    title: 'Cookie Policy',
    description: 'Learn how we use cookies on our website.',
    keywords: ['cookies', 'tracking', 'policy'],
    ogImage: '/assets/images/og-cookies.jpg',
  },
  team: {
    title: 'Our Team',
    description: 'Meet the talented professionals behind our organization.',
    keywords: ['team', 'employees', 'leadership', 'staff'],
    ogImage: '/assets/images/og-team.jpg',
  },
  careers: {
    title: 'Careers',
    description: 'Join our team and explore career opportunities with us.',
    keywords: ['careers', 'jobs', 'employment', 'opportunities'],
    ogImage: '/assets/images/og-careers.jpg',
  },
  jobApplication: {
    title: 'Apply for Position',
    description: 'Submit your application for this position',
    keywords: ['careers', 'application', 'jobs', 'apply'],
    ogImage: '/assets/images/og-job-application.jpg',
  },
  blog: {
    title: 'Blog',
    description: 'Latest news, updates, and insights from our team.',
    keywords: ['blog', 'news', 'articles', 'updates'],
    ogImage: '/assets/images/og-blog.jpg',
  },
  blogDetail: {
    title: 'Blog Post', // Will be dynamic
    description: 'Read our latest insights and updates', // Will be dynamic
    keywords: ['blog', 'article', 'insights'],
    ogImage: '/assets/images/og-blog-detail.jpg',
  },
  helpArticle: {
    title: 'Help Article', // Will be dynamic
    description: 'Get help with our platform', // Will be dynamic
    keywords: ['help', 'support', 'guide', 'tutorial'],
    ogImage: '/assets/images/og-help-article.jpg',
  },
  supportTicket: {
    title: 'Create Support Ticket',
    description: 'Get help from our support team by creating a support ticket',
    keywords: ['support', 'ticket', 'help', 'contact'],
    ogImage: '/assets/images/og-support-ticket.jpg',
  },
  supportArticle: {
    title: 'Support Article', // Will be dynamic
    description: 'Get help with our platform', // Will be dynamic
    keywords: ['support', 'help', 'guide', 'troubleshooting'],
    ogImage: '/assets/images/og-support-article.jpg',
  },
  support: {
    title: 'Support Center',
    description: 'Get help and support for our platform',
    keywords: ['support', 'help', 'contact', 'assistance'],
    ogImage: '/assets/images/og-support.jpg',
  },
  resources: {
    title: 'Resources',
    description:
      'Helpful resources, guides, and documentation for our services.',
    keywords: ['resources', 'guides', 'documentation', 'help'],
    ogImage: '/assets/images/og-resources.jpg',
  },
  security: {
    title: 'Security & Compliance',
    description:
      'Learn about our comprehensive security measures, compliance certifications, and data protection practices.',
    keywords: [
      'security',
      'compliance',
      'privacy',
      'data protection',
      'certifications',
      'SOC2',
      'ISO27001',
      'GDPR',
    ],
    ogImage: '/assets/images/og-security.jpg',
  },
  gdpr: {
    title: 'GDPR Compliance',
    description:
      'Information about our GDPR compliance and data protection measures.',
    keywords: ['GDPR', 'compliance', 'data protection', 'privacy'],
    ogImage: '/assets/images/og-gdpr.jpg',
  },
  notFound: {
    title: 'Page Not Found',
    description: 'The page you are looking for does not exist.',
    keywords: ['404', 'not found', 'error'],
    ogImage: '/assets/images/og-404.jpg',
  },
  serverError: {
    title: 'Server Error',
    description: 'Something went wrong on our server.',
    keywords: ['500', 'server error', 'issue'],
    ogImage: '/assets/images/og-500.jpg',
  },
  maintenance: {
    title: 'Maintenance Mode',
    description:
      'Our site is currently under maintenance. Please check back soon.',
    keywords: ['maintenance', 'downtime', 'updates'],
    ogImage: '/assets/images/og-maintenance.jpg',
  },
  services: {
    title: 'Our Services',
    description:
      'Comprehensive services and solutions we offer to help your business grow.',
    keywords: [
      'services',
      'solutions',
      'offerings',
      'consulting',
      'development',
    ],
    ogImage: '/assets/images/og-services.jpg',
  },
  testimonials: {
    title: 'Customer Testimonials',
    description: 'What our customers say about our services and solutions.',
    keywords: [
      'testimonials',
      'reviews',
      'customers',
      'feedback',
      'success stories',
    ],
    ogImage: '/assets/images/og-testimonials.jpg',
  },
  pricing: {
    title: 'Pricing Plans',
    description:
      'Choose the perfect plan for your needs with transparent pricing and flexible options.',
    keywords: [
      'pricing',
      'plans',
      'subscription',
      'cost',
      'billing',
      'packages',
    ],
    ogImage: '/assets/images/og-pricing.jpg',
  },
  news: {
    title: 'News & Press',
    description:
      'Stay updated with our latest announcements, product updates, and industry insights.',
    keywords: [
      'news',
      'press',
      'announcements',
      'updates',
      'insights',
      'articles',
    ],
    ogImage: '/assets/images/og-news.jpg',
  },
  newsDetail: {
    title: 'News Article', // Will be dynamic
    description: 'Read our latest news and updates', // Will be dynamic
    keywords: ['news', 'article', 'press', 'announcement'],
    ogImage: '/assets/images/og-news-detail.jpg',
  },
  partners: {
    title: 'Our Partners',
    description:
      'Discover our trusted partners who help us deliver exceptional solutions and services.',
    keywords: [
      'partners',
      'partnerships',
      'collaboration',
      'ecosystem',
      'integrations',
    ],
    ogImage: '/assets/images/og-partners.jpg',
  },
  caseStudies: {
    title: 'Case Studies',
    description:
      'Discover how we help businesses achieve remarkable results through innovative solutions and strategic partnerships.',
    keywords: [
      'case studies',
      'success stories',
      'client results',
      'testimonials',
      'solutions',
    ],
    ogImage: '/assets/images/og-case-studies.jpg',
  },
  caseStudyDetail: {
    title: 'Case Study', // Will be dynamic
    description: 'Read about our client success story', // Will be dynamic
    keywords: ['case study', 'success story', 'client', 'results'],
    ogImage: '/assets/images/og-case-study-detail.jpg',
  },
  events: {
    title: 'Events',
    description:
      'Join our upcoming events, workshops, and conferences to learn, network, and grow with industry experts.',
    keywords: ['events', 'conferences', 'workshops', 'webinars', 'networking'],
    ogImage: '/assets/images/og-events.jpg',
  },
  eventDetail: {
    title: 'Event', // Will be dynamic
    description: 'Learn more about this event', // Will be dynamic
    keywords: ['event', 'conference', 'workshop', 'registration'],
    ogImage: '/assets/images/og-event-detail.jpg',
  },
  integrations: {
    title: 'Integrations',
    description:
      'Connect with your favorite tools and services to streamline your workflow and boost productivity.',
    keywords: [
      'integrations',
      'API',
      'connections',
      'tools',
      'services',
      'automation',
      'workflow',
    ],
    ogImage: '/assets/images/og-integrations.jpg',
  },
  changelog: {
    title: 'Product Changelog',
    description:
      'Stay up to date with the latest product updates, features, and improvements.',
    keywords: [
      'changelog',
      'updates',
      'releases',
      'versions',
      'features',
      'improvements',
      'fixes',
    ],
    ogImage: '/assets/images/og-changelog.jpg',
  },
  resourceDetail: {
    title: 'Resource', // Will be dynamic
    description: 'Learn more about this resource', // Will be dynamic
    keywords: ['resource', 'guide', 'documentation', 'help'],
    ogImage: '/assets/images/og-resource-detail.jpg',
  },
};

// Feature Flags
export const FEATURES = {
  enableDarkMode: true,
  enableMultiLanguage: true,
  enableNotifications: true,
  enableUserRoles: true,
  enableAnalytics: true,
  enableTwoFactorAuth: true,
  enableSocialLogin: true,
  enableChatSupport: false,
  maintenanceMode: false,
};

// External Services Configuration
export const SERVICES = {
  analytics: {
    googleAnalyticsId: 'UA-XXXXXXXXX-X',
    matomoUrl: 'https://analytics.example.com/',
    matomoSiteId: 1,
  },
  payments: {
    stripePublicKey: 'pk_test_XXXXXXXXXXXXXXXXXXXXXXXX',
    paypalClientId: 'XXXXXXXXXXXXXXXXXXXX',
  },
  maps: {
    googleMapsApiKey: 'XXXXXXXXXXXXXXXXXXXX',
  },
  recaptcha: {
    siteKey: 'XXXXXXXXXXXXXXXXXXXX',
  },
};

// Content Configuration
export const CONTENT = {
  // Footer Links
  footerLinks: {
    company: [
      { name: 'About Us', path: '/about' },
      { name: 'Careers', path: '/careers' },
      { name: 'Blog', path: '/blog' },
      { name: 'Contact', path: '/contact' },
      { name: 'Team', path: '/team' },
      { name: 'Services', path: '/services' },
      { name: 'Pricing', path: '/pricing' },
      { name: 'Testimonials', path: '/testimonials' },
      { name: 'Case Studies', path: '/case-studies' },
      { name: 'Events', path: '/events' },
      { name: 'News', path: '/news' },
      { name: 'Partners', path: '/partners' },
      { name: 'Integrations', path: '/integrations' },
      { name: 'Changelog', path: '/changelog' },
    ],
    support: [
      { name: 'Help Center', path: '/help' },
      { name: 'FAQ', path: '/faq' },
      { name: 'Support', path: '/support' },
      { name: 'Resources', path: '/resources' },
      { name: 'Security', path: '/security' },
    ],
    legal: [
      { name: 'Terms of Service', path: '/terms' },
      { name: 'Privacy Policy', path: '/privacy' },
      { name: 'Cookie Policy', path: '/cookies' },
      { name: 'GDPR', path: '/gdpr' },
    ],
  },

  // Cookie Consent Text
  cookieConsent: {
    message:
      'This website uses cookies to ensure you get the best experience on our website.',
    acceptText: 'Accept Cookies',
    declineText: 'Decline',
    learnMoreText: 'Learn More',
    learnMoreLink: '/cookies',
  },
};

// Default export for easy importing
const siteConfig = {
  SITE_INFO,
  PAGE_METADATA,
  FEATURES,
  SERVICES,
  CONTENT,
};

export default siteConfig;

/**
 * Error Boundary Configuration
 *
 * Centralized configuration for error boundary behavior across the application
 */

export interface ErrorBoundaryGlobalConfig {
  // Global settings
  enableReporting: boolean;
  developmentMode: boolean;
  showErrorDetails: boolean;

  // Retry settings
  defaultRetryAttempts: number;
  defaultRetryDelay: number;
  maxRetryDelay: number;

  // API error boundary settings
  apiHealthCheckUrl?: string;
  apiHealthCheckTimeout: number;
  apiAutoRetryDelay: number;
  showOfflineMessage: boolean;

  // Async error boundary settings
  asyncMaxRetries: number;
  asyncRetryDelay: number;
  asyncShowProgress: boolean;

  // Form error boundary settings
  formShowResetButton: boolean;
  formExtractSensitiveData: boolean;

  // Data error boundary settings
  dataShowFallbackOptions: boolean;
  dataCacheTimeout: number;

  // UI settings
  errorBoundaryLevel: 'critical' | 'page' | 'component';
  consistentErrorUI: boolean;

  // Performance settings
  errorReportingThrottle: number;
  maxErrorReportsPerSession: number;
}

export const defaultErrorBoundaryConfig: ErrorBoundaryGlobalConfig = {
  // Global settings
  enableReporting: import.meta.env.PROD,
  developmentMode: import.meta.env.DEV,
  showErrorDetails: import.meta.env.DEV,

  // Retry settings
  defaultRetryAttempts: 3,
  defaultRetryDelay: 1000,
  maxRetryDelay: 30000,

  // API error boundary settings
  apiHealthCheckUrl: import.meta.env.VITE_API_URL
    ? `${import.meta.env.VITE_API_URL}/health`
    : undefined,
  apiHealthCheckTimeout: 5000,
  apiAutoRetryDelay: 30000,
  showOfflineMessage: true,

  // Async error boundary settings
  asyncMaxRetries: 3,
  asyncRetryDelay: 1000,
  asyncShowProgress: true,

  // Form error boundary settings
  formShowResetButton: true,
  formExtractSensitiveData: false,

  // Data error boundary settings
  dataShowFallbackOptions: true,
  dataCacheTimeout: 5 * 60 * 1000, // 5 minutes

  // UI settings
  errorBoundaryLevel: 'component',
  consistentErrorUI: true,

  // Performance settings
  errorReportingThrottle: 1000, // 1 second between reports
  maxErrorReportsPerSession: 50,
};

/**
 * Get error boundary configuration with environment overrides
 */
export function getErrorBoundaryConfig(): ErrorBoundaryGlobalConfig {
  return {
    ...defaultErrorBoundaryConfig,
    // Environment-specific overrides can be added here
    ...(import.meta.env.VITE_ERROR_BOUNDARY_CONFIG
      ? JSON.parse(import.meta.env.VITE_ERROR_BOUNDARY_CONFIG)
      : {}),
  };
}

/**
 * Error boundary type mappings for consistent configuration
 */
export const ERROR_BOUNDARY_TYPES = {
  CRITICAL: 'critical',
  PAGE: 'page',
  COMPONENT: 'component',
  API: 'api',
  ASYNC: 'async',
  FORM: 'form',
  DATA: 'data',
  ROUTE: 'route',
} as const;

export type ErrorBoundaryType =
  (typeof ERROR_BOUNDARY_TYPES)[keyof typeof ERROR_BOUNDARY_TYPES];

/**
 * Error boundary placement patterns for consistent hierarchy
 */
export const ERROR_BOUNDARY_PATTERNS = {
  APP_LEVEL: {
    type: ERROR_BOUNDARY_TYPES.CRITICAL,
    level: 'critical' as const,
    showDetails: false,
    enableReporting: true,
  },
  ROUTE_LEVEL: {
    type: ERROR_BOUNDARY_TYPES.ROUTE,
    level: 'page' as const,
    showDetails: false,
    enableReporting: true,
  },
  LAYOUT_LEVEL: {
    type: ERROR_BOUNDARY_TYPES.PAGE,
    level: 'page' as const,
    showDetails: false,
    enableReporting: true,
  },
  PAGE_LEVEL: {
    type: ERROR_BOUNDARY_TYPES.PAGE,
    level: 'page' as const,
    showDetails: false,
    enableReporting: true,
  },
  COMPONENT_LEVEL: {
    type: ERROR_BOUNDARY_TYPES.COMPONENT,
    level: 'component' as const,
    showDetails: import.meta.env.DEV,
    enableReporting: true,
  },
  API_LEVEL: {
    type: ERROR_BOUNDARY_TYPES.API,
    showOfflineMessage: true,
    enableReporting: true,
  },
  FORM_LEVEL: {
    type: ERROR_BOUNDARY_TYPES.FORM,
    showResetButton: true,
    enableReporting: true,
  },
  ASYNC_LEVEL: {
    type: ERROR_BOUNDARY_TYPES.ASYNC,
    showProgress: true,
    enableReporting: true,
  },
  DATA_LEVEL: {
    type: ERROR_BOUNDARY_TYPES.DATA,
    showFallbackOptions: true,
    enableReporting: true,
  },
} as const;

export default getErrorBoundaryConfig;

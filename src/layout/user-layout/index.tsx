import { Outlet } from 'react-router-dom';
import { SidebarInset, SidebarProvider } from '@/components/ui/shadcn/sidebar';
import { UserSidebar } from '@/components/layout/user-sidebar';
import { Toaster } from '@/components/ui/shadcn/toaster';
import { ErrorBoundary } from '@/components/base/boundary/error-boundary';

/**
 * UserLayout Component
 *
 * Layout component for user-related pages (profile, security, settings, notifications)
 * Provides a specialized sidebar for user-related navigation.
 *
 * This component follows the same pattern as MainLayout but uses a specialized UserSidebar.
 */
const UserLayout = () => {
  return (
    <SidebarProvider>
      <UserSidebar />
      <SidebarInset className="pt-0">
        <div className="p-6">
          <ErrorBoundary
            level="page"
            showDetails={import.meta.env.MODE === 'development'}
          >
            <Outlet />
          </ErrorBoundary>
        </div>
      </SidebarInset>
      <Toaster />
    </SidebarProvider>
  );
};

export default UserLayout;

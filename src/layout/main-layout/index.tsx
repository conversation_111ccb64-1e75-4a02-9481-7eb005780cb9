import { Separator } from '@/components/ui/shadcn/separator';
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from '@/components/ui/shadcn/sidebar';
import { Outlet } from 'react-router-dom';
import { AppSidebar } from '@/components/layout/app-sidebar';
import { Toaster } from '@/components/ui/shadcn/toaster';
import { NavBreadcrumb } from '@/components/layout/nav-breadcrumb';
import { ErrorBoundary } from '@/components/base/boundary/error-boundary';

const MainLayout = () => {
  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12">
          <div className="flex items-center gap-2 px-4">
            <SidebarTrigger className="-ml-1" />
            <Separator orientation="vertical" className="mr-2 h-4" />
            <NavBreadcrumb />
          </div>
        </header>
        <ErrorBoundary
          level="page"
          showDetails={import.meta.env.MODE === 'development'}
        >
          <Outlet />
        </ErrorBoundary>
      </SidebarInset>
      <Toaster />
    </SidebarProvider>
  );
};

export default MainLayout;

import { Outlet } from 'react-router-dom';
import { ErrorBoundary } from '@/components/base/boundary/error-boundary';

// ==============================|| MINIMAL LAYOUT ||============================== //

const MinimalLayout = () => (
  <>
    <ErrorBoundary
      level="page"
      showDetails={import.meta.env.MODE === 'development'}
    >
      <Outlet />
    </ErrorBoundary>
  </>
);

export default MinimalLayout;

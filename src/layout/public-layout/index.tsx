import React from 'react';
import { Outlet } from 'react-router-dom';
import NavbarPublic from '@/components/layout/navbar-public';
import FooterPublic from '@/components/layout/footer-public';
import { ErrorBoundary } from '@/components/base/boundary/error-boundary';

/**
 * PublicLayout Component
 *
 * @description Provides a consistent layout for all public pages including header navigation,
 * main content area, and footer with company links. Used for pages that don't require authentication.
 *
 * @returns {React.JSX.Element} The PublicLayout component
 */
const PublicLayout = (): React.JSX.Element => {
  return (
    <div className="flex min-h-screen flex-col">
      {/* Header Navigation */}
      <NavbarPublic />

      {/* Main Content */}
      <main className="flex-grow">
        <ErrorBoundary
          level="page"
          showDetails={import.meta.env.MODE === 'development'}
        >
          <Outlet />
        </ErrorBoundary>
      </main>

      {/* Footer */}
      <FooterPublic />
    </div>
  );
};

export default PublicLayout;

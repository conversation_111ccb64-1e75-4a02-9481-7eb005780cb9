import { useState, useEffect } from 'react';
import useAuth from './use-auth';
import { getUserProfile } from '@/services/user';
import type { UserProfileData } from '@/types/user';

/**
 * Hook to get comprehensive user data
 * Combines auth context user info with detailed profile data
 */
export const useUserData = () => {
  const { user: authUser, isLoggedIn } = useAuth();
  const [profileData, setProfileData] = useState<UserProfileData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchUserProfile = async () => {
      if (!isLoggedIn || !authUser) {
        setProfileData(null);
        return;
      }

      try {
        setIsLoading(true);
        setError(null);
        const profile = await getUserProfile();
        setProfileData(profile);
      } catch (err) {
        console.error('Failed to fetch user profile:', err);
        setError('Failed to load user profile');
      } finally {
        setIsLoading(false);
      }
    };

    fetchUserProfile();
  }, [isLoggedIn, authUser]);

  return {
    authUser,
    profileData,
    isLoggedIn,
    isLoading,
    error,
    // Refresh function to manually reload profile data
    refreshProfile: async () => {
      if (isLoggedIn) {
        try {
          setIsLoading(true);
          const profile = await getUserProfile();
          setProfileData(profile);
        } catch {
          setError('Failed to refresh profile');
        } finally {
          setIsLoading(false);
        }
      }
    },
  };
};

export default useUserData;

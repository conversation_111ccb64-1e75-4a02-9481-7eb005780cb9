/**
 * Simple and Stable API Data Hooks
 *
 * Rewritten with a focus on simplicity and stability to avoid infinite loops
 */

import { useState, useEffect, useCallback, useRef } from 'react';

interface UseApiDataOptions<T> {
  initialData?: T;
  enabled?: boolean;
}

interface UseApiDataResult<T> {
  data: T | undefined;
  loading: boolean;
  error: Error | null;
  refetch: () => void;
  isStale: boolean;
}

/**
 * Simple API data fetching hook - ZERO dependencies to prevent infinite loops
 */
export function useApiData<T>(
  fetcher: () => Promise<T>,
  options: UseApiDataOptions<T> = {}
): UseApiDataResult<T> {
  const { initialData, enabled = true } = options;

  const [data, setData] = useState<T | undefined>(initialData);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [isStale, setIsStale] = useState(false);

  const fetcherRef = useRef(fetcher);
  const enabledRef = useRef(enabled);

  // Update refs without causing re-renders
  fetcherRef.current = fetcher;
  enabledRef.current = enabled;

  const fetchData = useCallback(async () => {
    if (!enabledRef.current) return;

    try {
      setLoading(true);
      setError(null);

      const result = await fetcherRef.current();

      setData(result);
      setIsStale(false);
    } catch (err) {
      setError(err as Error);
    } finally {
      setLoading(false);
    }
  }, []); // NO dependencies!

  const refetch = useCallback(() => {
    setIsStale(true);
    fetchData();
  }, [fetchData]);

  useEffect(() => {
    fetchData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // NO dependencies to prevent infinite loops!

  return {
    data,
    loading,
    error,
    refetch,
    isStale,
  };
}

interface UseMutationOptions<TData, TVariables> {
  onSuccess?: (_data: TData, _variables: TVariables) => void;
  onError?: (_error: Error, _variables: TVariables) => void;
}

interface UseMutationResult<TData, TVariables> {
  mutate: (_variables: TVariables) => Promise<TData>;
  data: TData | undefined;
  loading: boolean;
  error: Error | null;
  reset: () => void;
}

/**
 * Simple mutation hook - ZERO dependencies to prevent infinite loops
 */
export function useMutation<TData, TVariables>(
  mutationFn: (_variables: TVariables) => Promise<TData>,
  options: UseMutationOptions<TData, TVariables> = {}
): UseMutationResult<TData, TVariables> {
  const { onSuccess, onError } = options;

  const [data, setData] = useState<TData | undefined>();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const mutationFnRef = useRef(mutationFn);
  const onSuccessRef = useRef(onSuccess);
  const onErrorRef = useRef(onError);

  // Update refs without causing re-renders
  mutationFnRef.current = mutationFn;
  onSuccessRef.current = onSuccess;
  onErrorRef.current = onError;

  const mutate = useCallback(
    async (variables: TVariables): Promise<TData> => {
      try {
        setLoading(true);
        setError(null);

        const result = await mutationFnRef.current(variables);

        setData(result);
        onSuccessRef.current?.(result, variables);

        return result;
      } catch (err) {
        const error = err as Error;
        setError(error);
        onErrorRef.current?.(error, variables);
        throw error;
      } finally {
        setLoading(false);
      }
    },
    [] // NO dependencies!
  );

  const reset = useCallback(() => {
    setData(undefined);
    setError(null);
    setLoading(false);
  }, []);

  return {
    mutate,
    data,
    loading,
    error,
    reset,
  };
}

interface UseInfiniteQueryOptions<T> {
  getNextPageParam: (_lastPage: T, _allPages: T[]) => any;
  enabled?: boolean;
}

interface UseInfiniteQueryResult<T> {
  data: T[];
  loading: boolean;
  error: Error | null;
  hasNextPage: boolean;
  isFetchingNextPage: boolean;
  fetchNextPage: () => void;
  refetch: () => void;
}

/**
 * Simple infinite query hook - ZERO dependencies to prevent infinite loops
 */
export function useInfiniteQuery<T>(
  fetcher: (_pageParam: any) => Promise<T>,
  options: UseInfiniteQueryOptions<T>
): UseInfiniteQueryResult<T> {
  const { getNextPageParam, enabled = true } = options;

  const [data, setData] = useState<T[]>([]);
  const [loading, setLoading] = useState(false);
  const [isFetchingNextPage, setIsFetchingNextPage] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [hasNextPage, setHasNextPage] = useState(true);
  const [nextPageParam, setNextPageParam] = useState<any>(undefined);

  const fetcherRef = useRef(fetcher);
  const getNextPageParamRef = useRef(getNextPageParam);
  const enabledRef = useRef(enabled);

  // Update refs without causing re-renders
  fetcherRef.current = fetcher;
  getNextPageParamRef.current = getNextPageParam;
  enabledRef.current = enabled;

  const fetchPage = useCallback(
    async (pageParam: any, isNextPage = false) => {
      if (!enabledRef.current) return;

      try {
        if (isNextPage) {
          setIsFetchingNextPage(true);
        } else {
          setLoading(true);
        }

        setError(null);
        const result = await fetcherRef.current(pageParam);

        setData(prevData => {
          const newData = isNextPage ? [...prevData, result] : [result];
          const nextParam = getNextPageParamRef.current(result, newData);
          setNextPageParam(nextParam);
          setHasNextPage(!!nextParam);
          return newData;
        });
      } catch (err) {
        setError(err as Error);
        setHasNextPage(false);
      } finally {
        setLoading(false);
        setIsFetchingNextPage(false);
      }
    },
    [] // NO dependencies!
  );

  const fetchNextPage = useCallback(() => {
    if (hasNextPage && !isFetchingNextPage) {
      fetchPage(nextPageParam, true);
    }
  }, [hasNextPage, isFetchingNextPage, nextPageParam, fetchPage]);

  const refetch = useCallback(() => {
    setData([]);
    setNextPageParam(undefined);
    setHasNextPage(true);
    fetchPage(undefined, false);
  }, [fetchPage]);

  useEffect(() => {
    fetchPage(undefined, false);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // NO dependencies to prevent infinite loops!

  return {
    data,
    loading,
    error,
    hasNextPage,
    isFetchingNextPage,
    fetchNextPage,
    refetch,
  };
}

// Removed complex optimistic mutation hook to keep things simple

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer utilities {
  .line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }

  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }

  .line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;

    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;

    --primary: 220 85% 57%;
    --primary-foreground: 0 0% 100%;

    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;

    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;

    --accent: 220 85% 97%;
    --accent-foreground: 220 85% 45%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 220 85% 57%;

    --radius: 0.5rem;

    /* Chart Colors */
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;

    /* Sidebar Colors */
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 220 85% 57%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 220 85% 57%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 220 85% 57%;
  }

  .dark {
    /* GeNieGO Brand Colors - Dark Mode (Greyish Dark) */
    --background: 240 5% 20%; /* More greyish background */
    --foreground: 0 0% 92%; /* Softer white text */
    --card: 240 5% 24%; /* Greyish cards */
    --card-foreground: 0 0% 92%;
    --popover: 240 5% 24%; /* Match card color */
    --popover-foreground: 0 0% 92%;
    --primary: 217 91% 65%; /* Lighter, more vibrant blue for dark mode */
    --primary-foreground: 0 0% 100%; /* White text for primary buttons */
    --secondary: 240 4% 30%; /* Lighter greyish secondary */
    --secondary-foreground: 0 0% 88%;
    --muted: 240 4% 28%; /* Greyish muted background */
    --muted-foreground: 240 3% 70%; /* Readable muted text */
    --accent: 240 4% 30%; /* Match secondary */
    --accent-foreground: 217 91% 65%;
    --destructive: 0 62.8% 45%; /* Balanced red for dark mode */
    --destructive-foreground: 0 0% 92%;
    --border: 240 4% 35%; /* Visible greyish borders */
    --input: 240 4% 35%; /* Input field border - same as border for consistency */
    --ring: 217 91% 65%;

    /* Chart Colors - Dark */
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;

    /* Sidebar Colors - GeNieGO Dark Theme (Greyish) */
    --sidebar-background: 240 5% 18%; /* Slightly darker greyish sidebar */
    --sidebar-foreground: 0 0% 88%; /* Readable sidebar text */
    --sidebar-primary: 217 91% 65%; /* Lighter blue for dark mode */
    --sidebar-primary-foreground: 0 0% 100%; /* White text for sidebar primary */
    --sidebar-accent: 217 91% 65%; /* Lighter blue for dark mode */
    --sidebar-accent-foreground: 0 0% 100%; /* White text for sidebar accent */
    --sidebar-border: 240 4% 32%; /* Greyish sidebar borders */
    --sidebar-ring: 217 91% 65%;
  }

  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings:
      'rlig' 1,
      'calt' 1;
  }
  .theme {
    --animate-marquee: marquee var(--duration) infinite linear;
    --animate-marquee-vertical: marquee-vertical var(--duration) linear infinite;
    --animate-gradient: gradient 8s linear infinite;
    --animate-shiny-text: shiny-text 8s infinite;
    --animate-meteor: meteor 5s linear infinite;
  }
}

/* Add these keyframes animations */
@keyframes progressBar {
  0% {
    width: 0%;
  }
  50% {
    width: 70%;
  }
  100% {
    width: 100%;
  }
}

@keyframes slideRight {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(100%);
  }
}

@theme inline {
  @keyframes marquee {
    from {
      transform: translateX(0);
    }
    to {
      transform: translateX(calc(-100% - var(--gap)));
    }
  }
  @keyframes marquee-vertical {
    from {
      transform: translateY(0);
    }
    to {
      transform: translateY(calc(-100% - var(--gap)));
    }
  }
  @keyframes gradient {
    to {
      background-position: var(--bg-size, 300%) 0;
    }
  }
  @keyframes shiny-text {
    0%,
    90%,
    100% {
      background-position: calc(-100% - var(--shiny-width)) 0;
    }
    30%,
    60% {
      background-position: calc(100% + var(--shiny-width)) 0;
    }
  }
  @keyframes meteor {
    0% {
      transform: rotate(var(--angle)) translateX(0);
      opacity: 1;
    }
    70% {
      opacity: 1;
    }
    100% {
      transform: rotate(var(--angle)) translateX(-500px);
      opacity: 0;
    }
  }
}

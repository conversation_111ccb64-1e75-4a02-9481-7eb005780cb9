import { StrictMode } from 'react';
import ReactDOM from 'react-dom/client';
import './index.css';
import App from './App.tsx';
import '@/styles/animations.css';

// Error reporting
import { reportError } from '@/utils/error-reporting';

// Global error handlers for uncaught errors
if (import.meta.env.MODE === 'production') {
  // Handle uncaught errors
  window.addEventListener('error', event => {
    reportError(event.error || new Error(event.message), {
      extra: {
        source: event.filename,
        lineno: event.lineno,
        colno: event.colno,
      },
      tags: {
        errorType: 'uncaught',
        errorLevel: 'critical',
      },
    });
  });

  // Handle unhandled promise rejections
  window.addEventListener('unhandledrejection', event => {
    const error =
      event.reason instanceof Error
        ? event.reason
        : new Error(String(event.reason));

    reportError(error, {
      tags: {
        errorType: 'unhandledRejection',
        errorLevel: 'critical',
      },
    });
  });
}

ReactDOM.createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <App />
  </StrictMode>
);

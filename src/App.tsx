import { BrowserRouter } from 'react-router-dom';

// routes
import Routes from '@/routes';

// project imports
import './App.css';
import Locales from './components/base/locales';

// contexts
import { ConfigProvider } from '@/contexts';
import { JW<PERSON>rovider as AuthProvider } from '@/contexts/auth/JWTContext';
import { ErrorBoundaryProvider } from '@/contexts/error-boundary/ErrorBoundaryContext';
import NavigationScroll from './layout/navigation-scroll';

// error boundaries
import { ErrorBoundary } from '@/components/base/boundary/error-boundary';

function App() {
  return (
    <ErrorBoundary
      level="critical"
      showDetails={import.meta.env.MODE === 'development'}
    >
      <ErrorBoundaryProvider>
        <BrowserRouter
          future={{
            v7_relativeSplatPath: true,
            v7_startTransition: true,
          }}
        >
          <AuthProvider>
            <ConfigProvider>
              <Locales>
                <NavigationScroll>
                  <Routes />
                </NavigationScroll>
              </Locales>
            </ConfigProvider>
          </AuthProvider>
        </BrowserRouter>
      </ErrorBoundaryProvider>
    </ErrorBoundary>
  );
}

export default App;

import { createContext, ReactNode, useEffect } from 'react';

// project import
import defaultConfig from '@/config';
import useLocalStorage from '@/hooks/use-localStorage';

// types
import { CustomizationProps } from '@/types/config';

// initial state
const initialState: CustomizationProps = {
  ...defaultConfig,
  fontSize: 'default',
  onChangeFontSize: () => {},
  onChangeLayout: () => {},
  onChangeDrawer: () => {},
  onChangeTheme: () => {},
  onChangePresetColor: () => {},
  onChangeLocale: () => {},
  onChangeRTL: () => {},
  onChangeContainer: () => {},
  onChangeFontFamily: () => {},
  onChangeBorderRadius: () => {},
  onChangeOutlinedField: () => {},
  onReset: () => {},
};

// ==============================|| CONFIG CONTEXT & PROVIDER ||============================== //

const ConfigContext = createContext(initialState);

type ConfigProviderProps = {
  children: ReactNode;
};

function ConfigProvider({ children }: ConfigProviderProps) {
  const [config, setConfig] = useLocalStorage('berry-config-ts', {
    layout: initialState.layout,
    drawerType: initialState.drawerType,
    fontFamily: initialState.fontFamily,
    borderRadius: initialState.borderRadius,
    outlinedFilled: initialState.outlinedFilled,
    theme: initialState.theme,
    presetColor: initialState.presetColor,
    locale: initialState.locale,
    rtlLayout: initialState.rtlLayout,
  });

  // Clean up old theme-related localStorage keys
  useEffect(() => {
    // Remove old theme keys that might conflict with unified config
    const oldKeys = ['base-theme', 'sso-theme', 'theme'];
    oldKeys.forEach(key => {
      if (key !== 'theme' || !localStorage.getItem('berry-config-ts')) {
        // Only remove standalone 'theme' key if berry-config-ts doesn't exist
        if (key === 'theme' && localStorage.getItem('berry-config-ts')) return;
        localStorage.removeItem(key);
      }
    });
  }, []);

  // Add this useEffect to sync theme with DOM
  useEffect(() => {
    const root = window.document.documentElement;
    root.classList.remove('light', 'dark');
    root.classList.add(config.theme);
  }, [config.theme]);

  useEffect(() => {
    const root = window.document.documentElement;
    const fontSizes: Record<'small' | 'default' | 'large', string> = {
      small: '14px',
      default: '16px',
      large: '18px',
    };
    const fontSize = fontSizes[config.fontSize as keyof typeof fontSizes];
    root.style.fontSize = fontSize;
  }, [config.fontSize]);

  const onChangeLayout = (layout: string) => {
    setConfig({
      ...config,
      layout,
    });
  };

  const onChangeDrawer = (drawerType: string) => {
    setConfig({
      ...config,
      drawerType,
    });
  };

  const onChangeTheme = (theme: 'light' | 'dark') => {
    setConfig({
      ...config,
      theme,
    });
  };

  const onChangePresetColor = (presetColor: string) => {
    setConfig({
      ...config,
      presetColor,
    });
  };

  const onChangeLocale = (locale: string) => {
    setConfig({
      ...config,
      locale,
    });
  };

  const onChangeRTL = (rtlLayout: boolean) => {
    setConfig({
      ...config,
      rtlLayout,
    });
  };

  const onChangeContainer = (container: boolean) => {
    setConfig({
      ...config,
      container,
    });
  };

  const onChangeFontFamily = (fontFamily: string) => {
    setConfig({
      ...config,
      fontFamily,
    });
  };

  const onChangeBorderRadius = (_event: Event, newValue: number | number[]) => {
    setConfig({
      ...config,
      borderRadius: newValue as number,
    });
  };

  const onChangeOutlinedField = (outlinedFilled: boolean) => {
    setConfig({
      ...config,
      outlinedFilled,
    });
  };

  const onChangeFontSize = (fontSize: 'small' | 'default' | 'large') => {
    setConfig({
      ...config,
      fontSize,
    });
  };

  const onReset = () => {
    setConfig({ ...defaultConfig });
  };

  return (
    <ConfigContext.Provider
      value={{
        ...config,
        onChangeLayout,
        onChangeDrawer,
        onChangeTheme,
        onChangePresetColor,
        onChangeLocale,
        onChangeRTL,
        onChangeContainer,
        onChangeFontFamily,
        onChangeBorderRadius,
        onChangeOutlinedField,
        onChangeFontSize,
        onReset,
      }}
    >
      {children}
    </ConfigContext.Provider>
  );
}

export { ConfigProvider, ConfigContext };

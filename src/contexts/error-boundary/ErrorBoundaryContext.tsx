/**
 * Error Boundary Context Provider
 *
 * Provides global configuration and context for error boundaries throughout the application
 */

import {
  createContext,
  useContext,
  ReactNode,
  useState,
  useCallback,
} from 'react';
import { ErrorInfo } from 'react';
import { ErrorReporter, ErrorContext } from '@/utils/error-reporting';

interface ErrorBoundaryConfig {
  enableReporting: boolean;
  developmentMode: boolean;
  retryAttempts: number;
  autoRetryDelay: number;
  showErrorDetails: boolean;
  reportingService?: (
    _error: Error,
    _errorInfo: ErrorInfo,
    _context?: ErrorContext
  ) => void;
}

interface ErrorBoundaryContextValue {
  config: ErrorBoundaryConfig;
  updateConfig: (_updates: Partial<ErrorBoundaryConfig>) => void;
  reportError: (
    _error: Error,
    _errorInfo: ErrorInfo,
    _boundaryType: string,
    _context?: ErrorContext
  ) => void;
  getErrorId: () => string;
}

const defaultConfig: ErrorBoundaryConfig = {
  enableReporting: import.meta.env.MODE === 'production',
  developmentMode: import.meta.env.MODE === 'development',
  retryAttempts: 3,
  autoRetryDelay: 1000,
  showErrorDetails: import.meta.env.MODE === 'development',
};

const ErrorBoundaryContext = createContext<
  ErrorBoundaryContextValue | undefined
>(undefined);

interface ErrorBoundaryProviderProps {
  children: ReactNode;
  config?: Partial<ErrorBoundaryConfig>;
}

export function ErrorBoundaryProvider({
  children,
  config: initialConfig,
}: ErrorBoundaryProviderProps) {
  const [config, setConfig] = useState<ErrorBoundaryConfig>({
    ...defaultConfig,
    ...initialConfig,
  });

  const updateConfig = useCallback((_updates: Partial<ErrorBoundaryConfig>) => {
    setConfig(prev => ({ ...prev, ..._updates }));
  }, []);

  const reportError = useCallback(
    (
      _error: Error,
      _errorInfo: ErrorInfo,
      _boundaryType: string,
      _context?: ErrorContext
    ) => {
      if (!config.enableReporting) return;

      // Use custom reporting service if provided
      if (config.reportingService) {
        config.reportingService(_error, _errorInfo, _context);
        return;
      }

      // Use default error reporter
      ErrorReporter.reportComponentError(
        _error,
        _errorInfo.componentStack || 'Unknown component stack',
        {
          ..._context,
          tags: {
            ..._context?.tags,
            boundaryType: _boundaryType,
            errorType: 'boundary',
          },
          extra: {
            ..._context?.extra,
            componentStack: _errorInfo.componentStack,
            retryAttempts: config.retryAttempts,
            autoRetryDelay: config.autoRetryDelay,
          },
        }
      );
    },
    [config]
  );

  const getErrorId = useCallback(() => {
    return `boundary_error_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
  }, []);

  const value: ErrorBoundaryContextValue = {
    config,
    updateConfig,
    reportError,
    getErrorId,
  };

  return (
    <ErrorBoundaryContext.Provider value={value}>
      {children}
    </ErrorBoundaryContext.Provider>
  );
}

export function useErrorBoundaryContext(): ErrorBoundaryContextValue {
  const context = useContext(ErrorBoundaryContext);
  if (!context) {
    throw new Error(
      'useErrorBoundaryContext must be used within an ErrorBoundaryProvider'
    );
  }
  return context;
}

export default ErrorBoundaryContext;

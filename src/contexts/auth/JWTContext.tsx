import React, { createContext, useEffect, useReducer } from 'react';

// third-party
import { Chance } from 'chance';
import jwtDecode from 'jwt-decode';

// reducer - state management
import { LOGIN, LOGOUT } from '@/stores/actions';
import accountReducer from '@/stores/account-reducer';

// project imports
import { Loader } from '@/components/base/loader';
import axios from '@/utils/api/axios';

// types
import { KeyedObject } from '@/types';
import { InitialLoginContextProps, JWTContextType } from '@/types/auth';
import { UserProfile } from '@/types/user-profile';

const chance = new Chance();

// constant// constant
const initialState: InitialLoginContextProps = {
  isLoggedIn: false,
  isInitialized: false,
  user: null,
};

const verifyToken: (_serviceToken: string) => boolean = _serviceToken => {
  if (!_serviceToken) {
    return false;
  }
  const decoded: KeyedObject = jwtDecode(_serviceToken);
  /**
   * Property 'exp' does not exist on type '<T = unknown>(token: string, options?: JwtDecodeOptions | undefined) => T'.
   */
  return decoded.exp > Date.now() / 1000;
};

const setSession = (serviceToken?: string | null) => {
  if (serviceToken) {
    localStorage.setItem('serviceToken', serviceToken);
    axios.defaults.headers.common.Authorization = `Bearer ${serviceToken}`;
  } else {
    localStorage.removeItem('serviceToken');
    delete axios.defaults.headers.common.Authorization;
  }
};

// ==============================|| JWT CONTEXT & PROVIDER ||============================== //
const JWTContext = createContext<JWTContextType | null>(null);

export const JWTProvider = ({ children }: { children: React.ReactElement }) => {
  const [state, dispatch] = useReducer(accountReducer, initialState);

  useEffect(() => {
    const init = async () => {
      try {
        const serviceToken = window.localStorage.getItem('serviceToken');
        if (serviceToken && verifyToken(serviceToken)) {
          setSession(serviceToken);

          // Check if we're on a public route - if so, skip the /me call
          const isPublicRoute =
            window.location.pathname.startsWith('/') &&
            !window.location.pathname.startsWith('/dashboard') &&
            !window.location.pathname.startsWith('/admin') &&
            !window.location.pathname.startsWith('/user');

          if (isPublicRoute) {
            // For public routes, just set the session but don't fetch user data
            dispatch({
              type: LOGIN,
              payload: {
                isLoggedIn: true,
                user: null, // Will be fetched when needed
              },
            });
          } else {
            try {
              const response = await axios.get('/api/account/me');
              const { user } = response.data;
              dispatch({
                type: LOGIN,
                payload: {
                  isLoggedIn: true,
                  user,
                },
              });
            } catch (apiError) {
              // If /me API fails, just logout silently (token might be invalid)
              console.warn(
                'Failed to fetch user profile, logging out:',
                apiError
              );
              setSession(null);
              dispatch({
                type: LOGOUT,
              });
            }
          }
        } else {
          dispatch({
            type: LOGOUT,
          });
        }
      } catch (err) {
        console.error('Auth initialization error:', err);
        dispatch({
          type: LOGOUT,
        });
      }
    };

    init();
  }, []);

  const login = async (email: string, password: string) => {
    const response = await axios.post('/api/auth/login', { email, password });
    const { access_token, user } = response.data;
    setSession(access_token);
    dispatch({
      type: LOGIN,
      payload: {
        isLoggedIn: true,
        user,
      },
    });
  };

  const register = async (
    email: string,
    password: string,
    firstName: string,
    lastName: string
  ) => {
    // todo: this flow need to be recode as it not verified
    const id = chance.bb_pin();
    const response = await axios.post('/api/account/register', {
      id,
      email,
      password,
      firstName,
      lastName,
    });
    let users = response.data;

    if (
      window.localStorage.getItem('users') !== undefined &&
      window.localStorage.getItem('users') !== null
    ) {
      const localUsers = window.localStorage.getItem('users');
      users = [
        ...JSON.parse(localUsers!),
        {
          id,
          email,
          password,
          name: `${firstName} ${lastName}`,
        },
      ];
    }

    window.localStorage.setItem('users', JSON.stringify(users));
  };

  const logout = async () => {
    setSession(null);
    dispatch({ type: LOGOUT });
    return Promise.resolve();
  };

  const logoutWithoutCallingAPI = () => {
    setSession(null);
    dispatch({ type: LOGOUT });
  };

  // @ts-ignore
  const resetPassword = async (_email: string) => {
    throw new Error('Reset password not implemented');
  };

  // @ts-ignore
  const updateProfile = async (_profile: UserProfile) => {
    throw new Error('Update profile not implemented');
  };

  if (state.isInitialized !== undefined && !state.isInitialized) {
    return <Loader />;
  }

  return (
    <JWTContext.Provider
      value={{
        ...state,
        login,
        logout,
        logoutWithoutCallingAPI,
        register,
        resetPassword,
        updateProfile,
      }}
    >
      {children}
    </JWTContext.Provider>
  );
};

export default JWTContext;

/**
 * System Service Types
 *
 * Type definitions for system-level operations including health checks,
 * API status monitoring, and service availability detection.
 */

/**
 * System health check response structure
 * Represents the response from the system health endpoint
 */
export interface HealthCheckResponse {
  /** Overall system health status */
  status: 'healthy' | 'unhealthy' | 'unknown';
  /** ISO timestamp of when the health check was performed */
  timestamp: string;
  /** Optional detailed service status information */
  services?: {
    /** Database connection status */
    database?: 'up' | 'down';
    /** Cache service status */
    cache?: 'up' | 'down';
    /** Storage service status */
    storage?: 'up' | 'down';
    /** Additional services can be added here */
    [key: string]: 'up' | 'down' | undefined;
  };
  /** System version information */
  version?: string;
  /** System uptime in seconds */
  uptime?: number;
  /** Additional metadata */
  metadata?: Record<string, unknown>;
}

/**
 * API availability status information
 * Cached information about the last API availability check
 */
export interface ApiStatus {
  /** Whether the API is currently available */
  available: boolean;
  /** Timestamp of when the check was performed */
  lastChecked: Date;
  /** Response time in milliseconds */
  responseTime?: number;
  /** Error message if the check failed */
  error?: string;
}

/**
 * System service configuration options
 */
export interface SystemServiceConfig {
  /** Health check endpoint URL */
  healthEndpoint?: string;
  /** Default timeout for health checks */
  defaultTimeout?: number;
  /** Maximum age for cached status (in minutes) */
  maxCacheAge?: number;
}

/**
 * System metrics interface
 * Extended information about system performance
 */
export interface SystemMetrics {
  /** Current CPU usage percentage */
  cpuUsage?: number;
  /** Current memory usage percentage */
  memoryUsage?: number;
  /** Current disk usage percentage */
  diskUsage?: number;
  /** Number of active connections */
  activeConnections?: number;
  /** Request rate per second */
  requestRate?: number;
  /** Error rate percentage */
  errorRate?: number;
}

/**
 * Service dependency status
 * Information about external service dependencies
 */
export interface ServiceDependency {
  /** Service name */
  name: string;
  /** Service status */
  status: 'up' | 'down' | 'degraded' | 'unknown';
  /** Response time in milliseconds */
  responseTime?: number;
  /** Last check timestamp */
  lastChecked?: Date;
  /** Service URL or endpoint */
  endpoint?: string;
  /** Error message if service is down */
  error?: string;
}

/**
 * System status summary
 * Comprehensive system status information
 */
export interface SystemStatus {
  /** Overall system health */
  health: HealthCheckResponse;
  /** API availability status */
  apiStatus: ApiStatus;
  /** System performance metrics */
  metrics?: SystemMetrics;
  /** External service dependencies */
  dependencies?: ServiceDependency[];
  /** System configuration status */
  configuration?: {
    environment: string;
    version: string;
    buildDate?: string;
    commitHash?: string;
  };
}

/**
 * Health check options
 * Configuration for individual health checks
 */
export interface HealthCheckOptions {
  /** Request timeout in milliseconds */
  timeout?: number;
  /** Include detailed service status */
  includeServices?: boolean;
  /** Include system metrics */
  includeMetrics?: boolean;
  /** Include dependency status */
  includeDependencies?: boolean;
}

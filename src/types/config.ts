export type ConfigProps = {
  layout: string;
  drawerType: string;
  fontFamily: string;
  borderRadius: number;
  outlinedFilled: boolean;
  theme: 'light' | 'dark';
  presetColor: string;
  locale: string;
  rtlLayout: boolean;
  container: boolean;
};

export type CustomizationProps = {
  layout: string;
  drawerType: string;
  fontFamily: string;
  borderRadius: number;
  outlinedFilled: boolean;
  theme: 'light' | 'dark';
  presetColor: string;
  locale: string;
  rtlLayout: boolean;
  container: boolean;
  fontSize: 'small' | 'default' | 'large';
  onChangeLayout: (_layout: string) => void;
  onChangeDrawer: (_drawerType: string) => void;
  onChangeTheme: (_theme: 'light' | 'dark') => void;
  onChangePresetColor: (_presetColor: string) => void;
  onChangeLocale: (_locale: string) => void;
  onChangeRTL: (_rtlLayout: boolean) => void;
  onChangeContainer: (_container: boolean) => void;
  onChangeFontFamily: (_fontFamily: string) => void;
  onChangeBorderRadius: (_event: Event, _newValue: number | number[]) => void;
  onChangeOutlinedField: (_outlinedFilled: boolean) => void;
  onChangeFontSize: (_fontSize: 'small' | 'default' | 'large') => void;
  onReset: () => void;
};

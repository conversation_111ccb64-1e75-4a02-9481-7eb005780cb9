/**
 * Toast Types
 *
 * Type definitions for toast notifications that can be safely imported by hooks
 * without violating the unidirectional architecture.
 */
import * as React from 'react';

// These types are extracted from the toast component to allow hooks to import them
// without violating architecture rules (hooks cannot import from UI components)

export type ToastActionElement = React.ReactElement<{
  altText: string;
  onClick?: () => void;
  className?: string;
  children?: React.ReactNode;
}>;

export type ToastProps = {
  id?: string;
  title?: React.ReactNode;
  description?: React.ReactNode;
  action?: ToastActionElement;
  open?: boolean;
  onOpenChange?: (_open: boolean) => void;
  className?: string;
  variant?: 'default' | 'destructive';
};

export type ToasterToast = ToastProps & {
  id: string;
  title?: React.ReactNode;
  description?: React.ReactNode;
  action?: ToastActionElement;
  duration?: number;
};

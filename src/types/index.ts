import React, { ReactElement, ReactNode } from 'react';

// project imports
import { UserStateProps } from '@/types/user';

// Export individual feature types to avoid naming conflicts
export * from './public/help-center';
export * from './public/blog';
export * from './public/milestones';
export * from './public/resources';
export * from './public/policies';
export * from './public/support';
export * from './public/careers';

// Site configuration types
export interface Office {
  name: string;
  street: string;
  city: string;
  state: string;
  zip: string;
  country: string;
  mapLink: string;
}

// Rename conflicting type exports
export type { TeamMember } from './public/team';
export type { Department as TeamDepartment } from './public/team';
export type { JobOpening } from './public/careers';
export type { Department as CareerDepartment } from './public/careers';

export type ArrangementOrder = 'asc' | 'desc' | undefined;

export type DateRange = { start: number | Date; end: number | Date };

export type GetComparator = (
  _o: ArrangementOrder,
  _o1: string
) => (_a: KeyedObject, _b: KeyedObject) => number;

export type Direction = 'up' | 'down' | 'right' | 'left';

export type LinkTarget = '_blank' | '_self' | '_parent' | '_top';

/**
 * Component-related types
 * Note: Consider moving component-specific types to their component files in future refactoring
 */
export interface TabsProps {
  children?: React.ReactElement | React.ReactNode | string;
  value: string | number;
  index: number;
}

export interface GenericCardProps {
  title?: string;
  primary?: string | number | undefined;
  secondary?: string;
  content?: string;
  image?: string;
  dateTime?: string;
  color?: string;
  size?: string;
}

export interface EnhancedTableToolbarProps {
  numSelected: number;
}

export type HeadCell = {
  id: string;
  numeric: boolean;
  label: string;
  disablePadding?: string | boolean | undefined;
  align?: 'left' | 'right' | 'inherit' | 'center' | 'justify' | undefined;
};

/**
 * Navigation types
 */
export type NavItemTypeObject = {
  children?: NavItemType[];
  items?: NavItemType[];
  type?: string;
};

export type NavItemType = {
  id?: string;
  target?: boolean;
  external?: boolean;
  url?: string | undefined;
  type?: string;
  title?: ReactNode | string;
  color?: 'primary' | 'secondary' | 'default' | undefined;
  caption?: ReactNode | string;
  breadcrumbs?: boolean;
  disabled?: boolean;
  children?: NavItemType[];
  elements?: NavItemType[];
  search?: string;
};

/**
 * Auth and theme related types
 */
export type AuthSliderProps = {
  title: string;
  description: string;
};

export interface ColorPaletteProps {
  color: string;
  label: string;
  value: string;
}

export interface DefaultRootStateProps {
  user: UserStateProps;
}

export interface ColorProps {
  readonly [key: string]: string;
}

export type GuardProps = {
  children: ReactElement | null;
};

export interface StringColorProps {
  id?: string;
  label?: string;
  color?: string;
  primary?: string;
  secondary?: string;
}

/**
 * Form related types
 */
export interface FormInputProps {
  bug: KeyedObject;
  fullWidth?: boolean;
  size?: 'small' | 'medium' | undefined;
  label: string;
  name: string;
  required?: boolean;
  InputProps?: {
    label: string;
    startAdornment?: React.ReactNode;
  };
}

/**
 * Common function types
 */
export type StringBoolFunc = (_s: string) => boolean;
export type StringNumFunc = (_s: string) => number;
export type NumbColorFunc = (_n: number) => StringColorProps | undefined;
export type ChangeEventFunc = (_e: React.ChangeEvent<HTMLInputElement>) => void;

/**
 * Utility types
 */
export type KeyedObject = {
  [key: string]: string | number | KeyedObject | any;
};

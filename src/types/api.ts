/**
 * Common API Types
 *
 * Shared type definitions for API responses, requests, and error handling
 */

// ==========================================
// PAGINATION TYPES
// ==========================================

export interface PaginationParams {
  page?: number;
  size?: number;
  sort?: string;
  order?: 'asc' | 'desc';
}

export interface PaginatedResponse<T> {
  content: T[];
  totalElements: number;
  totalPages: number;
  size: number;
  number: number;
  first: boolean;
  last: boolean;
  numberOfElements: number;
  empty: boolean;
}

// ==========================================
// API RESPONSE TYPES
// ==========================================

export interface ApiResponse<T = any> {
  data: T;
  message?: string;
  success: boolean;
  timestamp: string;
}

export interface ApiError {
  code: string;
  message: string;
  details?: Record<string, any>;
  timestamp: string;
  path?: string;
  status: number;
}

// ==========================================
// FILTER AND SEARCH TYPES
// ==========================================

export interface BaseFilters {
  search?: string;
  status?: string;
  createdFrom?: string;
  createdTo?: string;
}

export interface SortOptions {
  field: string;
  direction: 'asc' | 'desc';
}

// ==========================================
// BULK OPERATION TYPES
// ==========================================

export interface BulkOperationRequest {
  ids: string[];
  action: string;
  parameters?: Record<string, any>;
}

export interface BulkOperationResponse {
  successful: string[];
  failed: Array<{
    id: string;
    error: string;
  }>;
  totalProcessed: number;
  successCount: number;
  failureCount: number;
}

// ==========================================
// FILE UPLOAD TYPES
// ==========================================

export interface FileUploadResponse {
  id: string;
  filename: string;
  originalName: string;
  mimeType: string;
  size: number;
  url: string;
  uploadedAt: string;
}

export interface FileUploadProgress {
  loaded: number;
  total: number;
  percentage: number;
}

// ==========================================
// VALIDATION TYPES
// ==========================================

export interface ValidationError {
  field: string;
  message: string;
  code: string;
  rejectedValue?: any;
}

export interface ValidationResponse {
  valid: boolean;
  errors: ValidationError[];
}

// ==========================================
// AUDIT TYPES
// ==========================================

export interface AuditLog {
  id: string;
  action: string;
  entityType: string;
  entityId: string;
  userId: string;
  userName: string;
  timestamp: string;
  changes?: Record<
    string,
    {
      oldValue: any;
      newValue: any;
    }
  >;
  metadata?: Record<string, any>;
}

// ==========================================
// SYSTEM TYPES
// ==========================================

export interface HealthCheckResponse {
  status: 'healthy' | 'unhealthy' | 'degraded';
  timestamp: string;
  version?: string;
  uptime?: number;
  checks?: Record<
    string,
    {
      status: 'healthy' | 'unhealthy';
      message?: string;
      responseTime?: number;
    }
  >;
}

export interface ApiStatus {
  available: boolean;
  lastChecked: Date;
  responseTime: number;
  error?: string;
}

// ==========================================
// CACHE TYPES
// ==========================================

export interface CacheEntry<T> {
  data: T;
  timestamp: number;
  expiresAt: number;
}

export interface CacheOptions {
  ttl?: number; // Time to live in milliseconds
  key?: string;
  tags?: string[];
}

// ==========================================
// REQUEST TYPES
// ==========================================

export interface RequestConfig {
  timeout?: number;
  retries?: number;
  retryDelay?: number;
  cache?: boolean;
  cacheTTL?: number;
}

export interface RequestContext {
  requestId: string;
  timestamp: string;
  userId?: string;
  sessionId?: string;
  userAgent: string;
  ip?: string;
}

// ==========================================
// WEBHOOK TYPES
// ==========================================

export interface WebhookPayload<T = any> {
  id: string;
  event: string;
  timestamp: string;
  data: T;
  version: string;
}

export interface WebhookResponse {
  received: boolean;
  processedAt: string;
  errors?: string[];
}

// ==========================================
// EXPORT TYPES
// ==========================================

export interface ExportRequest {
  format: 'csv' | 'xlsx' | 'json' | 'pdf';
  filters?: Record<string, any>;
  fields?: string[];
  options?: Record<string, any>;
}

export interface ExportResponse {
  id: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  downloadUrl?: string;
  createdAt: string;
  completedAt?: string;
  error?: string;
  fileSize?: number;
  recordCount?: number;
}

// ==========================================
// NOTIFICATION TYPES
// ==========================================

export interface NotificationPreferences {
  email: boolean;
  push: boolean;
  sms: boolean;
  inApp: boolean;
}

export interface NotificationTemplate {
  id: string;
  name: string;
  subject: string;
  body: string;
  type: 'email' | 'push' | 'sms' | 'in_app';
  variables: string[];
}

// ==========================================
// RATE LIMITING TYPES
// ==========================================

export interface RateLimitInfo {
  limit: number;
  remaining: number;
  resetTime: string;
  retryAfter?: number;
}

export interface RateLimitResponse {
  rateLimited: boolean;
  info: RateLimitInfo;
}

// ==========================================
// TYPE GUARDS
// ==========================================

export function isApiError(error: any): error is ApiError {
  return (
    error && typeof error === 'object' && 'code' in error && 'message' in error
  );
}

export function isPaginatedResponse<T>(
  response: any
): response is PaginatedResponse<T> {
  return (
    response &&
    typeof response === 'object' &&
    Array.isArray(response.content) &&
    typeof response.totalElements === 'number'
  );
}

export function isValidationError(error: any): error is ValidationError {
  return (
    error && typeof error === 'object' && 'field' in error && 'message' in error
  );
}

// ==========================================
// UTILITY TYPES
// ==========================================

export type ApiMethod = 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';

export type ApiEndpoint = string;

export type QueryKey = readonly unknown[];

export type MutationKey = readonly unknown[];

// ==========================================
// GENERIC API TYPES
// ==========================================

export interface BaseEntity {
  id: string;
  createdAt: string;
  updatedAt: string;
  createdBy?: string;
  updatedBy?: string;
}

export interface CreateRequest<T> {
  data: Omit<T, keyof BaseEntity>;
}

export interface UpdateRequest<T> {
  id: string;
  data: Partial<Omit<T, keyof BaseEntity>>;
}

export interface DeleteRequest {
  id: string;
  force?: boolean;
}

// ==========================================
// API CLIENT TYPES
// ==========================================

export interface ApiClientConfig {
  baseURL: string;
  timeout: number;
  retries: number;
  retryDelay: number;
  headers?: Record<string, string>;
  interceptors?: {
    request?: Array<(_config: any) => any>;
    response?: Array<(_response: any) => any>;
  };
}

export interface ApiClient {
  get<T>(_url: string, _config?: RequestConfig): Promise<T>;
  post<T>(_url: string, _data?: any, _config?: RequestConfig): Promise<T>;
  put<T>(_url: string, _data?: any, _config?: RequestConfig): Promise<T>;
  patch<T>(_url: string, _data?: any, _config?: RequestConfig): Promise<T>;
  delete<T>(_url: string, _config?: RequestConfig): Promise<T>;
}

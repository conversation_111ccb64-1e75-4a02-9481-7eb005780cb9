import { Translation } from '@/types/shared';

export interface CaseStudyCategory {
  id: string;
  name: Translation;
  slug: string;
}

export interface CaseStudyClient {
  id: string;
  name: string;
  industry: Translation;
  logo: string;
  website?: string;
}

export interface CaseStudyMetric {
  id: string;
  label: Translation;
  value: string;
  description: Translation;
  icon?: string;
}

export interface CaseStudyChallenge {
  id: string;
  title: Translation;
  description: Translation;
}

export interface CaseStudySolution {
  id: string;
  title: Translation;
  description: Translation;
  features?: Translation[];
}

export interface CaseStudyOutcome {
  id: string;
  title: Translation;
  description: Translation;
  metrics?: CaseStudyMetric[];
}

export interface CaseStudy {
  id: string;
  slug: string;
  title: Translation;
  excerpt: Translation;
  content: Translation;
  featuredImage: string;
  category: CaseStudyCategory;
  client: CaseStudyClient;
  publishedAt: string;
  updatedAt: string;
  tags: string[];
  readTime: number;
  featured: boolean;
  status: 'published' | 'draft';
  challenges: CaseStudyChallenge[];
  solutions: CaseStudySolution[];
  outcomes: CaseStudyOutcome[];
  keyMetrics: CaseStudyMetric[];
  testimonial?: {
    quote: Translation;
    author: {
      name: string;
      title: Translation;
      avatar?: string;
    };
  };
}

export interface CaseStudyFilters {
  category?: string;
  industry?: string;
  tag?: string;
  search?: string;
  featured?: boolean;
}

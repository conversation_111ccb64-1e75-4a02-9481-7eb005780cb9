import { Translation } from '../shared';

export interface Service {
  id: string;
  name: Translation;
  description: Translation;
  features: Translation[];
  icon: string;
  category: string;
  pricing?: {
    startingPrice: number;
    currency: string;
    period: 'month' | 'year' | 'one-time';
  };
  popular?: boolean;
  ctaText: Translation;
  ctaLink: string;
}

export interface ServiceCategory {
  id: string;
  name: Translation;
  description: Translation;
  icon: string;
}

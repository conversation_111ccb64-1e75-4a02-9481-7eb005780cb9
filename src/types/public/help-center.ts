/**
 * Help Center-related type definitions
 */

import { Translation } from '../shared';

/**
 * Help center article structure
 */
export interface HelpArticle {
  id: string;
  title: Translation;
  summary: Translation;
  content?: Translation;
  category?: string;
  tags?: string[];
  lastUpdated?: string;
  difficulty?: 'beginner' | 'intermediate' | 'advanced';
  estimatedReadTime?: number;
}

/**
 * Help content section types
 */
export type HelpContentType =
  | 'heading'
  | 'paragraph'
  | 'list'
  | 'code'
  | 'note'
  | 'warning';

/**
 * Individual content section in a help article
 */
export interface HelpContentSection {
  type: HelpContentType;
  content: Translation;
  items?: Translation[]; // For list type
}

/**
 * Detailed help article with full content
 */
export interface HelpArticleDetail extends Omit<HelpArticle, 'content'> {
  excerpt: Translation;
  readingTime: number; // in minutes
  featured: boolean;
  content: HelpContentSection[];
  relatedArticles?: string[];
}

/**
 * Help center category structure
 */
export interface HelpCategory {
  id: string;
  name: Translation;
  icon?: string;
  description?: Translation;
  articles: HelpArticle[];
  articleCount?: number;
}

/**
 * Guide item structure for step-by-step guides
 */
export interface GuideItem {
  id: string;
  title: Translation;
  description: Translation;
  category?: string;
  difficulty?: 'beginner' | 'intermediate' | 'advanced';
  estimatedTime?: Translation;
  topics?: Translation[];
  icon?: string;
  featured?: boolean;
}

/**
 * Resource types for help center resources
 */
export interface ResourceType {
  id: string;
  title: Translation;
  description: Translation;
  icon: string;
  count: number;
  items?: ResourceItem[];
}

/**
 * Individual resource item
 */
export interface ResourceItem {
  id: string;
  title: Translation;
  description: Translation;
  type: 'video' | 'docs' | 'article' | 'api' | 'download';
  url?: string;
  downloadUrl?: string;
  duration?: number; // for videos
  fileSize?: string; // for downloads
  format?: string; // for downloads
  lastUpdated?: string;
}

/**
 * Help center search filters
 */
export interface HelpFilters {
  category?: string;
  difficulty?: string;
  type?: string;
  query?: string;
  locale?: string;
}

/**
 * Help center search result
 */
export interface HelpSearchResult {
  type: 'article' | 'guide' | 'resource';
  item: HelpArticle | GuideItem | ResourceItem;
  relevanceScore?: number;
  highlightedText?: string;
}

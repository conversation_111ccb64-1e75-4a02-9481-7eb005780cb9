import { Translation } from '@/types/shared';

export interface ResourceCategory {
  id: string;
  name: Translation;
  slug: string;
  color?: string;
  icon?: string;
}

export interface ResourceTag {
  id: string;
  name: Translation;
  slug: string;
}

export interface ResourceAuthor {
  id: string;
  name: string;
  title?: Translation;
  avatar?: string;
  bio?: Translation;
}

export interface Resource {
  id: string;
  slug: string;
  title: Translation;
  description: Translation;
  excerpt: Translation;
  content?: Translation;
  featuredImage?: string;
  category: ResourceCategory;
  tags: ResourceTag[];
  author?: ResourceAuthor;
  fileType:
    | 'pdf'
    | 'doc'
    | 'xls'
    | 'ppt'
    | 'zip'
    | 'video'
    | 'audio'
    | 'image'
    | 'link'
    | 'other';
  fileSize?: string;
  downloadUrl?: string;
  externalUrl?: string;
  featured: boolean;
  premium: boolean;
  publishedAt: string;
  updatedAt: string;
  downloadCount?: number;
  relatedResources?: string[]; // IDs of related resources
}

export interface ResourceFilters {
  category?: string;
  tag?: string;
  author?: string;
  fileType?: string;
  featured?: boolean;
  premium?: boolean;
  search?: string;
}

// Legacy resource interface for main page compatibility
export interface LegacyResource {
  id: string;
  slug: string;
  title: Translation;
  description: Translation;
  excerpt: Translation;
  featuredImage?: string;
  category: string; // String instead of object for main page
  tags: ResourceTag[];
  author?: ResourceAuthor;
  fileType:
    | 'pdf'
    | 'doc'
    | 'xls'
    | 'ppt'
    | 'zip'
    | 'video'
    | 'audio'
    | 'image'
    | 'link'
    | 'other';
  fileSize?: string;
  downloadUrl?: string | null;
  videoUrl?: string | null;
  duration?: string | null;
  date: string;
  featured: boolean;
  premium: boolean;
  publishedAt: string;
  updatedAt: string;
  downloadCount?: number;
}

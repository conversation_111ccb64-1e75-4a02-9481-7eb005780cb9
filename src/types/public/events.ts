import { Translation } from '@/types/shared';

export interface EventCategory {
  id: string;
  name: Translation;
  slug: string;
  color?: string;
}

export interface EventSpeaker {
  id: string;
  name: string;
  title: Translation;
  company?: string;
  bio: Translation;
  avatar?: string;
  social?: {
    twitter?: string;
    linkedin?: string;
    website?: string;
  };
}

export interface EventLocation {
  id: string;
  name: string;
  address: string;
  city: string;
  country: string;
  coordinates?: {
    lat: number;
    lng: number;
  };
  venue?: {
    name: string;
    capacity?: number;
    website?: string;
  };
}

export interface EventScheduleItem {
  id: string;
  title: Translation;
  description: Translation;
  startTime: string;
  endTime: string;
  speaker?: EventSpeaker;
  type: 'session' | 'break' | 'networking' | 'keynote' | 'workshop';
}

export interface EventRegistration {
  isRequired: boolean;
  isOpen: boolean;
  maxAttendees?: number;
  currentAttendees?: number;
  price?: {
    amount: number;
    currency: string;
    earlyBird?: {
      amount: number;
      deadline: string;
    };
  };
  registrationUrl?: string;
  waitlistAvailable?: boolean;
}

export interface Event {
  id: string;
  slug: string;
  title: Translation;
  description: Translation;
  excerpt: Translation;
  content: Translation;
  featuredImage: string;
  category: EventCategory;
  startDate: string;
  endDate: string;
  location: EventLocation;
  speakers: EventSpeaker[];
  schedule?: EventScheduleItem[];
  registration: EventRegistration;
  tags: string[];
  featured: boolean;
  status: 'upcoming' | 'ongoing' | 'completed' | 'cancelled';
  publishedAt: string;
  updatedAt: string;
  organizer: {
    name: string;
    email?: string;
    website?: string;
  };
  socialLinks?: {
    website?: string;
    twitter?: string;
    linkedin?: string;
    facebook?: string;
  };
  requirements?: Translation[];
  benefits?: Translation[];
  targetAudience?: Translation[];
}

export interface EventFilters {
  category?: string;
  status?: 'upcoming' | 'ongoing' | 'completed';
  location?: string;
  featured?: boolean;
  dateRange?: {
    start: string;
    end: string;
  };
}

/**
 * Careers-related type definitions
 */

import { Translation } from '../shared';

/**
 * Department information
 */
export interface Department {
  id: string;
  name: Translation;
}

/**
 * Job opening structure
 */
export interface JobOpening {
  id: number;
  title: Translation;
  department: string;
  location: Translation;
  type: Translation;
  description: Translation;
  responsibilities: Translation[];
  requirements: Translation[];
  postedDate?: string;
  applicationDeadline?: string;
  salary?: {
    min?: number;
    max?: number;
    currency?: string;
  };
  benefits?: Translation[];
  experience?: Translation;
  remote?: boolean;
  urgent?: boolean;
}

/**
 * Job application structure
 */
export interface JobApplication {
  id: string;
  jobId: number;
  applicantName: string;
  applicantEmail: string;
  resume: string;
  coverLetter?: string;
  appliedDate: string;
  status: 'pending' | 'reviewing' | 'interviewing' | 'rejected' | 'hired';
}

/**
 * Job application form structure
 */
export interface JobApplicationForm {
  personalInfo: {
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
    location: string;
  };
  documents: {
    resume: File | null;
    coverLetter?: File | null;
    portfolio?: string;
  };
  experience: {
    yearsExperience: number;
    currentRole?: string;
    expectedSalary?: string;
  };
  additionalInfo?: string;
}

/**
 * Job filter options
 */
export interface JobFilters {
  department?: string;
  location?: string;
  type?: string;
  remote?: boolean;
  query?: string;
  locale?: string;
}

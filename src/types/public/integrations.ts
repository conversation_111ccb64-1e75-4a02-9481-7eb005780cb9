import { Translation } from '@/types/shared';

export interface IntegrationCategory {
  id: string;
  name: Translation;
  slug: string;
  color?: string;
  icon?: string;
}

export interface Integration {
  id: string;
  slug: string;
  name: string;
  description: Translation;
  shortDescription: Translation;
  logo: string;
  category: IntegrationCategory;
  tags: string[];
  featured: boolean;
  popular: boolean;
  setupDifficulty: 'easy' | 'medium' | 'advanced';
  documentationUrl?: string;
  setupGuideUrl?: string;
  apiDocsUrl?: string;
  features: Translation[];
  requirements?: Translation[];
  pricing?: {
    free: boolean;
    paidPlans: boolean;
    startingPrice?: string;
  };
  status: 'active' | 'beta' | 'coming-soon';
  lastUpdated: string;
  integrationCount?: number;
}

export interface IntegrationFilters {
  category?: string;
  difficulty?: string;
  status?: string;
  featured?: boolean;
  popular?: boolean;
  search?: string;
}

/**
 * Policies-related type definitions
 */

import { Translation } from '../shared';

/**
 * Policy content structure with translations
 */
export interface PolicyContent {
  title: string;
  titleTranslations?: Translation;
  content: Translation;
  lastUpdated?: string;
  version?: string;
  effectiveDate?: string;
}

/**
 * Policy metadata
 */
export interface PolicyMetadata {
  id: string;
  title: string;
  description?: string;
  category: 'legal' | 'privacy' | 'terms' | 'compliance' | 'security';
  status: 'active' | 'draft' | 'archived';
  lastUpdated: string;
  version: string;
  effectiveDate: string;
  author?: string;
  approver?: string;
}

/**
 * Policy section structure
 */
export interface PolicySection {
  id: string;
  title: string;
  content: string;
  subsections?: PolicySection[];
  order: number;
}

/**
 * Policy navigation item
 */
export interface PolicyNavigationItem {
  id: string;
  title: string;
  href: string;
  description?: string;
  category?: string;
  featured?: boolean;
}

/**
 * Policy change log entry
 */
export interface PolicyChangeLogEntry {
  date: string;
  version: string;
  changes: string[];
  author: string;
  type: 'major' | 'minor' | 'patch';
}

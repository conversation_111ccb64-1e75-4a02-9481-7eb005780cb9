import { Translation } from '@/types/shared';

export interface NewsCategory {
  id: string;
  name: Translation;
  slug: string;
}

export interface NewsAuthor {
  id: string;
  name: string;
  title: Translation;
  avatar: string;
  bio: Translation;
}

export interface NewsArticle {
  id: string;
  slug: string;
  title: Translation;
  excerpt: Translation;
  content: Translation;
  featuredImage: string;
  category: NewsCategory;
  author: NewsAuthor;
  publishedAt: string;
  updatedAt: string;
  tags: string[];
  readTime: number;
  featured: boolean;
  status: 'published' | 'draft';
}

export interface NewsFilters {
  category?: string;
  author?: string;
  tag?: string;
  search?: string;
  featured?: boolean;
}

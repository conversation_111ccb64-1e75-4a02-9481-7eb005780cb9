/**
 * Support Types
 *
 * Type definitions for support functionality including tickets,
 * articles, and contact forms.
 */

import { Translation } from '@/types/shared';

/**
 * Support ticket priority levels
 */
export type SupportPriority = 'low' | 'medium' | 'high' | 'urgent';

/**
 * Support ticket categories
 */
export type SupportCategory =
  | 'technical'
  | 'billing'
  | 'account'
  | 'feature'
  | 'other';

/**
 * Support ticket status
 */
export type SupportTicketStatus =
  | 'open'
  | 'in-progress'
  | 'resolved'
  | 'closed';

/**
 * Contact information for support ticket
 */
export interface SupportContactInfo {
  name: string;
  email: string;
  company?: string;
}

/**
 * Support ticket information
 */
export interface SupportTicketInfo {
  subject: string;
  category: SupportCategory | string;
  priority: SupportPriority;
  description: string;
}

/**
 * Support ticket form data
 */
export interface SupportTicketFormType {
  contactInfo: SupportContactInfo;
  ticketInfo: SupportTicketInfo;
  attachments: File[];
}

/**
 * Support ticket (full ticket with ID and status)
 */
export interface SupportTicket extends SupportTicketFormType {
  id: string;
  ticketNumber: string;
  status: SupportTicketStatus;
  createdAt: string;
  updatedAt: string;
  assignedTo?: string;
  responses?: SupportTicketResponse[];
}

/**
 * Support ticket response
 */
export interface SupportTicketResponse {
  id: string;
  ticketId: string;
  author: string;
  authorType: 'customer' | 'support';
  message: string;
  createdAt: string;
  attachments?: string[];
}

/**
 * Support article content section types
 */
export type SupportContentType =
  | 'heading'
  | 'paragraph'
  | 'list'
  | 'code'
  | 'note'
  | 'warning'
  | 'image';

/**
 * Individual content section in a support article
 */
export interface SupportContentSection {
  type: SupportContentType;
  content: Translation;
  items?: Translation[]; // For list type
  src?: string; // For image type
  alt?: Translation; // For image type
}

/**
 * Basic support article information
 */
export interface SupportArticle {
  id: string;
  title: Translation;
  excerpt: Translation;
  category: string;
  readingTime: number; // in minutes
  lastUpdated: string;
  featured: boolean;
  tags: string[];
  helpfulness: number; // 0-5 rating
  views: number;
}

/**
 * Detailed support article with full content
 */
export interface SupportArticleDetail extends SupportArticle {
  content: SupportContentSection[];
  relatedArticles?: string[];
}

/**
 * Support category information
 */
export interface SupportCategoryInfo {
  id: string;
  name: Translation;
  description: Translation;
  icon: string;
  articleCount: number;
  color?: string;
}

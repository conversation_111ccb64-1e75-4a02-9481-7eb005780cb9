/**
 * Blog-related type definitions
 */

import type { Translation } from '../shared';

// Re-export Translation for convenience
export type { Translation };

/**
 * Blog post author information
 */
export interface Author {
  name: string;
  avatar: string;
  title: string;
}

/**
 * Blog post category
 */
export interface Category {
  id: string;
  name: Translation;
}

/**
 * Blog post structure
 */
export interface BlogPost {
  id: number;
  title: Translation;
  excerpt: Translation;
  category: string;
  author: Author;
  publishDate: string;
  readTime: Translation;
  image: string;
  featured: boolean;
  tags: string[];
}

/**
 * Blog filter options
 */
export interface BlogFilters {
  category?: string;
  query?: string;
  locale?: string;
  page?: number;
  postsPerPage?: number;
}

/**
 * Blog pagination information
 */
export interface BlogPagination {
  currentPage: number;
  totalPages: number;
  totalPosts: number;
  postsPerPage: number;
}

/**
 * Table of contents item for blog posts
 */
export interface TOCItem {
  id: string;
  title: string;
  level: number;
}

/**
 * Extended blog post with full content for detail pages
 */
export interface BlogPostDetail extends BlogPost {
  content: Translation;
  tableOfContents?: TOCItem[] | { [key: string]: TOCItem[] };
  relatedPosts?: number[];
  socialShares?: {
    facebook: number;
    twitter: number;
    linkedin: number;
  };
}

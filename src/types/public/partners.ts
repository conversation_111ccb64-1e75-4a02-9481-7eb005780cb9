import { Translation } from '@/types/shared';

export interface PartnerCategory {
  id: string;
  name: Translation;
  slug: string;
}

export interface Partner {
  id: string;
  name: string;
  logo: string;
  website: string;
  description: Translation;
  category: PartnerCategory;
  featured: boolean;
  testimonial?: {
    quote: Translation;
    author: string;
    position: string;
  };
  caseStudyLink?: string;
  partnershipDate: string;
  status: 'active' | 'inactive';
}

export interface PartnerFilters {
  category?: string;
  featured?: boolean;
}

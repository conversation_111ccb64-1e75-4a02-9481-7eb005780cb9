import { Translation } from '@/types/shared';

export interface ChangelogCategory {
  id: string;
  name: Translation;
  slug: string;
  color?: string;
  icon?: string;
}

export interface ChangelogEntry {
  id: string;
  version: string;
  title: Translation;
  description: Translation;
  releaseDate: string;
  category: ChangelogCategory;
  changes: ChangelogChange[];
  featured: boolean;
  breaking: boolean;
  prerelease: boolean;
  downloadUrl?: string;
  githubUrl?: string;
  blogPostUrl?: string;
}

export interface ChangelogChange {
  id: string;
  type:
    | 'feature'
    | 'improvement'
    | 'bugfix'
    | 'security'
    | 'deprecated'
    | 'removed';
  title: Translation;
  description: Translation;
  impact?: 'low' | 'medium' | 'high';
  pullRequestUrl?: string;
  issueUrl?: string;
}

export interface ChangelogFilters {
  category?: string;
  type?: string;
  version?: string;
  featured?: boolean;
  breaking?: boolean;
  prerelease?: boolean;
  search?: string;
}

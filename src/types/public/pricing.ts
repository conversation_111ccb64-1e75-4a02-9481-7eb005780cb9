import { Translation } from '../shared';

export interface PricingFeature {
  id: string;
  name: Translation;
  description?: Translation;
  included: boolean;
  highlight?: boolean;
}

export interface PricingTier {
  id: string;
  name: Translation;
  description: Translation;
  price: {
    monthly: number;
    yearly: number;
    currency: string;
  };
  popular: boolean;
  features: PricingFeature[];
  cta: {
    text: Translation;
    href: string;
    variant: 'default' | 'outline' | 'secondary';
  };
  badge?: Translation;
  limits?: {
    users?: number | 'unlimited';
    storage?: string;
    projects?: number | 'unlimited';
    support?: string;
  };
}

export interface PricingComparison {
  category: Translation;
  features: {
    id: string;
    name: Translation;
    description?: Translation;
    tiers: {
      [tierId: string]: boolean | string | number;
    };
  }[];
}

export type BillingPeriod = 'monthly' | 'yearly';

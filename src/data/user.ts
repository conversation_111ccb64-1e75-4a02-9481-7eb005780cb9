import type {
  UserProfileData,
  NotificationData,
  NotificationSettings,
  SecurityData,
  UserSettingsData,
} from '@/types/user';

/**
 * Mock User Profile Data
 * Used in /user/profile page
 */
export const mockUserProfile: UserProfileData = {
  firstName: 'John',
  lastName: 'Doe',
  email: '<EMAIL>',
  phone: '+****************',
  jobTitle: 'Product Manager',
  company: 'Acme Inc.',
  bio: 'Product enthusiast with over 5 years of experience in the tech industry.',
  location: 'New York, USA',
  avatarUrl: null,
  joinDate: 'January 15, 2023',
  lastLogin: 'Today at 9:30 AM',
};

/**
 * Mock Notifications Data
 * Used in /user/notifications page
 */
export const mockNotifications: NotificationData[] = [
  {
    id: 1,
    type: 'system',
    title: 'System Maintenance Notice',
    message:
      'Our system will undergo scheduled maintenance on November 20th from 2:00 AM to 4:00 AM UTC. Some features may be unavailable during this time.',
    date: '2023-11-15T10:30:00Z',
    read: false,
    actionUrl: '/system-updates',
  },
  {
    id: 2,
    type: 'activity',
    title: 'Dashboard Report Generated',
    message:
      'Your monthly analytics report has been generated and is ready to view.',
    date: '2023-11-14T15:45:00Z',
    read: true,
    actionUrl: '/reports/monthly-november',
  },
  {
    id: 3,
    type: 'mention',
    title: 'Michael mentioned you in a comment',
    message:
      'Hey @john, can you review the updates to the client presentation?',
    date: '2023-11-14T09:20:00Z',
    read: false,
    user: {
      name: 'Michael Chen',
      avatar: '/images/avatars/michael-chen.jpg',
    },
    actionUrl: '/projects/client-presentation/comments',
  },
  {
    id: 4,
    type: 'activity',
    title: 'New Team Member Added',
    message: 'Sarah Williams has been added to your team.',
    date: '2023-11-13T14:10:00Z',
    read: true,
    actionUrl: '/team',
  },
  {
    id: 5,
    type: 'system',
    title: 'Security Alert',
    message:
      "A new login to your account was detected from a new device. If this wasn't you, please secure your account immediately.",
    date: '2023-11-12T08:45:00Z',
    read: true,
    actionUrl: '/user/security',
  },
  {
    id: 6,
    type: 'mention',
    title: 'Emily mentioned you in a task',
    message: "I've assigned you to the homepage redesign task @john",
    date: '2023-11-10T16:30:00Z',
    read: true,
    user: {
      name: 'Emily Johnson',
      avatar: '/images/avatars/emily-johnson.jpg',
    },
    actionUrl: '/tasks/homepage-redesign',
  },
  {
    id: 7,
    type: 'activity',
    title: 'Task Completed',
    message: "The 'Update Documentation' task has been marked as complete.",
    date: '2023-11-09T11:25:00Z',
    read: true,
    actionUrl: '/tasks/update-documentation',
  },
];

/**
 * Mock Notification Settings
 * Used in /user/notifications page
 */
export const mockNotificationSettings: NotificationSettings = {
  email: true,
  browser: true,
  mobile: true,
};

/**
 * Mock Security Data
 * Used in /user/security page
 */
export const mockSecurityData: SecurityData = {
  email: '<EMAIL>',
  passwordLastChanged: '2023-09-15T10:30:00Z',
  twoFactorEnabled: false,
  recoveryCodesRemaining: 8,
  activeSessions: [
    {
      id: 'sess_1234567890',
      device: 'Chrome on Windows',
      location: 'San Francisco, CA',
      ipAddress: '***********',
      lastActive: '2023-11-15T14:30:00Z',
      current: true,
    },
    {
      id: 'sess_0987654321',
      device: 'Mobile App on iPhone',
      location: 'New York, NY',
      ipAddress: '***********',
      lastActive: '2023-11-14T09:15:00Z',
      current: false,
    },
    {
      id: 'sess_5678901234',
      device: 'Safari on Mac',
      location: 'San Francisco, CA',
      ipAddress: '***********',
      lastActive: '2023-11-10T16:45:00Z',
      current: false,
    },
  ],
  securityEvents: [
    {
      id: 1,
      type: 'login',
      description: 'Successful login',
      ipAddress: '***********',
      location: 'San Francisco, CA',
      timestamp: '2023-11-15T14:30:00Z',
      device: 'Chrome on Windows',
    },
    {
      id: 2,
      type: 'password_change',
      description: 'Password changed',
      ipAddress: '***********',
      location: 'San Francisco, CA',
      timestamp: '2023-09-15T10:30:00Z',
      device: 'Chrome on Windows',
    },
    {
      id: 3,
      type: 'login_failed',
      description: 'Failed login attempt',
      ipAddress: '***********',
      location: 'Unknown',
      timestamp: '2023-09-10T08:45:00Z',
      device: 'Unknown',
    },
    {
      id: 4,
      type: 'mfa_disabled',
      description: 'Two-factor authentication disabled',
      ipAddress: '***********',
      location: 'San Francisco, CA',
      timestamp: '2023-08-24T11:20:00Z',
      device: 'Chrome on Windows',
    },
  ],
};

/**
 * Mock User Settings Data
 * Used in /user/settings page
 */
export const mockUserSettings: UserSettingsData = {
  name: 'John Doe',
  email: '<EMAIL>',
  avatar: '/images/avatars/default.jpg',
  jobTitle: 'Product Manager',
  company: 'Acme Inc.',
  bio: 'Product manager with 5+ years of experience in SaaS products.',
  location: 'San Francisco, CA',
  timezone: 'America/Los_Angeles',
  language: 'en',
  theme: 'system',
  notifications: {
    email: {
      productUpdates: true,
      securityAlerts: true,
      newsletters: false,
      usageReports: true,
    },
    inApp: {
      mentions: true,
      comments: true,
      taskAssignments: true,
      statusChanges: true,
    },
  },
  connectedAccounts: {
    google: true,
    github: false,
    linkedin: true,
    slack: false,
  },
};

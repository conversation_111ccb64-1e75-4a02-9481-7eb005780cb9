import { Testimonial, TestimonialCategory } from '@/types/public/testimonials';

export const testimonialCategories: TestimonialCategory[] = [
  {
    id: 'all',
    name: {
      en: 'All Testimonials',
      zh: '所有推荐',
      'zh-TW': '所有推薦',
    },
    description: {
      en: 'View all customer testimonials',
      zh: '查看所有客户推荐',
      'zh-TW': '查看所有客戶推薦',
    },
  },
  {
    id: 'enterprise',
    name: {
      en: 'Enterprise',
      zh: '企业',
      'zh-TW': '企業',
    },
    description: {
      en: 'Large enterprise customer testimonials',
      zh: '大型企业客户推荐',
      'zh-TW': '大型企業客戶推薦',
    },
  },
  {
    id: 'startup',
    name: {
      en: 'Startup',
      zh: '初创公司',
      'zh-TW': '新創公司',
    },
    description: {
      en: 'Startup and small business testimonials',
      zh: '初创公司和小企业推荐',
      'zh-TW': '新創公司和小企業推薦',
    },
  },
  {
    id: 'agency',
    name: {
      en: 'Agency',
      zh: '代理机构',
      'zh-TW': '代理機構',
    },
    description: {
      en: 'Digital agency and consultancy testimonials',
      zh: '数字代理和咨询公司推荐',
      'zh-TW': '數位代理和諮詢公司推薦',
    },
  },
];

export const testimonials: Testimonial[] = [
  {
    id: '1',
    content: {
      en: 'This platform has completely transformed how we manage our enterprise operations. The scalability and reliability are outstanding, and the support team is incredibly responsive.',
      zh: '这个平台彻底改变了我们管理企业运营的方式。可扩展性和可靠性都很出色，支持团队反应非常迅速。',
      'zh-TW':
        '這個平台徹底改變了我們管理企業營運的方式。可擴展性和可靠性都很出色，支援團隊反應非常迅速。',
    },
    author: {
      name: 'Sarah Johnson',
      title: 'CTO',
      company: 'TechCorp Solutions',
      avatar: '/images/testimonials/sarah-johnson.jpg',
    },
    rating: 5,
    featured: true,
    category: 'enterprise',
    date: '2024-01-15',
  },
  {
    id: '2',
    content: {
      en: 'As a growing startup, we needed a solution that could scale with us. This platform delivered exactly that, with excellent performance and intuitive design.',
      zh: '作为一家成长中的初创公司，我们需要一个能够与我们一起扩展的解决方案。这个平台正好提供了这一点，性能出色，设计直观。',
      'zh-TW':
        '作為一家成長中的新創公司，我們需要一個能夠與我們一起擴展的解決方案。這個平台正好提供了這一點，性能出色，設計直觀。',
    },
    author: {
      name: 'Michael Chen',
      title: 'Founder & CEO',
      company: 'InnovateLab',
      avatar: '/images/testimonials/michael-chen.jpg',
    },
    rating: 5,
    featured: true,
    category: 'startup',
    date: '2024-01-20',
  },
  {
    id: '3',
    content: {
      en: 'Our agency has been using this platform for over two years. The flexibility and customization options have allowed us to deliver exceptional results for our clients.',
      zh: '我们的代理机构使用这个平台已经超过两年了。灵活性和定制选项让我们能够为客户提供卓越的结果。',
      'zh-TW':
        '我們的代理機構使用這個平台已經超過兩年了。靈活性和定制選項讓我們能夠為客戶提供卓越的結果。',
    },
    author: {
      name: 'Emily Rodriguez',
      title: 'Creative Director',
      company: 'Digital Dynamics',
      avatar: '/images/testimonials/emily-rodriguez.jpg',
    },
    rating: 5,
    featured: true,
    category: 'agency',
    date: '2024-01-10',
  },
  {
    id: '4',
    content: {
      en: 'The implementation was seamless and the ROI has been incredible. Our team productivity increased by 40% within the first quarter of adoption.',
      zh: '实施过程非常顺利，投资回报率令人难以置信。我们团队的生产力在采用后的第一个季度就提高了40%。',
      'zh-TW':
        '實施過程非常順利，投資回報率令人難以置信。我們團隊的生產力在採用後的第一個季度就提高了40%。',
    },
    author: {
      name: 'David Kim',
      title: 'VP of Operations',
      company: 'Global Manufacturing Inc.',
      avatar: '/images/testimonials/david-kim.jpg',
    },
    rating: 5,
    featured: false,
    category: 'enterprise',
    date: '2024-01-25',
  },
  {
    id: '5',
    content: {
      en: 'Perfect for our small team. The user interface is clean and intuitive, and we were up and running in no time. Highly recommend for startups.',
      zh: '非常适合我们的小团队。用户界面简洁直观，我们很快就上手了。强烈推荐给初创公司。',
      'zh-TW':
        '非常適合我們的小團隊。用戶界面簡潔直觀，我們很快就上手了。強烈推薦給新創公司。',
    },
    author: {
      name: 'Lisa Wang',
      title: 'Product Manager',
      company: 'StartupHub',
      avatar: '/images/testimonials/lisa-wang.jpg',
    },
    rating: 4,
    featured: false,
    category: 'startup',
    date: '2024-02-01',
  },
  {
    id: '6',
    content: {
      en: 'We manage multiple client projects and this platform has streamlined our workflow significantly. The reporting features are particularly impressive.',
      zh: '我们管理多个客户项目，这个平台显著简化了我们的工作流程。报告功能特别令人印象深刻。',
      'zh-TW':
        '我們管理多個客戶項目，這個平台顯著簡化了我們的工作流程。報告功能特別令人印象深刻。',
    },
    author: {
      name: 'James Thompson',
      title: 'Account Director',
      company: 'Creative Solutions Agency',
      avatar: '/images/testimonials/james-thompson.jpg',
    },
    rating: 5,
    featured: false,
    category: 'agency',
    date: '2024-02-05',
  },
  {
    id: '7',
    content: {
      en: 'Security and compliance were our top concerns, and this platform exceeded our expectations. The enterprise-grade features are exactly what we needed.',
      zh: '安全性和合规性是我们最关心的问题，这个平台超出了我们的期望。企业级功能正是我们所需要的。',
      'zh-TW':
        '安全性和合規性是我們最關心的問題，這個平台超出了我們的期望。企業級功能正是我們所需要的。',
    },
    author: {
      name: 'Rachel Green',
      title: 'CISO',
      company: 'SecureBank Corp',
      avatar: '/images/testimonials/rachel-green.jpg',
    },
    rating: 5,
    featured: false,
    category: 'enterprise',
    date: '2024-02-10',
  },
  {
    id: '8',
    content: {
      en: 'The customer support is phenomenal. Every question is answered quickly and thoroughly. It feels like having an extended team member.',
      zh: '客户支持非常出色。每个问题都能得到快速而全面的回答。感觉就像有了一个扩展的团队成员。',
      'zh-TW':
        '客戶支援非常出色。每個問題都能得到快速而全面的回答。感覺就像有了一個擴展的團隊成員。',
    },
    author: {
      name: 'Alex Martinez',
      title: 'Technical Lead',
      company: 'DevStudio',
      avatar: '/images/testimonials/alex-martinez.jpg',
    },
    rating: 5,
    featured: false,
    category: 'startup',
    date: '2024-02-15',
  },
];

// Helper functions
export const getFeaturedTestimonials = (): Testimonial[] => {
  return testimonials.filter(testimonial => testimonial.featured);
};

export const getTestimonialsByCategory = (category: string): Testimonial[] => {
  if (category === 'all') {
    return testimonials;
  }
  return testimonials.filter(testimonial => testimonial.category === category);
};

export const getTestimonialById = (id: string): Testimonial | undefined => {
  return testimonials.find(testimonial => testimonial.id === id);
};

// Helper function for getting translated text
export const getTranslatedText = (
  translation: any,
  locale: string = 'en'
): string => {
  return translation[locale] || translation.en || '';
};

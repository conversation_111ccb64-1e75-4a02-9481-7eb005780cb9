import { PricingTier, PricingComparison } from '@/types/public/pricing';

export const pricingTiers: PricingTier[] = [
  {
    id: 'starter',
    name: {
      en: 'Starter',
      zh: '入门版',
      'zh-TW': '入門版',
    },
    description: {
      en: 'Perfect for individuals and small teams getting started',
      zh: '适合个人和小团队入门使用',
      'zh-TW': '適合個人和小團隊入門使用',
    },
    price: {
      monthly: 9,
      yearly: 90,
      currency: 'USD',
    },
    popular: false,
    badge: {
      en: 'Most Popular',
      zh: '最受欢迎',
      'zh-TW': '最受歡迎',
    },
    limits: {
      users: 5,
      storage: '10GB',
      projects: 10,
      support: 'Email',
    },
    features: [
      {
        id: 'users-5',
        name: {
          en: 'Up to 5 users',
          zh: '最多5个用户',
          'zh-TW': '最多5個用戶',
        },
        included: true,
      },
      {
        id: 'storage-10gb',
        name: {
          en: '10GB storage',
          zh: '10GB存储空间',
          'zh-TW': '10GB儲存空間',
        },
        included: true,
      },
      {
        id: 'projects-10',
        name: {
          en: '10 projects',
          zh: '10个项目',
          'zh-TW': '10個專案',
        },
        included: true,
      },
      {
        id: 'email-support',
        name: {
          en: 'Email support',
          zh: '邮件支持',
          'zh-TW': '郵件支援',
        },
        included: true,
      },
      {
        id: 'basic-analytics',
        name: {
          en: 'Basic analytics',
          zh: '基础分析',
          'zh-TW': '基礎分析',
        },
        included: true,
      },
      {
        id: 'api-access',
        name: {
          en: 'API access',
          zh: 'API访问',
          'zh-TW': 'API存取',
        },
        included: false,
      },
      {
        id: 'priority-support',
        name: {
          en: 'Priority support',
          zh: '优先支持',
          'zh-TW': '優先支援',
        },
        included: false,
      },
      {
        id: 'advanced-security',
        name: {
          en: 'Advanced security',
          zh: '高级安全',
          'zh-TW': '進階安全',
        },
        included: false,
      },
    ],
    cta: {
      text: {
        en: 'Get Started',
        zh: '开始使用',
        'zh-TW': '開始使用',
      },
      href: '/signup?plan=starter',
      variant: 'outline',
    },
  },
  {
    id: 'professional',
    name: {
      en: 'Professional',
      zh: '专业版',
      'zh-TW': '專業版',
    },
    description: {
      en: 'Ideal for growing teams and businesses',
      zh: '适合成长中的团队和企业',
      'zh-TW': '適合成長中的團隊和企業',
    },
    price: {
      monthly: 29,
      yearly: 290,
      currency: 'USD',
    },
    popular: true,
    badge: {
      en: 'Most Popular',
      zh: '最受欢迎',
      'zh-TW': '最受歡迎',
    },
    limits: {
      users: 25,
      storage: '100GB',
      projects: 50,
      support: 'Priority',
    },
    features: [
      {
        id: 'users-25',
        name: {
          en: 'Up to 25 users',
          zh: '最多25个用户',
          'zh-TW': '最多25個用戶',
        },
        included: true,
      },
      {
        id: 'storage-100gb',
        name: {
          en: '100GB storage',
          zh: '100GB存储空间',
          'zh-TW': '100GB儲存空間',
        },
        included: true,
      },
      {
        id: 'projects-50',
        name: {
          en: '50 projects',
          zh: '50个项目',
          'zh-TW': '50個專案',
        },
        included: true,
      },
      {
        id: 'priority-support',
        name: {
          en: 'Priority support',
          zh: '优先支持',
          'zh-TW': '優先支援',
        },
        included: true,
        highlight: true,
      },
      {
        id: 'advanced-analytics',
        name: {
          en: 'Advanced analytics',
          zh: '高级分析',
          'zh-TW': '進階分析',
        },
        included: true,
        highlight: true,
      },
      {
        id: 'api-access',
        name: {
          en: 'Full API access',
          zh: '完整API访问',
          'zh-TW': '完整API存取',
        },
        included: true,
        highlight: true,
      },
      {
        id: 'integrations',
        name: {
          en: 'Third-party integrations',
          zh: '第三方集成',
          'zh-TW': '第三方整合',
        },
        included: true,
      },
      {
        id: 'advanced-security',
        name: {
          en: 'Advanced security',
          zh: '高级安全',
          'zh-TW': '進階安全',
        },
        included: false,
      },
    ],
    cta: {
      text: {
        en: 'Start Free Trial',
        zh: '开始免费试用',
        'zh-TW': '開始免費試用',
      },
      href: '/signup?plan=professional',
      variant: 'default',
    },
  },
  {
    id: 'enterprise',
    name: {
      en: 'Enterprise',
      zh: '企业版',
      'zh-TW': '企業版',
    },
    description: {
      en: 'Advanced features for large organizations',
      zh: '为大型组织提供的高级功能',
      'zh-TW': '為大型組織提供的進階功能',
    },
    price: {
      monthly: 99,
      yearly: 990,
      currency: 'USD',
    },
    popular: false,
    limits: {
      users: 'unlimited',
      storage: '1TB',
      projects: 'unlimited',
      support: '24/7 Dedicated',
    },
    features: [
      {
        id: 'users-unlimited',
        name: {
          en: 'Unlimited users',
          zh: '无限用户',
          'zh-TW': '無限用戶',
        },
        included: true,
      },
      {
        id: 'storage-1tb',
        name: {
          en: '1TB storage',
          zh: '1TB存储空间',
          'zh-TW': '1TB儲存空間',
        },
        included: true,
      },
      {
        id: 'projects-unlimited',
        name: {
          en: 'Unlimited projects',
          zh: '无限项目',
          'zh-TW': '無限專案',
        },
        included: true,
      },
      {
        id: 'dedicated-support',
        name: {
          en: '24/7 dedicated support',
          zh: '24/7专属支持',
          'zh-TW': '24/7專屬支援',
        },
        included: true,
        highlight: true,
      },
      {
        id: 'advanced-security',
        name: {
          en: 'Enterprise security',
          zh: '企业级安全',
          'zh-TW': '企業級安全',
        },
        included: true,
        highlight: true,
      },
      {
        id: 'custom-integrations',
        name: {
          en: 'Custom integrations',
          zh: '自定义集成',
          'zh-TW': '自訂整合',
        },
        included: true,
        highlight: true,
      },
      {
        id: 'sso',
        name: {
          en: 'Single Sign-On (SSO)',
          zh: '单点登录(SSO)',
          'zh-TW': '單一登入(SSO)',
        },
        included: true,
      },
      {
        id: 'compliance',
        name: {
          en: 'Compliance & audit logs',
          zh: '合规性和审计日志',
          'zh-TW': '合規性和稽核日誌',
        },
        included: true,
      },
    ],
    cta: {
      text: {
        en: 'Contact Sales',
        zh: '联系销售',
        'zh-TW': '聯繫銷售',
      },
      href: '/contact?inquiry=enterprise',
      variant: 'outline',
    },
  },
];

export const pricingComparison: PricingComparison[] = [
  {
    category: {
      en: 'Core Features',
      zh: '核心功能',
      'zh-TW': '核心功能',
    },
    features: [
      {
        id: 'users',
        name: {
          en: 'Number of users',
          zh: '用户数量',
          'zh-TW': '用戶數量',
        },
        tiers: {
          starter: '5',
          professional: '25',
          enterprise: 'Unlimited',
        },
      },
      {
        id: 'storage',
        name: {
          en: 'Storage space',
          zh: '存储空间',
          'zh-TW': '儲存空間',
        },
        tiers: {
          starter: '10GB',
          professional: '100GB',
          enterprise: '1TB',
        },
      },
      {
        id: 'projects',
        name: {
          en: 'Projects',
          zh: '项目',
          'zh-TW': '專案',
        },
        tiers: {
          starter: '10',
          professional: '50',
          enterprise: 'Unlimited',
        },
      },
    ],
  },
  {
    category: {
      en: 'Analytics & Reporting',
      zh: '分析和报告',
      'zh-TW': '分析和報告',
    },
    features: [
      {
        id: 'basic-analytics',
        name: {
          en: 'Basic analytics',
          zh: '基础分析',
          'zh-TW': '基礎分析',
        },
        tiers: {
          starter: true,
          professional: true,
          enterprise: true,
        },
      },
      {
        id: 'advanced-analytics',
        name: {
          en: 'Advanced analytics',
          zh: '高级分析',
          'zh-TW': '進階分析',
        },
        tiers: {
          starter: false,
          professional: true,
          enterprise: true,
        },
      },
      {
        id: 'custom-reports',
        name: {
          en: 'Custom reports',
          zh: '自定义报告',
          'zh-TW': '自訂報告',
        },
        tiers: {
          starter: false,
          professional: false,
          enterprise: true,
        },
      },
    ],
  },
  {
    category: {
      en: 'Support & Security',
      zh: '支持和安全',
      'zh-TW': '支援和安全',
    },
    features: [
      {
        id: 'email-support',
        name: {
          en: 'Email support',
          zh: '邮件支持',
          'zh-TW': '郵件支援',
        },
        tiers: {
          starter: true,
          professional: true,
          enterprise: true,
        },
      },
      {
        id: 'priority-support',
        name: {
          en: 'Priority support',
          zh: '优先支持',
          'zh-TW': '優先支援',
        },
        tiers: {
          starter: false,
          professional: true,
          enterprise: true,
        },
      },
      {
        id: 'dedicated-support',
        name: {
          en: '24/7 dedicated support',
          zh: '24/7专属支持',
          'zh-TW': '24/7專屬支援',
        },
        tiers: {
          starter: false,
          professional: false,
          enterprise: true,
        },
      },
      {
        id: 'sso',
        name: {
          en: 'Single Sign-On (SSO)',
          zh: '单点登录',
          'zh-TW': '單一登入',
        },
        tiers: {
          starter: false,
          professional: false,
          enterprise: true,
        },
      },
    ],
  },
];

// Helper functions
export const getPricingTierById = (id: string): PricingTier | undefined => {
  return pricingTiers.find(tier => tier.id === id);
};

export const getPopularTier = (): PricingTier | undefined => {
  return pricingTiers.find(tier => tier.popular);
};

export const calculateYearlySavings = (tier: PricingTier): number => {
  const monthlyTotal = tier.price.monthly * 12;
  const yearlyPrice = tier.price.yearly;
  return monthlyTotal - yearlyPrice;
};

export const calculateYearlySavingsPercentage = (tier: PricingTier): number => {
  const monthlyTotal = tier.price.monthly * 12;
  const savings = calculateYearlySavings(tier);
  return Math.round((savings / monthlyTotal) * 100);
};

export const formatPrice = (
  price: number,
  currency: string = 'USD'
): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(price);
};

export const getTranslatedText = (
  translation: any,
  locale: string = 'en'
): string => {
  return translation[locale] || translation.en || '';
};

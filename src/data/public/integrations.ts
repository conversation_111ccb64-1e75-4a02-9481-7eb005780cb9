import { Integration, IntegrationCategory } from '@/types/public/integrations';

export const integrationCategories: IntegrationCategory[] = [
  {
    id: '1',
    name: {
      en: 'CRM Systems',
      zh: 'CRM系统',
      'zh-TW': 'CRM系統',
    },
    slug: 'crm',
    color: '#3B82F6',
    icon: 'users',
  },
  {
    id: '2',
    name: {
      en: 'Communication',
      zh: '通信工具',
      'zh-TW': '通信工具',
    },
    slug: 'communication',
    color: '#10B981',
    icon: 'message-circle',
  },
  {
    id: '3',
    name: {
      en: 'Development Tools',
      zh: '开发工具',
      'zh-TW': '開發工具',
    },
    slug: 'development',
    color: '#8B5CF6',
    icon: 'code',
  },
  {
    id: '4',
    name: {
      en: 'Analytics & Monitoring',
      zh: '分析与监控',
      'zh-TW': '分析與監控',
    },
    slug: 'analytics',
    color: '#F59E0B',
    icon: 'bar-chart',
  },
  {
    id: '5',
    name: {
      en: 'Payment Systems',
      zh: '支付系统',
      'zh-TW': '支付系統',
    },
    slug: 'payments',
    color: '#EF4444',
    icon: 'credit-card',
  },
  {
    id: '6',
    name: {
      en: 'Cloud Services',
      zh: '云服务',
      'zh-TW': '雲服務',
    },
    slug: 'cloud',
    color: '#06B6D4',
    icon: 'cloud',
  },
  {
    id: '7',
    name: {
      en: 'Marketing Tools',
      zh: '营销工具',
      'zh-TW': '營銷工具',
    },
    slug: 'marketing',
    color: '#EC4899',
    icon: 'megaphone',
  },
  {
    id: '8',
    name: {
      en: 'Productivity',
      zh: '生产力工具',
      'zh-TW': '生產力工具',
    },
    slug: 'productivity',
    color: '#84CC16',
    icon: 'zap',
  },
];

export const integrations: Integration[] = [
  {
    id: '1',
    slug: 'salesforce',
    name: 'Salesforce',
    description: {
      en: 'Integrate with Salesforce CRM to sync customer data, leads, and opportunities. Automatically update records and trigger workflows based on platform activities.',
      zh: '与Salesforce CRM集成，同步客户数据、潜在客户和商机。根据平台活动自动更新记录并触发工作流。',
      'zh-TW':
        '與Salesforce CRM集成，同步客戶數據、潛在客戶和商機。根據平台活動自動更新記錄並觸發工作流。',
    },
    shortDescription: {
      en: 'Sync customer data and automate workflows with Salesforce CRM',
      zh: '与Salesforce CRM同步客户数据并自动化工作流',
      'zh-TW': '與Salesforce CRM同步客戶數據並自動化工作流',
    },
    logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/salesforce/salesforce-original.svg',
    category: integrationCategories[0], // CRM
    tags: ['crm', 'sales', 'automation', 'enterprise'],
    featured: true,
    popular: true,
    setupDifficulty: 'medium',
    documentationUrl: 'https://docs.example.com/integrations/salesforce',
    setupGuideUrl: 'https://docs.example.com/guides/salesforce-setup',
    apiDocsUrl: 'https://docs.example.com/api/salesforce',
    features: [
      {
        en: 'Bi-directional data sync',
        zh: '双向数据同步',
        'zh-TW': '雙向數據同步',
      },
      {
        en: 'Real-time webhook notifications',
        zh: '实时webhook通知',
        'zh-TW': '實時webhook通知',
      },
      {
        en: 'Custom field mapping',
        zh: '自定义字段映射',
        'zh-TW': '自定義字段映射',
      },
      {
        en: 'Automated workflow triggers',
        zh: '自动化工作流触发器',
        'zh-TW': '自動化工作流觸發器',
      },
    ],
    requirements: [
      {
        en: 'Salesforce Professional edition or higher',
        zh: 'Salesforce专业版或更高版本',
        'zh-TW': 'Salesforce專業版或更高版本',
      },
      {
        en: 'API access permissions',
        zh: 'API访问权限',
        'zh-TW': 'API訪問權限',
      },
    ],
    pricing: {
      free: false,
      paidPlans: true,
      startingPrice: '$29/month',
    },
    status: 'active',
    lastUpdated: '2024-01-15T10:00:00Z',
    integrationCount: 15420,
  },
  {
    id: '2',
    slug: 'slack',
    name: 'Slack',
    description: {
      en: 'Get real-time notifications and manage your platform directly from Slack. Create channels for specific projects and receive automated updates.',
      zh: '从Slack获取实时通知并直接管理您的平台。为特定项目创建频道并接收自动更新。',
      'zh-TW':
        '從Slack獲取實時通知並直接管理您的平台。為特定項目創建頻道並接收自動更新。',
    },
    shortDescription: {
      en: 'Real-time notifications and platform management in Slack',
      zh: '在Slack中获取实时通知和平台管理',
      'zh-TW': '在Slack中獲取實時通知和平台管理',
    },
    logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/slack/slack-original.svg',
    category: integrationCategories[1], // Communication
    tags: ['communication', 'notifications', 'collaboration', 'productivity'],
    featured: true,
    popular: true,
    setupDifficulty: 'easy',
    documentationUrl: 'https://docs.example.com/integrations/slack',
    setupGuideUrl: 'https://docs.example.com/guides/slack-setup',
    features: [
      {
        en: 'Real-time notifications',
        zh: '实时通知',
        'zh-TW': '實時通知',
      },
      {
        en: 'Interactive message buttons',
        zh: '交互式消息按钮',
        'zh-TW': '交互式消息按鈕',
      },
      {
        en: 'Custom slash commands',
        zh: '自定义斜杠命令',
        'zh-TW': '自定義斜杠命令',
      },
      {
        en: 'Channel-specific updates',
        zh: '频道特定更新',
        'zh-TW': '頻道特定更新',
      },
    ],
    pricing: {
      free: true,
      paidPlans: false,
    },
    status: 'active',
    lastUpdated: '2024-01-20T14:30:00Z',
    integrationCount: 28750,
  },
  {
    id: '3',
    slug: 'github',
    name: 'GitHub',
    description: {
      en: 'Connect your GitHub repositories to automatically sync code changes, track issues, and manage pull requests directly from the platform.',
      zh: '连接您的GitHub存储库以自动同步代码更改、跟踪问题并直接从平台管理拉取请求。',
      'zh-TW':
        '連接您的GitHub存儲庫以自動同步代碼更改、跟踪問題並直接從平台管理拉取請求。',
    },
    shortDescription: {
      en: 'Sync repositories and manage development workflow',
      zh: '同步存储库并管理开发工作流',
      'zh-TW': '同步存儲庫並管理開發工作流',
    },
    logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/github/github-original.svg',
    category: integrationCategories[2], // Development
    tags: ['git', 'development', 'version-control', 'collaboration'],
    featured: true,
    popular: true,
    setupDifficulty: 'easy',
    documentationUrl: 'https://docs.example.com/integrations/github',
    setupGuideUrl: 'https://docs.example.com/guides/github-setup',
    features: [
      {
        en: 'Repository synchronization',
        zh: '存储库同步',
        'zh-TW': '存儲庫同步',
      },
      {
        en: 'Issue tracking integration',
        zh: '问题跟踪集成',
        'zh-TW': '問題跟踪集成',
      },
      {
        en: 'Pull request management',
        zh: '拉取请求管理',
        'zh-TW': '拉取請求管理',
      },
      {
        en: 'Automated deployments',
        zh: '自动化部署',
        'zh-TW': '自動化部署',
      },
    ],
    pricing: {
      free: true,
      paidPlans: false,
    },
    status: 'active',
    lastUpdated: '2024-01-18T09:15:00Z',
    integrationCount: 22100,
  },
  {
    id: '4',
    slug: 'google-analytics',
    name: 'Google Analytics',
    description: {
      en: 'Track user behavior and platform performance with Google Analytics integration. Get detailed insights into user engagement and conversion metrics.',
      zh: '通过Google Analytics集成跟踪用户行为和平台性能。获得有关用户参与度和转化指标的详细见解。',
      'zh-TW':
        '通過Google Analytics集成跟踪用戶行為和平台性能。獲得有關用戶參與度和轉化指標的詳細見解。',
    },
    shortDescription: {
      en: 'Track user behavior and platform analytics',
      zh: '跟踪用户行为和平台分析',
      'zh-TW': '跟踪用戶行為和平台分析',
    },
    logo: 'https://www.google.com/analytics/static/img/analytics-logo.svg',
    category: integrationCategories[3], // Analytics
    tags: ['analytics', 'tracking', 'metrics', 'insights'],
    featured: false,
    popular: true,
    setupDifficulty: 'medium',
    documentationUrl: 'https://docs.example.com/integrations/google-analytics',
    setupGuideUrl: 'https://docs.example.com/guides/google-analytics-setup',
    features: [
      {
        en: 'Event tracking',
        zh: '事件跟踪',
        'zh-TW': '事件跟踪',
      },
      {
        en: 'Custom dimensions',
        zh: '自定义维度',
        'zh-TW': '自定義維度',
      },
      {
        en: 'Goal conversion tracking',
        zh: '目标转化跟踪',
        'zh-TW': '目標轉化跟踪',
      },
      {
        en: 'Real-time reporting',
        zh: '实时报告',
        'zh-TW': '實時報告',
      },
    ],
    pricing: {
      free: true,
      paidPlans: true,
      startingPrice: 'Free',
    },
    status: 'active',
    lastUpdated: '2024-01-12T16:45:00Z',
    integrationCount: 18900,
  },
  {
    id: '5',
    slug: 'stripe',
    name: 'Stripe',
    description: {
      en: 'Process payments securely with Stripe integration. Handle subscriptions, one-time payments, and manage customer billing information.',
      zh: '通过Stripe集成安全处理付款。处理订阅、一次性付款并管理客户账单信息。',
      'zh-TW':
        '通過Stripe集成安全處理付款。處理訂閱、一次性付款並管理客戶賬單信息。',
    },
    shortDescription: {
      en: 'Secure payment processing and subscription management',
      zh: '安全的支付处理和订阅管理',
      'zh-TW': '安全的支付處理和訂閱管理',
    },
    logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/stripe/stripe-original.svg',
    category: integrationCategories[4], // Payments
    tags: ['payments', 'billing', 'subscriptions', 'e-commerce'],
    featured: true,
    popular: true,
    setupDifficulty: 'medium',
    documentationUrl: 'https://docs.example.com/integrations/stripe',
    setupGuideUrl: 'https://docs.example.com/guides/stripe-setup',
    features: [
      {
        en: 'Secure payment processing',
        zh: '安全支付处理',
        'zh-TW': '安全支付處理',
      },
      {
        en: 'Subscription management',
        zh: '订阅管理',
        'zh-TW': '訂閱管理',
      },
      {
        en: 'Webhook notifications',
        zh: 'Webhook通知',
        'zh-TW': 'Webhook通知',
      },
      {
        en: 'Multi-currency support',
        zh: '多币种支持',
        'zh-TW': '多幣種支持',
      },
    ],
    requirements: [
      {
        en: 'Stripe account with API access',
        zh: '具有API访问权限的Stripe账户',
        'zh-TW': '具有API訪問權限的Stripe賬戶',
      },
    ],
    pricing: {
      free: false,
      paidPlans: true,
      startingPrice: '2.9% + 30¢ per transaction',
    },
    status: 'active',
    lastUpdated: '2024-01-22T11:20:00Z',
    integrationCount: 12650,
  },
  {
    id: '6',
    slug: 'aws',
    name: 'Amazon Web Services',
    description: {
      en: 'Deploy and scale your applications using AWS cloud services. Integrate with S3, Lambda, RDS, and other AWS services for robust infrastructure.',
      zh: '使用AWS云服务部署和扩展您的应用程序。与S3、Lambda、RDS和其他AWS服务集成以获得强大的基础设施。',
      'zh-TW':
        '使用AWS雲服務部署和擴展您的應用程序。與S3、Lambda、RDS和其他AWS服務集成以獲得強大的基礎設施。',
    },
    shortDescription: {
      en: 'Cloud infrastructure and services integration',
      zh: '云基础设施和服务集成',
      'zh-TW': '雲基礎設施和服務集成',
    },
    logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/amazonwebservices/amazonwebservices-original.svg',
    category: integrationCategories[5], // Cloud
    tags: ['cloud', 'infrastructure', 'storage', 'compute'],
    featured: true,
    popular: false,
    setupDifficulty: 'advanced',
    documentationUrl: 'https://docs.example.com/integrations/aws',
    setupGuideUrl: 'https://docs.example.com/guides/aws-setup',
    features: [
      {
        en: 'S3 storage integration',
        zh: 'S3存储集成',
        'zh-TW': 'S3存儲集成',
      },
      {
        en: 'Lambda function triggers',
        zh: 'Lambda函数触发器',
        'zh-TW': 'Lambda函數觸發器',
      },
      {
        en: 'RDS database connectivity',
        zh: 'RDS数据库连接',
        'zh-TW': 'RDS數據庫連接',
      },
      {
        en: 'CloudWatch monitoring',
        zh: 'CloudWatch监控',
        'zh-TW': 'CloudWatch監控',
      },
    ],
    requirements: [
      {
        en: 'AWS account with appropriate IAM permissions',
        zh: '具有适当IAM权限的AWS账户',
        'zh-TW': '具有適當IAM權限的AWS賬戶',
      },
    ],
    pricing: {
      free: false,
      paidPlans: true,
      startingPrice: 'Pay-as-you-use',
    },
    status: 'active',
    lastUpdated: '2024-01-10T08:30:00Z',
    integrationCount: 8750,
  },
  {
    id: '7',
    slug: 'mailchimp',
    name: 'Mailchimp',
    description: {
      en: 'Sync customer data with Mailchimp for email marketing campaigns. Automatically add subscribers and trigger email sequences based on platform events.',
      zh: '将客户数据与Mailchimp同步以进行电子邮件营销活动。根据平台事件自动添加订阅者并触发电子邮件序列。',
      'zh-TW':
        '將客戶數據與Mailchimp同步以進行電子郵件營銷活動。根據平台事件自動添加訂閱者並觸發電子郵件序列。',
    },
    shortDescription: {
      en: 'Email marketing automation and subscriber management',
      zh: '电子邮件营销自动化和订阅者管理',
      'zh-TW': '電子郵件營銷自動化和訂閱者管理',
    },
    logo: 'https://cdn.worldvectorlogo.com/logos/mailchimp-freddie-icon.svg',
    category: integrationCategories[6], // Marketing
    tags: ['email', 'marketing', 'automation', 'campaigns'],
    featured: false,
    popular: true,
    setupDifficulty: 'easy',
    documentationUrl: 'https://docs.example.com/integrations/mailchimp',
    setupGuideUrl: 'https://docs.example.com/guides/mailchimp-setup',
    features: [
      {
        en: 'Subscriber synchronization',
        zh: '订阅者同步',
        'zh-TW': '訂閱者同步',
      },
      {
        en: 'Automated email sequences',
        zh: '自动化电子邮件序列',
        'zh-TW': '自動化電子郵件序列',
      },
      {
        en: 'Campaign performance tracking',
        zh: '活动效果跟踪',
        'zh-TW': '活動效果跟踪',
      },
      {
        en: 'Audience segmentation',
        zh: '受众细分',
        'zh-TW': '受眾細分',
      },
    ],
    pricing: {
      free: true,
      paidPlans: true,
      startingPrice: 'Free up to 500 contacts',
    },
    status: 'active',
    lastUpdated: '2024-01-14T13:15:00Z',
    integrationCount: 16200,
  },
  {
    id: '8',
    slug: 'notion',
    name: 'Notion',
    description: {
      en: 'Create and update Notion pages automatically based on platform activities. Sync project data and maintain documentation in real-time.',
      zh: '根据平台活动自动创建和更新Notion页面。同步项目数据并实时维护文档。',
      'zh-TW':
        '根據平台活動自動創建和更新Notion頁面。同步項目數據並實時維護文檔。',
    },
    shortDescription: {
      en: 'Automated documentation and project management',
      zh: '自动化文档和项目管理',
      'zh-TW': '自動化文檔和項目管理',
    },
    logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/notion/notion-original.svg',
    category: integrationCategories[7], // Productivity
    tags: ['documentation', 'productivity', 'collaboration', 'notes'],
    featured: false,
    popular: false,
    setupDifficulty: 'medium',
    documentationUrl: 'https://docs.example.com/integrations/notion',
    setupGuideUrl: 'https://docs.example.com/guides/notion-setup',
    features: [
      {
        en: 'Page creation and updates',
        zh: '页面创建和更新',
        'zh-TW': '頁面創建和更新',
      },
      {
        en: 'Database synchronization',
        zh: '数据库同步',
        'zh-TW': '數據庫同步',
      },
      {
        en: 'Template automation',
        zh: '模板自动化',
        'zh-TW': '模板自動化',
      },
      {
        en: 'Real-time collaboration',
        zh: '实时协作',
        'zh-TW': '實時協作',
      },
    ],
    pricing: {
      free: true,
      paidPlans: false,
    },
    status: 'beta',
    lastUpdated: '2024-01-08T10:45:00Z',
    integrationCount: 3420,
  },
  {
    id: '9',
    slug: 'microsoft-teams',
    name: 'Microsoft Teams',
    description: {
      en: 'Get notifications and manage your platform from Microsoft Teams. Create custom tabs and bots for enhanced team collaboration.',
      zh: '从Microsoft Teams获取通知并管理您的平台。创建自定义选项卡和机器人以增强团队协作。',
      'zh-TW':
        '從Microsoft Teams獲取通知並管理您的平台。創建自定義選項卡和機器人以增強團隊協作。',
    },
    shortDescription: {
      en: 'Team collaboration and platform management',
      zh: '团队协作和平台管理',
      'zh-TW': '團隊協作和平台管理',
    },
    logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/microsoftteams/microsoftteams-original.svg',
    category: integrationCategories[1], // Communication
    tags: ['communication', 'collaboration', 'microsoft', 'enterprise'],
    featured: false,
    popular: true,
    setupDifficulty: 'medium',
    documentationUrl: 'https://docs.example.com/integrations/microsoft-teams',
    setupGuideUrl: 'https://docs.example.com/guides/teams-setup',
    features: [
      {
        en: 'Custom Teams tabs',
        zh: '自定义Teams选项卡',
        'zh-TW': '自定義Teams選項卡',
      },
      {
        en: 'Bot notifications',
        zh: '机器人通知',
        'zh-TW': '機器人通知',
      },
      {
        en: 'File sharing integration',
        zh: '文件共享集成',
        'zh-TW': '文件共享集成',
      },
      {
        en: 'Meeting integration',
        zh: '会议集成',
        'zh-TW': '會議集成',
      },
    ],
    requirements: [
      {
        en: 'Microsoft 365 subscription',
        zh: 'Microsoft 365订阅',
        'zh-TW': 'Microsoft 365訂閱',
      },
    ],
    pricing: {
      free: false,
      paidPlans: true,
      startingPrice: 'Included with Microsoft 365',
    },
    status: 'active',
    lastUpdated: '2024-01-16T15:20:00Z',
    integrationCount: 9850,
  },
  {
    id: '10',
    slug: 'zapier',
    name: 'Zapier',
    description: {
      en: 'Connect with thousands of apps through Zapier automation. Create custom workflows and automate repetitive tasks without coding.',
      zh: '通过Zapier自动化连接数千个应用程序。创建自定义工作流并自动化重复任务，无需编码。',
      'zh-TW':
        '通過Zapier自動化連接數千個應用程序。創建自定義工作流並自動化重複任務，無需編碼。',
    },
    shortDescription: {
      en: 'No-code automation with thousands of apps',
      zh: '与数千个应用程序的无代码自动化',
      'zh-TW': '與數千個應用程序的無代碼自動化',
    },
    logo: 'https://cdn.worldvectorlogo.com/logos/zapier.svg',
    category: integrationCategories[7], // Productivity
    tags: ['automation', 'workflow', 'no-code', 'productivity'],
    featured: true,
    popular: true,
    setupDifficulty: 'easy',
    documentationUrl: 'https://docs.example.com/integrations/zapier',
    setupGuideUrl: 'https://docs.example.com/guides/zapier-setup',
    features: [
      {
        en: 'Multi-step workflows',
        zh: '多步骤工作流',
        'zh-TW': '多步驟工作流',
      },
      {
        en: 'Conditional logic',
        zh: '条件逻辑',
        'zh-TW': '條件邏輯',
      },
      {
        en: 'Data transformation',
        zh: '数据转换',
        'zh-TW': '數據轉換',
      },
      {
        en: '5000+ app connections',
        zh: '5000+应用程序连接',
        'zh-TW': '5000+應用程序連接',
      },
    ],
    pricing: {
      free: true,
      paidPlans: true,
      startingPrice: 'Free for 5 Zaps',
    },
    status: 'active',
    lastUpdated: '2024-01-19T12:00:00Z',
    integrationCount: 31500,
  },
];

// Helper functions
export const getFeaturedIntegrations = (): Integration[] => {
  return integrations.filter(integration => integration.featured);
};

export const getPopularIntegrations = (): Integration[] => {
  return integrations.filter(integration => integration.popular);
};

export const getIntegrationsByCategory = (
  categorySlug: string
): Integration[] => {
  return integrations.filter(
    integration => integration.category.slug === categorySlug
  );
};

export const getIntegrationsByDifficulty = (
  difficulty: string
): Integration[] => {
  return integrations.filter(
    integration => integration.setupDifficulty === difficulty
  );
};

export const getIntegrationsByStatus = (status: string): Integration[] => {
  return integrations.filter(integration => integration.status === status);
};

export const searchIntegrations = (query: string): Integration[] => {
  const lowercaseQuery = query.toLowerCase();

  return integrations.filter(
    integration =>
      integration.name.toLowerCase().includes(lowercaseQuery) ||
      integration.shortDescription.en.toLowerCase().includes(lowercaseQuery) ||
      integration.tags.some(tag => tag.toLowerCase().includes(lowercaseQuery))
  );
};

export const getIntegrationBySlug = (slug: string): Integration | undefined => {
  return integrations.find(integration => integration.slug === slug);
};

export const getRelatedIntegrations = (
  currentIntegration: Integration,
  limit: number = 3
): Integration[] => {
  return integrations
    .filter(
      integration =>
        integration.id !== currentIntegration.id &&
        (integration.category.id === currentIntegration.category.id ||
          integration.tags.some(tag => currentIntegration.tags.includes(tag)))
    )
    .slice(0, limit);
};

export const getMostPopularIntegrations = (
  limit: number = 5
): Integration[] => {
  return [...integrations]
    .sort((a, b) => (b.integrationCount || 0) - (a.integrationCount || 0))
    .slice(0, limit);
};

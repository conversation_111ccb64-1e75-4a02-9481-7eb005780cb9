/**
 * Team Data
 *
 * Contains all team-related data including:
 * - Team members with their details
 * - Department information
 */

import { TeamMember, Department } from '@/types/public/team';

// Team members data
export const teamMembers: TeamMember[] = [
  {
    id: 1,
    name: '<PERSON>',
    position: 'Chief Executive Officer',
    bio: '<PERSON> has over 15 years of industry experience and has led the company since its founding. Her vision and leadership have been instrumental in our growth and success.',
    imageUrl: '/images/team/jane-doe.jpg', // This would be a real image path in a production app
  },
  {
    id: 2,
    name: '<PERSON>',
    position: 'Chief Technology Officer',
    bio: 'With a background in software engineering and cloud architecture, <PERSON> oversees all technical aspects of our platform. He ensures we stay at the cutting edge of technology.',
    imageUrl: '/images/team/john-smith.jpg',
  },
  {
    id: 3,
    name: '<PERSON>',
    position: 'Chief Product Officer',
    bio: '<PERSON> brings her expertise in user experience and product development to create intuitive, user-friendly solutions that solve real business problems.',
    imageUrl: '/images/team/emily-johnson.jpg',
  },
  {
    id: 4,
    name: '<PERSON>',
    position: 'Chief Financial Officer',
    bio: "<PERSON>'s financial acumen has helped steer the company through rapid growth while maintaining financial health and stability.",
    imageUrl: '/images/team/michael-chen.jpg',
  },
  {
    id: 5,
    name: '<PERSON> <PERSON>',
    position: 'VP of Marketing',
    bio: '<PERSON> leads our marketing efforts with a focus on brand development and customer acquisition. Her innovative campaigns have significantly increased our market presence.',
    imageUrl: '/images/team/sarah-williams.jpg',
  },
  {
    id: 6,
    name: '<PERSON> <PERSON>',
    position: 'VP of Customer Success',
    bio: 'David is dedicated to ensuring our customers achieve their goals with our products. His team sets the standard for excellent customer support and relationship management.',
    imageUrl: '/images/team/david-patel.jpg',
  },
];

// Department information
export const departments: Department[] = [
  {
    name: 'Engineering',
    description:
      'Our engineering team builds and maintains our core products with a focus on quality, performance, and innovation.',
    memberCount: 25,
    iconType: 'code',
  },
  {
    name: 'Product',
    description:
      'The product team focuses on user experience and design, ensuring our solutions meet the needs of our customers.',
    memberCount: 15,
    iconType: 'sliders',
  },
  {
    name: 'Marketing',
    description:
      'Our marketing team shares our story and communicates the value of our products to the world.',
    memberCount: 10,
    iconType: 'megaphone',
  },
];

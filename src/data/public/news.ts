import { NewsArticle, NewsCategory, NewsAuthor } from '@/types/public/news';

export const newsCategories: NewsCategory[] = [
  {
    id: '1',
    name: {
      en: 'Product Updates',
      zh: '产品更新',
      'zh-TW': '產品更新',
    },
    slug: 'product-updates',
  },
  {
    id: '2',
    name: {
      en: 'Company News',
      zh: '公司新闻',
      'zh-TW': '公司新聞',
    },
    slug: 'company-news',
  },
  {
    id: '3',
    name: {
      en: 'Industry Insights',
      zh: '行业洞察',
      'zh-TW': '行業洞察',
    },
    slug: 'industry-insights',
  },
  {
    id: '4',
    name: {
      en: 'Press Releases',
      zh: '新闻发布',
      'zh-TW': '新聞發布',
    },
    slug: 'press-releases',
  },
];

export const newsAuthors: <AUTHORS>
  {
    id: '1',
    name: '<PERSON>',
    title: {
      en: 'Head of Product',
      zh: '产品负责人',
      'zh-TW': '產品負責人',
    },
    avatar:
      'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
    bio: {
      en: 'Sarah leads our product development team with over 10 years of experience in enterprise software.',
      zh: '莎拉领导我们的产品开发团队，在企业软件方面拥有超过10年的经验。',
      'zh-TW': '莎拉領導我們的產品開發團隊，在企業軟體方面擁有超過10年的經驗。',
    },
  },
  {
    id: '2',
    name: 'Michael Chen',
    title: {
      en: 'Chief Technology Officer',
      zh: '首席技术官',
      'zh-TW': '首席技術官',
    },
    avatar:
      'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
    bio: {
      en: 'Michael oversees our technical strategy and innovation initiatives.',
      zh: '迈克尔负责监督我们的技术战略和创新举措。',
      'zh-TW': '邁克爾負責監督我們的技術戰略和創新舉措。',
    },
  },
  {
    id: '3',
    name: 'Emily Rodriguez',
    title: {
      en: 'VP of Marketing',
      zh: '营销副总裁',
      'zh-TW': '行銷副總裁',
    },
    avatar:
      'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
    bio: {
      en: 'Emily drives our marketing strategy and brand communications.',
      zh: '艾米丽推动我们的营销策略和品牌传播。',
      'zh-TW': '艾米麗推動我們的行銷策略和品牌傳播。',
    },
  },
];

export const newsArticles: NewsArticle[] = [
  {
    id: '1',
    slug: 'introducing-new-dashboard-features',
    title: {
      en: 'Introducing New Dashboard Features for Enhanced User Experience',
      zh: '推出新的仪表板功能以增强用户体验',
      'zh-TW': '推出新的儀表板功能以增強用戶體驗',
    },
    excerpt: {
      en: 'We are excited to announce the latest updates to our dashboard, featuring improved analytics, customizable widgets, and enhanced performance monitoring.',
      zh: '我们很高兴宣布仪表板的最新更新，包括改进的分析、可定制的小部件和增强的性能监控。',
      'zh-TW':
        '我們很高興宣布儀表板的最新更新，包括改進的分析、可定制的小部件和增強的性能監控。',
    },
    content: {
      en: `# Introducing New Dashboard Features

We are thrilled to announce the release of our enhanced dashboard with powerful new features designed to improve your workflow and productivity.

## Key Features

### Advanced Analytics
Our new analytics engine provides deeper insights into your data with real-time reporting and customizable metrics.

### Customizable Widgets
Create personalized dashboards with drag-and-drop widgets that display the information most important to you.

### Performance Monitoring
Monitor system performance with detailed metrics and alerts to ensure optimal operation.

## Getting Started

To access these new features, simply log into your account and navigate to the dashboard. The new interface will guide you through the setup process.

## What's Next

We're continuing to invest in improving your experience with more features planned for the coming months.`,
      zh: `# 推出新的仪表板功能

我们很高兴宣布发布增强的仪表板，具有强大的新功能，旨在改善您的工作流程和生产力。

## 主要功能

### 高级分析
我们的新分析引擎通过实时报告和可定制指标为您的数据提供更深入的洞察。

### 可定制小部件
使用拖放小部件创建个性化仪表板，显示对您最重要的信息。

### 性能监控
通过详细的指标和警报监控系统性能，确保最佳运行。

## 入门指南

要访问这些新功能，只需登录您的帐户并导航到仪表板。新界面将指导您完成设置过程。

## 下一步计划

我们将继续投资改善您的体验，计划在未来几个月推出更多功能。`,
      'zh-TW': `# 推出新的儀表板功能

我們很高興宣布發布增強的儀表板，具有強大的新功能，旨在改善您的工作流程和生產力。

## 主要功能

### 高級分析
我們的新分析引擎通過實時報告和可定制指標為您的數據提供更深入的洞察。

### 可定制小部件
使用拖放小部件創建個性化儀表板，顯示對您最重要的信息。

### 性能監控
通過詳細的指標和警報監控系統性能，確保最佳運行。

## 入門指南

要訪問這些新功能，只需登錄您的帳戶並導航到儀表板。新界面將指導您完成設置過程。

## 下一步計劃

我們將繼續投資改善您的體驗，計劃在未來幾個月推出更多功能。`,
    },
    featuredImage:
      'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=800&h=400&fit=crop&crop=center',
    category: newsCategories[0],
    author: newsAuthors[0],
    publishedAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z',
    tags: ['dashboard', 'features', 'analytics', 'ui-ux'],
    readTime: 5,
    featured: true,
    status: 'published',
  },
  {
    id: '2',
    slug: 'company-expansion-announcement',
    title: {
      en: 'Company Expansion: Opening New Offices in Asia-Pacific Region',
      zh: '公司扩张：在亚太地区开设新办事处',
      'zh-TW': '公司擴張：在亞太地區開設新辦事處',
    },
    excerpt: {
      en: 'We are pleased to announce our expansion into the Asia-Pacific region with new offices in Singapore and Tokyo to better serve our growing customer base.',
      zh: '我们很高兴宣布我们扩展到亚太地区，在新加坡和东京设立新办事处，以更好地服务我们不断增长的客户群。',
      'zh-TW':
        '我們很高興宣布我們擴展到亞太地區，在新加坡和東京設立新辦事處，以更好地服務我們不斷增長的客戶群。',
    },
    content: {
      en: `# Company Expansion Announcement

We are excited to share that our company is expanding its global presence with new offices in the Asia-Pacific region.

## New Locations

### Singapore Office
Our Singapore office will serve as the regional headquarters for Southeast Asia, providing local support and services.

### Tokyo Office
The Tokyo office will focus on the Japanese market and serve as a hub for innovation and partnerships.

## What This Means for Our Customers

- Improved local support and faster response times
- Better understanding of regional market needs
- Enhanced collaboration opportunities
- Localized services and solutions

## Our Commitment

This expansion reflects our commitment to serving our global customers better and investing in long-term growth.`,
      zh: `# 公司扩张公告

我们很高兴地分享我们公司正在扩大其全球业务，在亚太地区设立新办事处。

## 新地点

### 新加坡办事处
我们的新加坡办事处将作为东南亚的区域总部，提供本地支持和服务。

### 东京办事处
东京办事处将专注于日本市场，并作为创新和合作伙伴关系的中心。

## 对我们客户的意义

- 改善本地支持和更快的响应时间
- 更好地了解区域市场需求
- 增强合作机会
- 本地化服务和解决方案

## 我们的承诺

这次扩张反映了我们更好地服务全球客户和投资长期增长的承诺。`,
      'zh-TW': `# 公司擴張公告

我們很高興地分享我們公司正在擴大其全球業務，在亞太地區設立新辦事處。

## 新地點

### 新加坡辦事處
我們的新加坡辦事處將作為東南亞的區域總部，提供本地支持和服務。

### 東京辦事處
東京辦事處將專注於日本市場，並作為創新和合作夥伴關係的中心。

## 對我們客戶的意義

- 改善本地支持和更快的響應時間
- 更好地了解區域市場需求
- 增強合作機會
- 本地化服務和解決方案

## 我們的承諾

這次擴張反映了我們更好地服務全球客戶和投資長期增長的承諾。`,
    },
    featuredImage:
      'https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=800&h=400&fit=crop&crop=center',
    category: newsCategories[1],
    author: newsAuthors[2],
    publishedAt: '2024-01-10T09:00:00Z',
    updatedAt: '2024-01-10T09:00:00Z',
    tags: ['expansion', 'asia-pacific', 'offices', 'growth'],
    readTime: 4,
    featured: true,
    status: 'published',
  },
  {
    id: '3',
    slug: 'ai-integration-trends-2024',
    title: {
      en: 'AI Integration Trends Shaping Enterprise Software in 2024',
      zh: '2024年塑造企业软件的AI集成趋势',
      'zh-TW': '2024年塑造企業軟體的AI整合趨勢',
    },
    excerpt: {
      en: 'Explore the latest trends in AI integration and how they are transforming enterprise software development and user experiences.',
      zh: '探索AI集成的最新趋势以及它们如何改变企业软件开发和用户体验。',
      'zh-TW': '探索AI整合的最新趨勢以及它們如何改變企業軟體開發和用戶體驗。',
    },
    content: {
      en: `# AI Integration Trends Shaping Enterprise Software in 2024

Artificial Intelligence continues to revolutionize how we build and interact with enterprise software. Here are the key trends we're seeing this year.

## Key Trends

### Intelligent Automation
AI-powered automation is becoming more sophisticated, handling complex workflows and decision-making processes.

### Natural Language Processing
Advanced NLP capabilities are making software more accessible through conversational interfaces.

### Predictive Analytics
Machine learning models are providing better insights and forecasting capabilities.

### Personalization at Scale
AI is enabling highly personalized user experiences across large user bases.

## Impact on Development

These trends are changing how we approach software development, requiring new skills and methodologies.

## Looking Forward

The integration of AI will continue to accelerate, creating new opportunities and challenges for enterprise software.`,
      zh: `# 2024年塑造企业软件的AI集成趋势

人工智能继续革命性地改变我们构建和与企业软件交互的方式。以下是我们今年看到的关键趋势。

## 关键趋势

### 智能自动化
AI驱动的自动化变得更加复杂，处理复杂的工作流程和决策过程。

### 自然语言处理
先进的NLP功能通过对话界面使软件更易于访问。

### 预测分析
机器学习模型提供更好的洞察和预测能力。

### 规模化个性化
AI正在为大型用户群体提供高度个性化的用户体验。

## 对开发的影响

这些趋势正在改变我们处理软件开发的方式，需要新的技能和方法论。

## 展望未来

AI的集成将继续加速，为企业软件创造新的机遇和挑战。`,
      'zh-TW': `# 2024年塑造企業軟體的AI整合趨勢

人工智慧繼續革命性地改變我們構建和與企業軟體交互的方式。以下是我們今年看到的關鍵趨勢。

## 關鍵趨勢

### 智能自動化
AI驅動的自動化變得更加複雜，處理複雜的工作流程和決策過程。

### 自然語言處理
先進的NLP功能通過對話界面使軟體更易於訪問。

### 預測分析
機器學習模型提供更好的洞察和預測能力。

### 規模化個性化
AI正在為大型用戶群體提供高度個性化的用戶體驗。

## 對開發的影響

這些趨勢正在改變我們處理軟體開發的方式，需要新的技能和方法論。

## 展望未來

AI的整合將繼續加速，為企業軟體創造新的機遇和挑戰。`,
    },
    featuredImage:
      'https://images.unsplash.com/photo-1677442136019-21780ecad995?w=800&h=400&fit=crop&crop=center',
    category: newsCategories[2],
    author: newsAuthors[1],
    publishedAt: '2024-01-08T14:00:00Z',
    updatedAt: '2024-01-08T14:00:00Z',
    tags: ['ai', 'trends', 'enterprise', 'automation'],
    readTime: 6,
    featured: false,
    status: 'published',
  },
  {
    id: '4',
    slug: 'security-certification-iso-27001',
    title: {
      en: 'Achieving ISO 27001 Certification: Our Commitment to Security',
      zh: '获得ISO 27001认证：我们对安全的承诺',
      'zh-TW': '獲得ISO 27001認證：我們對安全的承諾',
    },
    excerpt: {
      en: 'We are proud to announce that we have achieved ISO 27001 certification, demonstrating our commitment to information security management.',
      zh: '我们很自豪地宣布我们已获得ISO 27001认证，展示了我们对信息安全管理的承诺。',
      'zh-TW':
        '我們很自豪地宣布我們已獲得ISO 27001認證，展示了我們對信息安全管理的承諾。',
    },
    content: {
      en: `# Achieving ISO 27001 Certification

We are proud to announce that our company has successfully achieved ISO 27001 certification, a globally recognized standard for information security management systems.

## What This Means

### Enhanced Security
This certification validates our comprehensive approach to protecting customer data and maintaining system security.

### Continuous Improvement
ISO 27001 requires ongoing monitoring and improvement of our security practices.

### Customer Trust
This achievement reinforces our commitment to maintaining the highest security standards.

## Our Security Journey

The certification process involved extensive audits and improvements to our security infrastructure and processes.

## Moving Forward

We will continue to invest in security measures and maintain our certification through regular assessments and updates.`,
      zh: `# 获得ISO 27001认证

我们很自豪地宣布我们公司已成功获得ISO 27001认证，这是信息安全管理系统的全球认可标准。

## 这意味着什么

### 增强安全性
此认证验证了我们保护客户数据和维护系统安全的综合方法。

### 持续改进
ISO 27001要求持续监控和改进我们的安全实践。

### 客户信任
这一成就强化了我们维护最高安全标准的承诺。

## 我们的安全之旅

认证过程涉及对我们的安全基础设施和流程进行广泛的审计和改进。

## 展望未来

我们将继续投资安全措施，并通过定期评估和更新维护我们的认证。`,
      'zh-TW': `# 獲得ISO 27001認證

我們很自豪地宣布我們公司已成功獲得ISO 27001認證，這是信息安全管理系統的全球認可標準。

## 這意味著什麼

### 增強安全性
此認證驗證了我們保護客戶數據和維護系統安全的綜合方法。

### 持續改進
ISO 27001要求持續監控和改進我們的安全實踐。

### 客戶信任
這一成就強化了我們維護最高安全標準的承諾。

## 我們的安全之旅

認證過程涉及對我們的安全基礎設施和流程進行廣泛的審計和改進。

## 展望未來

我們將繼續投資安全措施，並通過定期評估和更新維護我們的認證。`,
    },
    featuredImage:
      'https://images.unsplash.com/photo-1450101499163-c8848c66ca85?w=800&h=400&fit=crop&crop=center',
    category: newsCategories[3],
    author: newsAuthors[1],
    publishedAt: '2024-01-05T11:00:00Z',
    updatedAt: '2024-01-05T11:00:00Z',
    tags: ['security', 'certification', 'iso-27001', 'compliance'],
    readTime: 4,
    featured: false,
    status: 'published',
  },
  {
    id: '5',
    slug: 'quarterly-results-q4-2023',
    title: {
      en: 'Q4 2023 Results: Record Growth and Customer Satisfaction',
      zh: '2023年第四季度业绩：创纪录的增长和客户满意度',
      'zh-TW': '2023年第四季度業績：創紀錄的增長和客戶滿意度',
    },
    excerpt: {
      en: 'Our Q4 2023 results show record growth in revenue and customer satisfaction, highlighting the success of our strategic initiatives.',
      zh: '我们2023年第四季度的业绩显示收入和客户满意度创纪录增长，突出了我们战略举措的成功。',
      'zh-TW':
        '我們2023年第四季度的業績顯示收入和客戶滿意度創紀錄增長，突出了我們戰略舉措的成功。',
    },
    content: {
      en: `# Q4 2023 Results: Record Growth and Customer Satisfaction

We are pleased to share our Q4 2023 results, which demonstrate strong performance across all key metrics.

## Key Highlights

### Revenue Growth
- 45% year-over-year revenue growth
- 120% increase in new customer acquisitions
- 95% customer retention rate

### Product Milestones
- Launched 3 major product features
- Achieved 99.9% uptime
- Processed over 1 billion transactions

### Customer Satisfaction
- Net Promoter Score of 72
- 98% customer satisfaction rating
- 40% increase in customer referrals

## Looking Ahead

These results position us well for continued growth in 2024 as we expand our product offerings and market presence.

## Thank You

We thank our customers, partners, and team members for making these achievements possible.`,
      zh: `# 2023年第四季度业绩：创纪录的增长和客户满意度

我们很高兴分享我们2023年第四季度的业绩，这些业绩在所有关键指标上都表现强劲。

## 主要亮点

### 收入增长
- 同比收入增长45%
- 新客户获取增长120%
- 客户保留率95%

### 产品里程碑
- 推出3个主要产品功能
- 实现99.9%的正常运行时间
- 处理超过10亿笔交易

### 客户满意度
- 净推荐值72
- 客户满意度评级98%
- 客户推荐增长40%

## 展望未来

这些结果为我们在2024年继续增长奠定了良好基础，我们将扩大产品供应和市场存在。

## 感谢

我们感谢我们的客户、合作伙伴和团队成员使这些成就成为可能。`,
      'zh-TW': `# 2023年第四季度業績：創紀錄的增長和客戶滿意度

我們很高興分享我們2023年第四季度的業績，這些業績在所有關鍵指標上都表現強勁。

## 主要亮點

### 收入增長
- 同比收入增長45%
- 新客戶獲取增長120%
- 客戶保留率95%

### 產品里程碑
- 推出3個主要產品功能
- 實現99.9%的正常運行時間
- 處理超過10億筆交易

### 客戶滿意度
- 淨推薦值72
- 客戶滿意度評級98%
- 客戶推薦增長40%

## 展望未來

這些結果為我們在2024年繼續增長奠定了良好基礎，我們將擴大產品供應和市場存在。

## 感謝

我們感謝我們的客戶、合作夥伴和團隊成員使這些成就成為可能。`,
    },
    featuredImage:
      'https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=800&h=400&fit=crop&crop=center',
    category: newsCategories[3],
    author: newsAuthors[2],
    publishedAt: '2024-01-02T16:00:00Z',
    updatedAt: '2024-01-02T16:00:00Z',
    tags: ['results', 'growth', 'performance', 'q4-2023'],
    readTime: 5,
    featured: true,
    status: 'published',
  },
];

// Helper functions
export const getFeaturedNews = (): NewsArticle[] => {
  return newsArticles.filter(
    article => article.featured && article.status === 'published'
  );
};

export const getNewsByCategory = (categorySlug: string): NewsArticle[] => {
  return newsArticles.filter(
    article =>
      article.category.slug === categorySlug && article.status === 'published'
  );
};

export const getNewsByAuthor = (authorId: string): NewsArticle[] => {
  return newsArticles.filter(
    article => article.author.id === authorId && article.status === 'published'
  );
};

export const getNewsById = (id: string): NewsArticle | undefined => {
  return newsArticles.find(article => article.id === id);
};

export const getNewsBySlug = (slug: string): NewsArticle | undefined => {
  return newsArticles.find(article => article.slug === slug);
};

export const getRelatedNews = (
  currentArticle: NewsArticle,
  limit: number = 3
): NewsArticle[] => {
  return newsArticles
    .filter(
      article =>
        article.id !== currentArticle.id &&
        article.status === 'published' &&
        (article.category.id === currentArticle.category.id ||
          article.tags.some(tag => currentArticle.tags.includes(tag)))
    )
    .slice(0, limit);
};

export const searchNews = (query: string): NewsArticle[] => {
  const lowercaseQuery = query.toLowerCase();
  return newsArticles.filter(
    article =>
      article.status === 'published' &&
      (article.title.en.toLowerCase().includes(lowercaseQuery) ||
        article.excerpt.en.toLowerCase().includes(lowercaseQuery) ||
        article.tags.some(tag => tag.toLowerCase().includes(lowercaseQuery)))
  );
};

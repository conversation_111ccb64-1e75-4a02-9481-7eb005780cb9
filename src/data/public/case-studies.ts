import {
  CaseStudy,
  CaseStudyCategory,
  CaseStudyClient,
} from '@/types/public/case-studies';

export const caseStudyCategories: CaseStudyCategory[] = [
  {
    id: '1',
    name: {
      en: 'Digital Transformation',
      zh: '数字化转型',
      'zh-TW': '數位轉型',
    },
    slug: 'digital-transformation',
  },
  {
    id: '2',
    name: {
      en: 'Process Automation',
      zh: '流程自动化',
      'zh-TW': '流程自動化',
    },
    slug: 'process-automation',
  },
  {
    id: '3',
    name: {
      en: 'Data Analytics',
      zh: '数据分析',
      'zh-TW': '數據分析',
    },
    slug: 'data-analytics',
  },
  {
    id: '4',
    name: {
      en: 'Cloud Migration',
      zh: '云迁移',
      'zh-TW': '雲端遷移',
    },
    slug: 'cloud-migration',
  },
  {
    id: '5',
    name: {
      en: 'Enterprise Integration',
      zh: '企业集成',
      'zh-TW': '企業整合',
    },
    slug: 'enterprise-integration',
  },
];

export const caseStudyClients: CaseStudyClient[] = [
  {
    id: '1',
    name: 'TechCorp Solutions',
    industry: {
      en: 'Technology',
      zh: '技术',
      'zh-TW': '技術',
    },
    logo: 'https://images.unsplash.com/photo-**********-b33ff0c44a43?w=200&h=100&fit=crop&crop=center',
    website: 'https://techcorp.example.com',
  },
  {
    id: '2',
    name: 'Global Manufacturing Inc.',
    industry: {
      en: 'Manufacturing',
      zh: '制造业',
      'zh-TW': '製造業',
    },
    logo: 'https://images.unsplash.com/photo-*************-c627a92ad1ab?w=200&h=100&fit=crop&crop=center',
    website: 'https://globalmanufacturing.example.com',
  },
  {
    id: '3',
    name: 'FinanceFirst Bank',
    industry: {
      en: 'Financial Services',
      zh: '金融服务',
      'zh-TW': '金融服務',
    },
    logo: 'https://images.unsplash.com/photo-*************-f4d9a9f9297f?w=200&h=100&fit=crop&crop=center',
    website: 'https://financefirst.example.com',
  },
  {
    id: '4',
    name: 'HealthCare Plus',
    industry: {
      en: 'Healthcare',
      zh: '医疗保健',
      'zh-TW': '醫療保健',
    },
    logo: 'https://images.unsplash.com/photo-*************-112ba8d25d1f?w=200&h=100&fit=crop&crop=center',
    website: 'https://healthcareplus.example.com',
  },
  {
    id: '5',
    name: 'RetailMax Group',
    industry: {
      en: 'Retail',
      zh: '零售',
      'zh-TW': '零售',
    },
    logo: 'https://images.unsplash.com/photo-*************-64674bd600d8?w=200&h=100&fit=crop&crop=center',
    website: 'https://retailmax.example.com',
  },
];

export const caseStudies: CaseStudy[] = [
  {
    id: '1',
    slug: 'techcorp-digital-transformation',
    title: {
      en: "TechCorp's Complete Digital Transformation Journey",
      zh: 'TechCorp的完整数字化转型之旅',
      'zh-TW': 'TechCorp的完整數位轉型之旅',
    },
    excerpt: {
      en: 'How TechCorp Solutions transformed their legacy systems and achieved 300% productivity increase through comprehensive digital transformation.',
      zh: 'TechCorp Solutions如何通过全面的数字化转型改造其传统系统并实现300%的生产力提升。',
      'zh-TW':
        'TechCorp Solutions如何通過全面的數位轉型改造其傳統系統並實現300%的生產力提升。',
    },
    content: {
      en: `# TechCorp's Digital Transformation Success Story

TechCorp Solutions, a leading technology consulting firm, embarked on a comprehensive digital transformation journey to modernize their operations and better serve their clients.

## The Challenge

TechCorp was struggling with outdated legacy systems that were hindering their ability to scale and compete effectively in the modern market.

## Our Solution

We implemented a comprehensive digital transformation strategy that included:

- Cloud migration of all critical systems
- Implementation of modern DevOps practices
- Integration of AI-powered analytics
- Complete workflow automation

## The Results

The transformation resulted in significant improvements across all key metrics, positioning TechCorp as a leader in their industry.`,
      zh: `# TechCorp的数字化转型成功案例

TechCorp Solutions是一家领先的技术咨询公司，开始了全面的数字化转型之旅，以现代化其运营并更好地为客户服务。

## 挑战

TechCorp正在与过时的传统系统作斗争，这些系统阻碍了他们扩展和在现代市场中有效竞争的能力。

## 我们的解决方案

我们实施了一个全面的数字化转型策略，包括：

- 所有关键系统的云迁移
- 实施现代DevOps实践
- 集成AI驱动的分析
- 完整的工作流程自动化

## 结果

转型在所有关键指标上都取得了显著改善，使TechCorp成为其行业的领导者。`,
      'zh-TW': `# TechCorp的數位轉型成功案例

TechCorp Solutions是一家領先的技術諮詢公司，開始了全面的數位轉型之旅，以現代化其運營並更好地為客戶服務。

## 挑戰

TechCorp正在與過時的傳統系統作鬥爭，這些系統阻礙了他們擴展和在現代市場中有效競爭的能力。

## 我們的解決方案

我們實施了一個全面的數位轉型策略，包括：

- 所有關鍵系統的雲端遷移
- 實施現代DevOps實踐
- 整合AI驅動的分析
- 完整的工作流程自動化

## 結果

轉型在所有關鍵指標上都取得了顯著改善，使TechCorp成為其行業的領導者。`,
    },
    featuredImage:
      'https://images.unsplash.com/photo-**********-bebda4e38f71?w=800&h=400&fit=crop&crop=center',
    category: caseStudyCategories[0],
    client: caseStudyClients[0],
    publishedAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z',
    tags: [
      'digital-transformation',
      'cloud-migration',
      'automation',
      'productivity',
    ],
    readTime: 8,
    featured: true,
    status: 'published',
    challenges: [
      {
        id: '1',
        title: {
          en: 'Legacy System Dependencies',
          zh: '传统系统依赖',
          'zh-TW': '傳統系統依賴',
        },
        description: {
          en: 'Multiple interconnected legacy systems that were difficult to maintain and scale.',
          zh: '多个相互关联的传统系统，难以维护和扩展。',
          'zh-TW': '多個相互關聯的傳統系統，難以維護和擴展。',
        },
      },
      {
        id: '2',
        title: {
          en: 'Manual Process Bottlenecks',
          zh: '手动流程瓶颈',
          'zh-TW': '手動流程瓶頸',
        },
        description: {
          en: 'Time-consuming manual processes that limited productivity and introduced errors.',
          zh: '耗时的手动流程限制了生产力并引入了错误。',
          'zh-TW': '耗時的手動流程限制了生產力並引入了錯誤。',
        },
      },
    ],
    solutions: [
      {
        id: '1',
        title: {
          en: 'Cloud-First Architecture',
          zh: '云优先架构',
          'zh-TW': '雲端優先架構',
        },
        description: {
          en: 'Migrated all systems to a modern cloud infrastructure with microservices architecture.',
          zh: '将所有系统迁移到具有微服务架构的现代云基础设施。',
          'zh-TW': '將所有系統遷移到具有微服務架構的現代雲端基礎設施。',
        },
        features: [
          {
            en: 'Scalable microservices',
            zh: '可扩展的微服务',
            'zh-TW': '可擴展的微服務',
          },
          {
            en: 'Automated deployment',
            zh: '自动化部署',
            'zh-TW': '自動化部署',
          },
          {
            en: 'Real-time monitoring',
            zh: '实时监控',
            'zh-TW': '即時監控',
          },
        ],
      },
      {
        id: '2',
        title: {
          en: 'Process Automation',
          zh: '流程自动化',
          'zh-TW': '流程自動化',
        },
        description: {
          en: 'Implemented comprehensive workflow automation to eliminate manual bottlenecks.',
          zh: '实施全面的工作流程自动化以消除手动瓶颈。',
          'zh-TW': '實施全面的工作流程自動化以消除手動瓶頸。',
        },
        features: [
          {
            en: 'Automated testing',
            zh: '自动化测试',
            'zh-TW': '自動化測試',
          },
          {
            en: 'CI/CD pipelines',
            zh: 'CI/CD管道',
            'zh-TW': 'CI/CD管道',
          },
          {
            en: 'Intelligent routing',
            zh: '智能路由',
            'zh-TW': '智能路由',
          },
        ],
      },
    ],
    outcomes: [
      {
        id: '1',
        title: {
          en: 'Dramatic Productivity Gains',
          zh: '显著的生产力提升',
          'zh-TW': '顯著的生產力提升',
        },
        description: {
          en: 'Achieved unprecedented productivity improvements across all departments.',
          zh: '在所有部门实现了前所未有的生产力改进。',
          'zh-TW': '在所有部門實現了前所未有的生產力改進。',
        },
      },
      {
        id: '2',
        title: {
          en: 'Enhanced Customer Satisfaction',
          zh: '提高客户满意度',
          'zh-TW': '提高客戶滿意度',
        },
        description: {
          en: 'Faster response times and improved service quality led to higher customer satisfaction.',
          zh: '更快的响应时间和改进的服务质量导致更高的客户满意度。',
          'zh-TW': '更快的響應時間和改進的服務質量導致更高的客戶滿意度。',
        },
      },
    ],
    keyMetrics: [
      {
        id: '1',
        label: {
          en: 'Productivity Increase',
          zh: '生产力提升',
          'zh-TW': '生產力提升',
        },
        value: '300%',
        description: {
          en: 'Overall productivity improvement',
          zh: '整体生产力改进',
          'zh-TW': '整體生產力改進',
        },
        icon: 'trending-up',
      },
      {
        id: '2',
        label: {
          en: 'Cost Reduction',
          zh: '成本降低',
          'zh-TW': '成本降低',
        },
        value: '45%',
        description: {
          en: 'Operational cost savings',
          zh: '运营成本节约',
          'zh-TW': '營運成本節約',
        },
        icon: 'dollar-sign',
      },
      {
        id: '3',
        label: {
          en: 'Deployment Speed',
          zh: '部署速度',
          'zh-TW': '部署速度',
        },
        value: '10x',
        description: {
          en: 'Faster deployment cycles',
          zh: '更快的部署周期',
          'zh-TW': '更快的部署週期',
        },
        icon: 'zap',
      },
      {
        id: '4',
        label: {
          en: 'System Uptime',
          zh: '系统正常运行时间',
          'zh-TW': '系統正常運行時間',
        },
        value: '99.9%',
        description: {
          en: 'Improved system reliability',
          zh: '改进的系统可靠性',
          'zh-TW': '改進的系統可靠性',
        },
        icon: 'shield-check',
      },
    ],
    testimonial: {
      quote: {
        en: "The transformation exceeded our expectations. We've not only modernized our systems but fundamentally changed how we operate as a business.",
        zh: '转型超出了我们的预期。我们不仅现代化了我们的系统，而且从根本上改变了我们作为企业的运营方式。',
        'zh-TW':
          '轉型超出了我們的預期。我們不僅現代化了我們的系統，而且從根本上改變了我們作為企業的營運方式。',
      },
      author: {
        name: 'Sarah Johnson',
        title: {
          en: 'CEO, TechCorp Solutions',
          zh: 'TechCorp Solutions首席执行官',
          'zh-TW': 'TechCorp Solutions執行長',
        },
        avatar:
          'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
      },
    },
  },
  {
    id: '2',
    slug: 'global-manufacturing-automation',
    title: {
      en: "Global Manufacturing's Process Automation Revolution",
      zh: 'Global Manufacturing的流程自动化革命',
      'zh-TW': 'Global Manufacturing的流程自動化革命',
    },
    excerpt: {
      en: 'Discover how Global Manufacturing Inc. revolutionized their production processes with intelligent automation, reducing costs by 60% and improving quality.',
      zh: '了解Global Manufacturing Inc.如何通过智能自动化革命性地改变其生产流程，降低成本60%并提高质量。',
      'zh-TW':
        '了解Global Manufacturing Inc.如何通過智能自動化革命性地改變其生產流程，降低成本60%並提高品質。',
    },
    content: {
      en: `# Global Manufacturing's Automation Success

Global Manufacturing Inc. partnered with us to transform their traditional manufacturing processes through intelligent automation and data-driven insights.

## The Challenge

The company faced increasing competition and rising operational costs while struggling to maintain consistent product quality across multiple facilities.

## Our Approach

We implemented a comprehensive automation strategy that integrated IoT sensors, machine learning algorithms, and predictive analytics to optimize their entire production pipeline.

## Transformative Results

The automation initiative delivered exceptional results, positioning Global Manufacturing as an industry leader in operational efficiency and product quality.`,
      zh: `# Global Manufacturing的自动化成功

Global Manufacturing Inc.与我们合作，通过智能自动化和数据驱动的洞察来转变其传统制造流程。

## 挑战

该公司面临日益激烈的竞争和不断上升的运营成本，同时努力在多个设施中保持一致的产品质量。

## 我们的方法

我们实施了一个全面的自动化策略，集成了物联网传感器、机器学习算法和预测分析，以优化其整个生产管道。

## 变革性结果

自动化倡议取得了卓越的成果，使Global Manufacturing成为运营效率和产品质量方面的行业领导者。`,
      'zh-TW': `# Global Manufacturing的自動化成功

Global Manufacturing Inc.與我們合作，通過智能自動化和數據驅動的洞察來轉變其傳統製造流程。

## 挑戰

該公司面臨日益激烈的競爭和不斷上升的營運成本，同時努力在多個設施中保持一致的產品品質。

## 我們的方法

我們實施了一個全面的自動化策略，整合了物聯網感測器、機器學習演算法和預測分析，以優化其整個生產管道。

## 變革性結果

自動化倡議取得了卓越的成果，使Global Manufacturing成為營運效率和產品品質方面的行業領導者。`,
    },
    featuredImage:
      'https://images.unsplash.com/photo-1565514020179-026b92b84bb6?w=800&h=400&fit=crop&crop=center',
    category: caseStudyCategories[1],
    client: caseStudyClients[1],
    publishedAt: '2024-01-12T14:00:00Z',
    updatedAt: '2024-01-12T14:00:00Z',
    tags: ['automation', 'manufacturing', 'iot', 'machine-learning'],
    readTime: 7,
    featured: true,
    status: 'published',
    challenges: [
      {
        id: '1',
        title: {
          en: 'Inconsistent Quality Control',
          zh: '质量控制不一致',
          'zh-TW': '品質控制不一致',
        },
        description: {
          en: 'Manual quality control processes led to inconsistent product quality across different production lines.',
          zh: '手动质量控制流程导致不同生产线的产品质量不一致。',
          'zh-TW': '手動品質控制流程導致不同生產線的產品品質不一致。',
        },
      },
      {
        id: '2',
        title: {
          en: 'High Operational Costs',
          zh: '高运营成本',
          'zh-TW': '高營運成本',
        },
        description: {
          en: 'Rising labor costs and inefficient processes were impacting profitability.',
          zh: '不断上升的劳动成本和低效的流程正在影响盈利能力。',
          'zh-TW': '不斷上升的勞動成本和低效的流程正在影響獲利能力。',
        },
      },
    ],
    solutions: [
      {
        id: '1',
        title: {
          en: 'Smart Manufacturing Platform',
          zh: '智能制造平台',
          'zh-TW': '智能製造平台',
        },
        description: {
          en: 'Deployed IoT-enabled smart manufacturing platform with real-time monitoring and control.',
          zh: '部署了具有实时监控和控制功能的物联网智能制造平台。',
          'zh-TW': '部署了具有即時監控和控制功能的物聯網智能製造平台。',
        },
        features: [
          {
            en: 'Real-time monitoring',
            zh: '实时监控',
            'zh-TW': '即時監控',
          },
          {
            en: 'Predictive maintenance',
            zh: '预测性维护',
            'zh-TW': '預測性維護',
          },
          {
            en: 'Quality automation',
            zh: '质量自动化',
            'zh-TW': '品質自動化',
          },
        ],
      },
    ],
    outcomes: [
      {
        id: '1',
        title: {
          en: 'Significant Cost Savings',
          zh: '显著的成本节约',
          'zh-TW': '顯著的成本節約',
        },
        description: {
          en: 'Achieved substantial reduction in operational costs through automation and efficiency improvements.',
          zh: '通过自动化和效率改进实现了运营成本的大幅降低。',
          'zh-TW': '通過自動化和效率改進實現了營運成本的大幅降低。',
        },
      },
    ],
    keyMetrics: [
      {
        id: '1',
        label: {
          en: 'Cost Reduction',
          zh: '成本降低',
          'zh-TW': '成本降低',
        },
        value: '60%',
        description: {
          en: 'Operational cost savings',
          zh: '运营成本节约',
          'zh-TW': '營運成本節約',
        },
        icon: 'dollar-sign',
      },
      {
        id: '2',
        label: {
          en: 'Quality Improvement',
          zh: '质量改进',
          'zh-TW': '品質改進',
        },
        value: '95%',
        description: {
          en: 'Defect reduction rate',
          zh: '缺陷减少率',
          'zh-TW': '缺陷減少率',
        },
        icon: 'award',
      },
      {
        id: '3',
        label: {
          en: 'Production Efficiency',
          zh: '生产效率',
          'zh-TW': '生產效率',
        },
        value: '40%',
        description: {
          en: 'Increase in production speed',
          zh: '生产速度提升',
          'zh-TW': '生產速度提升',
        },
        icon: 'trending-up',
      },
    ],
    testimonial: {
      quote: {
        en: "The automation solution transformed our operations completely. We're now more competitive than ever while maintaining the highest quality standards.",
        zh: '自动化解决方案彻底改变了我们的运营。我们现在比以往任何时候都更具竞争力，同时保持最高的质量标准。',
        'zh-TW':
          '自動化解決方案徹底改變了我們的營運。我們現在比以往任何時候都更具競爭力，同時保持最高的品質標準。',
      },
      author: {
        name: 'Michael Chen',
        title: {
          en: 'Operations Director, Global Manufacturing Inc.',
          zh: 'Global Manufacturing Inc.运营总监',
          'zh-TW': 'Global Manufacturing Inc.營運總監',
        },
        avatar:
          'https://images.unsplash.com/photo-*************-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
      },
    },
  },
  {
    id: '3',
    slug: 'financefirst-data-analytics',
    title: {
      en: "FinanceFirst Bank's Data-Driven Decision Making Revolution",
      zh: 'FinanceFirst银行的数据驱动决策革命',
      'zh-TW': 'FinanceFirst銀行的數據驅動決策革命',
    },
    excerpt: {
      en: 'Learn how FinanceFirst Bank leveraged advanced analytics to improve risk assessment, reduce fraud by 80%, and enhance customer experience.',
      zh: '了解FinanceFirst银行如何利用高级分析来改善风险评估，减少80%的欺诈，并增强客户体验。',
      'zh-TW':
        '了解FinanceFirst銀行如何利用高級分析來改善風險評估，減少80%的詐欺，並增強客戶體驗。',
    },
    content: {
      en: `# FinanceFirst Bank's Analytics Transformation

FinanceFirst Bank embarked on a comprehensive data analytics initiative to modernize their risk management and customer service capabilities.

## The Challenge

The bank needed to improve fraud detection, enhance risk assessment accuracy, and provide personalized customer experiences in an increasingly competitive financial services market.

## Our Solution

We implemented a sophisticated analytics platform that leverages machine learning, real-time data processing, and predictive modeling to transform their operations.

## Outstanding Results

The analytics transformation delivered remarkable improvements in security, efficiency, and customer satisfaction.`,
      zh: `# FinanceFirst银行的分析转型

FinanceFirst银行开始了一项全面的数据分析倡议，以现代化其风险管理和客户服务能力。

## 挑战

银行需要改善欺诈检测，提高风险评估准确性，并在日益竞争激烈的金融服务市场中提供个性化的客户体验。

## 我们的解决方案

我们实施了一个复杂的分析平台，利用机器学习、实时数据处理和预测建模来转变其运营。

## 卓越的结果

分析转型在安全性、效率和客户满意度方面取得了显著改善。`,
      'zh-TW': `# FinanceFirst銀行的分析轉型

FinanceFirst銀行開始了一項全面的數據分析倡議，以現代化其風險管理和客戶服務能力。

## 挑戰

銀行需要改善詐欺檢測，提高風險評估準確性，並在日益競爭激烈的金融服務市場中提供個性化的客戶體驗。

## 我們的解決方案

我們實施了一個複雜的分析平台，利用機器學習、即時數據處理和預測建模來轉變其營運。

## 卓越的結果

分析轉型在安全性、效率和客戶滿意度方面取得了顯著改善。`,
    },
    featuredImage:
      'https://images.unsplash.com/photo-**********-bebda4e38f71?w=800&h=400&fit=crop&crop=center',
    category: caseStudyCategories[2],
    client: caseStudyClients[2],
    publishedAt: '2024-01-10T11:00:00Z',
    updatedAt: '2024-01-10T11:00:00Z',
    tags: ['data-analytics', 'machine-learning', 'fraud-detection', 'banking'],
    readTime: 6,
    featured: false,
    status: 'published',
    challenges: [
      {
        id: '1',
        title: {
          en: 'Fraud Detection Limitations',
          zh: '欺诈检测限制',
          'zh-TW': '詐欺檢測限制',
        },
        description: {
          en: 'Traditional rule-based fraud detection systems were missing sophisticated fraud patterns.',
          zh: '传统的基于规则的欺诈检测系统错过了复杂的欺诈模式。',
          'zh-TW': '傳統的基於規則的詐欺檢測系統錯過了複雜的詐欺模式。',
        },
      },
    ],
    solutions: [
      {
        id: '1',
        title: {
          en: 'Advanced Analytics Platform',
          zh: '高级分析平台',
          'zh-TW': '高級分析平台',
        },
        description: {
          en: 'Implemented machine learning-powered analytics platform for real-time fraud detection and risk assessment.',
          zh: '实施了机器学习驱动的分析平台，用于实时欺诈检测和风险评估。',
          'zh-TW': '實施了機器學習驅動的分析平台，用於即時詐欺檢測和風險評估。',
        },
      },
    ],
    outcomes: [
      {
        id: '1',
        title: {
          en: 'Enhanced Security',
          zh: '增强安全性',
          'zh-TW': '增強安全性',
        },
        description: {
          en: 'Dramatically improved fraud detection capabilities and reduced financial losses.',
          zh: '显著改善了欺诈检测能力并减少了财务损失。',
          'zh-TW': '顯著改善了詐欺檢測能力並減少了財務損失。',
        },
      },
    ],
    keyMetrics: [
      {
        id: '1',
        label: {
          en: 'Fraud Reduction',
          zh: '欺诈减少',
          'zh-TW': '詐欺減少',
        },
        value: '80%',
        description: {
          en: 'Reduction in fraud incidents',
          zh: '欺诈事件减少',
          'zh-TW': '詐欺事件減少',
        },
        icon: 'shield-check',
      },
      {
        id: '2',
        label: {
          en: 'Risk Assessment Accuracy',
          zh: '风险评估准确性',
          'zh-TW': '風險評估準確性',
        },
        value: '92%',
        description: {
          en: 'Improved accuracy in risk scoring',
          zh: '风险评分准确性提升',
          'zh-TW': '風險評分準確性提升',
        },
        icon: 'target',
      },
    ],
  },
];

// Helper functions
export const getFeaturedCaseStudies = (): CaseStudy[] => {
  return caseStudies.filter(
    study => study.featured && study.status === 'published'
  );
};

export const getCaseStudiesByCategory = (categorySlug: string): CaseStudy[] => {
  return caseStudies.filter(
    study =>
      study.category.slug === categorySlug && study.status === 'published'
  );
};

export const getCaseStudiesByIndustry = (industry: string): CaseStudy[] => {
  return caseStudies.filter(
    study =>
      study.client.industry.en.toLowerCase().includes(industry.toLowerCase()) &&
      study.status === 'published'
  );
};

export const getCaseStudyById = (id: string): CaseStudy | undefined => {
  return caseStudies.find(study => study.id === id);
};

export const getCaseStudyBySlug = (slug: string): CaseStudy | undefined => {
  return caseStudies.find(study => study.slug === slug);
};

export const getRelatedCaseStudies = (
  currentStudy: CaseStudy,
  limit: number = 3
): CaseStudy[] => {
  return caseStudies
    .filter(
      study =>
        study.id !== currentStudy.id &&
        study.status === 'published' &&
        (study.category.id === currentStudy.category.id ||
          study.tags.some(tag => currentStudy.tags.includes(tag)))
    )
    .slice(0, limit);
};

export const searchCaseStudies = (query: string): CaseStudy[] => {
  const lowercaseQuery = query.toLowerCase();
  return caseStudies.filter(
    study =>
      study.status === 'published' &&
      (study.title.en.toLowerCase().includes(lowercaseQuery) ||
        study.excerpt.en.toLowerCase().includes(lowercaseQuery) ||
        study.client.name.toLowerCase().includes(lowercaseQuery) ||
        study.tags.some(tag => tag.toLowerCase().includes(lowercaseQuery)))
  );
};

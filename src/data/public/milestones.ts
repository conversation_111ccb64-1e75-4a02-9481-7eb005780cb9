/**
 * Company Milestones Data
 *
 * This file contains the milestone data for the company, including all translations.
 * Translations are stored directly with the data rather than in separate translation files,
 * making it easier to maintain and update milestone information.
 */

import { MilestoneYear, Milestone } from '@/types/public/milestones';
import { getTranslation } from '@/utils/translation';

/**
 * Company milestones
 * These will be displayed on the About page timeline
 */
export const companyMilestones: Milestone[] = [
  {
    year: 'foundedYear',
    title: {
      en: 'Company Founded',
      zh: '公司成立',
      'zh-TW': '公司成立',
    },
    description: {
      en: '{company} was founded with a vision to transform the industry.',
      zh: '{company}成立，以改变行业的愿景为宗旨。',
      'zh-TW': '{company}成立，以改變行業的願景為宗旨。',
    },
  },
  {
    year: 'foundedYear+3',
    title: {
      en: 'International Expansion',
      zh: '国际扩张',
      'zh-TW': '國際擴張',
    },
    description: {
      en: '{company} expanded operations to international markets, reaching clients worldwide.',
      zh: '{company}将业务扩展到国际市场，服务全球客户。',
      'zh-TW': '{company}將業務擴展到國際市場，服務全球客戶。',
    },
  },
  {
    year: 'currentYear-1',
    title: {
      en: 'Industry Recognition',
      zh: '行业认可',
      'zh-TW': '行業認可',
    },
    description: {
      en: '{company} received multiple awards for innovation and excellence in service delivery.',
      zh: '{company}因创新和卓越的服务而获得多项奖项。',
      'zh-TW': '{company}因創新和卓越的服務而獲得多項獎項。',
    },
  },
];

/**
 * Helper function to calculate the actual year from a MilestoneYear
 */
export function calculateMilestoneYear(
  milestone: MilestoneYear,
  foundedYear: number,
  currentYear: number = new Date().getFullYear()
): number {
  if (typeof milestone === 'number') {
    return milestone;
  }

  if (milestone === 'foundedYear') {
    return foundedYear;
  }

  if (milestone.startsWith('foundedYear+')) {
    const addYears = parseInt(milestone.split('+')[1]);
    return foundedYear + addYears;
  }

  if (milestone.startsWith('currentYear-')) {
    const subtractYears = parseInt(milestone.split('-')[1]);
    return currentYear - subtractYears;
  }

  return parseInt(milestone) || currentYear;
}

/**
 * Helper function to get milestone data with translations for a specific locale
 * Falls back to English if the requested locale isn't available
 */
export function getMilestoneWithTranslation(
  milestone: Milestone,
  locale: string,
  companyName: string
): { year: number; title: string; description: string } {
  // Get the localized translations using the utility function
  const title = getTranslation(milestone.title, locale);
  const description = getTranslation(milestone.description, locale).replace(
    '{company}',
    companyName
  );

  return {
    year: typeof milestone.year === 'number' ? milestone.year : 0, // This will be replaced by the calculated year
    title,
    description,
  };
}

export default companyMilestones;

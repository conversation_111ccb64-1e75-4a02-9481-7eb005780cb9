import { Partner, PartnerCategory } from '@/types/public/partners';

export const partnerCategories: PartnerCategory[] = [
  {
    id: '1',
    name: {
      en: 'Technology Partners',
      zh: '技术合作伙伴',
      'zh-TW': '技術合作夥伴',
    },
    slug: 'technology',
  },
  {
    id: '2',
    name: {
      en: 'Integration Partners',
      zh: '集成合作伙伴',
      'zh-TW': '整合合作夥伴',
    },
    slug: 'integration',
  },
  {
    id: '3',
    name: {
      en: 'Consulting Partners',
      zh: '咨询合作伙伴',
      'zh-TW': '諮詢合作夥伴',
    },
    slug: 'consulting',
  },
  {
    id: '4',
    name: {
      en: 'Channel Partners',
      zh: '渠道合作伙伴',
      'zh-TW': '通路合作夥伴',
    },
    slug: 'channel',
  },
];

export const partners: Partner[] = [
  {
    id: '1',
    name: 'TechCorp Solutions',
    logo: 'https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=200&h=100&fit=crop&crop=center',
    website: 'https://techcorp.example.com',
    description: {
      en: 'Leading technology solutions provider specializing in cloud infrastructure and enterprise software development.',
      zh: '领先的技术解决方案提供商，专注于云基础设施和企业软件开发。',
      'zh-TW': '領先的技術解決方案提供商，專注於雲基礎設施和企業軟體開發。',
    },
    category: partnerCategories[0],
    featured: true,
    testimonial: {
      quote: {
        en: 'Our partnership has enabled us to deliver cutting-edge solutions to our clients faster than ever before.',
        zh: '我们的合作伙伴关系使我们能够比以往更快地为客户提供尖端解决方案。',
        'zh-TW':
          '我們的合作夥伴關係使我們能夠比以往更快地為客戶提供尖端解決方案。',
      },
      author: 'John Smith',
      position: 'CTO, TechCorp Solutions',
    },
    caseStudyLink: '/case-studies/techcorp-integration',
    partnershipDate: '2022-03-15',
    status: 'active',
  },
  {
    id: '2',
    name: 'DataFlow Systems',
    logo: 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=200&h=100&fit=crop&crop=center',
    website: 'https://dataflow.example.com',
    description: {
      en: 'Specialized in data integration and analytics platforms for enterprise customers.',
      zh: '专门为企业客户提供数据集成和分析平台。',
      'zh-TW': '專門為企業客戶提供數據整合和分析平台。',
    },
    category: partnerCategories[1],
    featured: true,
    testimonial: {
      quote: {
        en: 'The seamless integration capabilities have transformed how our customers manage their data workflows.',
        zh: '无缝集成功能改变了我们客户管理数据工作流的方式。',
        'zh-TW': '無縫整合功能改變了我們客戶管理數據工作流的方式。',
      },
      author: 'Sarah Johnson',
      position: 'VP of Product, DataFlow Systems',
    },
    partnershipDate: '2021-11-20',
    status: 'active',
  },
  {
    id: '3',
    name: 'CloudConsult Pro',
    logo: 'https://images.unsplash.com/photo-1677442136019-21780ecad995?w=200&h=100&fit=crop&crop=center',
    website: 'https://cloudconsult.example.com',
    description: {
      en: 'Premier consulting firm helping enterprises adopt cloud-first strategies and digital transformation.',
      zh: '帮助企业采用云优先策略和数字化转型的顶级咨询公司。',
      'zh-TW': '幫助企業採用雲優先策略和數位化轉型的頂級諮詢公司。',
    },
    category: partnerCategories[2],
    featured: false,
    testimonial: {
      quote: {
        en: 'Working together, we have successfully guided over 200 enterprises through their digital transformation journey.',
        zh: '通过合作，我们成功指导了200多家企业完成数字化转型之旅。',
        'zh-TW': '通過合作，我們成功指導了200多家企業完成數位化轉型之旅。',
      },
      author: 'Michael Chen',
      position: 'Managing Director, CloudConsult Pro',
    },
    caseStudyLink: '/case-studies/cloudconsult-transformation',
    partnershipDate: '2020-08-10',
    status: 'active',
  },
  {
    id: '4',
    name: 'GlobalTech Distributors',
    logo: 'https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=200&h=100&fit=crop&crop=center',
    website: 'https://globaltech.example.com',
    description: {
      en: 'International distribution network connecting technology solutions with businesses worldwide.',
      zh: '连接全球企业与技术解决方案的国际分销网络。',
      'zh-TW': '連接全球企業與技術解決方案的國際分銷網路。',
    },
    category: partnerCategories[3],
    featured: true,
    partnershipDate: '2019-05-22',
    status: 'active',
  },
  {
    id: '5',
    name: 'SecureNet Solutions',
    logo: 'https://images.unsplash.com/photo-1450101499163-c8848c66ca85?w=200&h=100&fit=crop&crop=center',
    website: 'https://securenet.example.com',
    description: {
      en: 'Cybersecurity specialists providing advanced threat protection and compliance solutions.',
      zh: '网络安全专家，提供先进的威胁防护和合规解决方案。',
      'zh-TW': '網路安全專家，提供先進的威脅防護和合規解決方案。',
    },
    category: partnerCategories[0],
    featured: false,
    testimonial: {
      quote: {
        en: 'Our joint security solutions have helped protect thousands of organizations from cyber threats.',
        zh: '我们的联合安全解决方案帮助保护了数千个组织免受网络威胁。',
        'zh-TW': '我們的聯合安全解決方案幫助保護了數千個組織免受網路威脅。',
      },
      author: 'Emily Rodriguez',
      position: 'Chief Security Officer, SecureNet Solutions',
    },
    partnershipDate: '2021-02-14',
    status: 'active',
  },
  {
    id: '6',
    name: 'InnovateLabs',
    logo: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=200&h=100&fit=crop&crop=center',
    website: 'https://innovatelabs.example.com',
    description: {
      en: 'Research and development partner focused on emerging technologies and innovation.',
      zh: '专注于新兴技术和创新的研发合作伙伴。',
      'zh-TW': '專注於新興技術和創新的研發合作夥伴。',
    },
    category: partnerCategories[0],
    featured: false,
    partnershipDate: '2023-01-30',
    status: 'active',
  },
];

// Helper functions
export const getFeaturedPartners = (): Partner[] => {
  return partners.filter(
    partner => partner.featured && partner.status === 'active'
  );
};

export const getPartnersByCategory = (categorySlug: string): Partner[] => {
  if (categorySlug === 'all') {
    return partners.filter(partner => partner.status === 'active');
  }
  return partners.filter(
    partner =>
      partner.category.slug === categorySlug && partner.status === 'active'
  );
};

export const getPartnerById = (id: string): Partner | undefined => {
  return partners.find(partner => partner.id === id);
};

export const searchPartners = (query: string): Partner[] => {
  const lowercaseQuery = query.toLowerCase();
  return partners.filter(
    partner =>
      partner.status === 'active' &&
      (partner.name.toLowerCase().includes(lowercaseQuery) ||
        partner.description.en.toLowerCase().includes(lowercaseQuery))
  );
};

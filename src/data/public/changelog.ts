import { ChangelogEntry, ChangelogCategory } from '@/types/public/changelog';

export const changelogCategories: ChangelogCategory[] = [
  {
    id: '1',
    name: {
      en: 'Platform Updates',
      zh: '平台更新',
      'zh-TW': '平台更新',
    },
    slug: 'platform',
    color: '#3B82F6',
    icon: 'monitor',
  },
  {
    id: '2',
    name: {
      en: 'API Changes',
      zh: 'API变更',
      'zh-TW': 'API變更',
    },
    slug: 'api',
    color: '#10B981',
    icon: 'code',
  },
  {
    id: '3',
    name: {
      en: 'Security Updates',
      zh: '安全更新',
      'zh-TW': '安全更新',
    },
    slug: 'security',
    color: '#EF4444',
    icon: 'shield',
  },
  {
    id: '4',
    name: {
      en: 'Integrations',
      zh: '集成功能',
      'zh-TW': '集成功能',
    },
    slug: 'integrations',
    color: '#8B5CF6',
    icon: 'link',
  },
  {
    id: '5',
    name: {
      en: 'Mobile App',
      zh: '移动应用',
      'zh-TW': '移動應用',
    },
    slug: 'mobile',
    color: '#F59E0B',
    icon: 'smartphone',
  },
];

export const changelog: ChangelogEntry[] = [
  {
    id: '1',
    version: '2.4.0',
    title: {
      en: 'Enhanced Dashboard Analytics',
      zh: '增强的仪表板分析',
      'zh-TW': '增強的儀表板分析',
    },
    description: {
      en: 'Major update to the analytics dashboard with new visualization options, real-time data updates, and improved performance.',
      zh: '分析仪表板的重大更新，包括新的可视化选项、实时数据更新和改进的性能。',
      'zh-TW':
        '分析儀表板的重大更新，包括新的可視化選項、實時數據更新和改進的性能。',
    },
    releaseDate: '2024-01-25T10:00:00Z',
    category: changelogCategories[0], // Platform Updates
    changes: [
      {
        id: '1-1',
        type: 'feature',
        title: {
          en: 'Real-time Data Streaming',
          zh: '实时数据流',
          'zh-TW': '實時數據流',
        },
        description: {
          en: 'Dashboard now updates in real-time without requiring page refresh',
          zh: '仪表板现在可以实时更新，无需刷新页面',
          'zh-TW': '儀表板現在可以實時更新，無需刷新頁面',
        },
        impact: 'high',
        pullRequestUrl: 'https://github.com/example/repo/pull/1234',
      },
      {
        id: '1-2',
        type: 'feature',
        title: {
          en: 'Custom Chart Builder',
          zh: '自定义图表构建器',
          'zh-TW': '自定義圖表構建器',
        },
        description: {
          en: 'Create custom charts and visualizations with drag-and-drop interface',
          zh: '使用拖放界面创建自定义图表和可视化',
          'zh-TW': '使用拖放界面創建自定義圖表和可視化',
        },
        impact: 'medium',
      },
      {
        id: '1-3',
        type: 'improvement',
        title: {
          en: 'Performance Optimization',
          zh: '性能优化',
          'zh-TW': '性能優化',
        },
        description: {
          en: 'Dashboard loading time reduced by 40% through code optimization',
          zh: '通过代码优化，仪表板加载时间减少了40%',
          'zh-TW': '通過代碼優化，儀表板加載時間減少了40%',
        },
        impact: 'medium',
      },
    ],
    featured: true,
    breaking: false,
    prerelease: false,
    downloadUrl: 'https://github.com/example/repo/releases/tag/v2.4.0',
    githubUrl: 'https://github.com/example/repo/releases/tag/v2.4.0',
    blogPostUrl: 'https://blog.example.com/dashboard-analytics-update',
  },
  {
    id: '2',
    version: '2.3.5',
    title: {
      en: 'API Rate Limiting & Security Enhancements',
      zh: 'API速率限制和安全增强',
      'zh-TW': 'API速率限制和安全增強',
    },
    description: {
      en: 'Important security update with new API rate limiting features and enhanced authentication mechanisms.',
      zh: '重要的安全更新，包括新的API速率限制功能和增强的身份验证机制。',
      'zh-TW': '重要的安全更新，包括新的API速率限制功能和增強的身份驗證機制。',
    },
    releaseDate: '2024-01-18T14:30:00Z',
    category: changelogCategories[2], // Security Updates
    changes: [
      {
        id: '2-1',
        type: 'security',
        title: {
          en: 'Enhanced API Authentication',
          zh: '增强的API身份验证',
          'zh-TW': '增強的API身份驗證',
        },
        description: {
          en: 'Implemented OAuth 2.0 with PKCE for improved API security',
          zh: '实施了带有PKCE的OAuth 2.0以提高API安全性',
          'zh-TW': '實施了帶有PKCE的OAuth 2.0以提高API安全性',
        },
        impact: 'high',
      },
      {
        id: '2-2',
        type: 'feature',
        title: {
          en: 'API Rate Limiting',
          zh: 'API速率限制',
          'zh-TW': 'API速率限制',
        },
        description: {
          en: 'Added configurable rate limiting to prevent API abuse',
          zh: '添加了可配置的速率限制以防止API滥用',
          'zh-TW': '添加了可配置的速率限制以防止API濫用',
        },
        impact: 'medium',
      },
      {
        id: '2-3',
        type: 'bugfix',
        title: {
          en: 'Session Management Fix',
          zh: '会话管理修复',
          'zh-TW': '會話管理修復',
        },
        description: {
          en: 'Fixed issue where sessions would not properly expire',
          zh: '修复了会话无法正确过期的问题',
          'zh-TW': '修復了會話無法正確過期的問題',
        },
        impact: 'high',
      },
    ],
    featured: true,
    breaking: false,
    prerelease: false,
    downloadUrl: 'https://github.com/example/repo/releases/tag/v2.3.5',
    githubUrl: 'https://github.com/example/repo/releases/tag/v2.3.5',
  },
  {
    id: '3',
    version: '2.3.0',
    title: {
      en: 'New Slack & Teams Integrations',
      zh: '新的Slack和Teams集成',
      'zh-TW': '新的Slack和Teams集成',
    },
    description: {
      en: 'Added native integrations for Slack and Microsoft Teams with real-time notifications and interactive commands.',
      zh: '为Slack和Microsoft Teams添加了原生集成，具有实时通知和交互式命令。',
      'zh-TW':
        '為Slack和Microsoft Teams添加了原生集成，具有實時通知和交互式命令。',
    },
    releaseDate: '2024-01-10T09:00:00Z',
    category: changelogCategories[3], // Integrations
    changes: [
      {
        id: '3-1',
        type: 'feature',
        title: {
          en: 'Slack Integration',
          zh: 'Slack集成',
          'zh-TW': 'Slack集成',
        },
        description: {
          en: 'Native Slack app with slash commands and interactive notifications',
          zh: '具有斜杠命令和交互式通知的原生Slack应用',
          'zh-TW': '具有斜杠命令和交互式通知的原生Slack應用',
        },
        impact: 'high',
      },
      {
        id: '3-2',
        type: 'feature',
        title: {
          en: 'Microsoft Teams Integration',
          zh: 'Microsoft Teams集成',
          'zh-TW': 'Microsoft Teams集成',
        },
        description: {
          en: 'Teams app with custom tabs and bot notifications',
          zh: '具有自定义选项卡和机器人通知的Teams应用',
          'zh-TW': '具有自定義選項卡和機器人通知的Teams應用',
        },
        impact: 'high',
      },
      {
        id: '3-3',
        type: 'improvement',
        title: {
          en: 'Webhook Reliability',
          zh: 'Webhook可靠性',
          'zh-TW': 'Webhook可靠性',
        },
        description: {
          en: 'Improved webhook delivery with retry mechanism and failure notifications',
          zh: '通过重试机制和失败通知改进了webhook传递',
          'zh-TW': '通過重試機制和失敗通知改進了webhook傳遞',
        },
        impact: 'medium',
      },
    ],
    featured: false,
    breaking: false,
    prerelease: false,
    downloadUrl: 'https://github.com/example/repo/releases/tag/v2.3.0',
    githubUrl: 'https://github.com/example/repo/releases/tag/v2.3.0',
  },
  {
    id: '4',
    version: '2.2.8',
    title: {
      en: 'Mobile App Performance Update',
      zh: '移动应用性能更新',
      'zh-TW': '移動應用性能更新',
    },
    description: {
      en: 'Significant performance improvements for the mobile app with faster loading times and reduced battery usage.',
      zh: '移动应用的显著性能改进，加载时间更快，电池使用量减少。',
      'zh-TW': '移動應用的顯著性能改進，加載時間更快，電池使用量減少。',
    },
    releaseDate: '2024-01-05T16:20:00Z',
    category: changelogCategories[4], // Mobile App
    changes: [
      {
        id: '4-1',
        type: 'improvement',
        title: {
          en: 'App Launch Speed',
          zh: '应用启动速度',
          'zh-TW': '應用啟動速度',
        },
        description: {
          en: 'Reduced app launch time by 60% through code optimization',
          zh: '通过代码优化将应用启动时间减少了60%',
          'zh-TW': '通過代碼優化將應用啟動時間減少了60%',
        },
        impact: 'high',
      },
      {
        id: '4-2',
        type: 'improvement',
        title: {
          en: 'Battery Optimization',
          zh: '电池优化',
          'zh-TW': '電池優化',
        },
        description: {
          en: 'Optimized background processes to reduce battery consumption by 30%',
          zh: '优化后台进程，将电池消耗减少30%',
          'zh-TW': '優化後台進程，將電池消耗減少30%',
        },
        impact: 'medium',
      },
      {
        id: '4-3',
        type: 'bugfix',
        title: {
          en: 'Crash on iOS 17',
          zh: 'iOS 17崩溃修复',
          'zh-TW': 'iOS 17崩潰修復',
        },
        description: {
          en: 'Fixed app crashes on iOS 17 devices during data synchronization',
          zh: '修复了iOS 17设备在数据同步期间的应用崩溃问题',
          'zh-TW': '修復了iOS 17設備在數據同步期間的應用崩潰問題',
        },
        impact: 'high',
      },
    ],
    featured: false,
    breaking: false,
    prerelease: false,
    downloadUrl: 'https://github.com/example/repo/releases/tag/v2.2.8',
    githubUrl: 'https://github.com/example/repo/releases/tag/v2.2.8',
  },
  {
    id: '5',
    version: '2.2.0',
    title: {
      en: 'GraphQL API & Breaking Changes',
      zh: 'GraphQL API和重大变更',
      'zh-TW': 'GraphQL API和重大變更',
    },
    description: {
      en: 'Introduction of GraphQL API alongside REST API. This release includes breaking changes to some REST endpoints.',
      zh: '在REST API的基础上引入GraphQL API。此版本包括对某些REST端点的重大更改。',
      'zh-TW':
        '在REST API的基礎上引入GraphQL API。此版本包括對某些REST端點的重大更改。',
    },
    releaseDate: '2023-12-20T11:00:00Z',
    category: changelogCategories[1], // API Changes
    changes: [
      {
        id: '5-1',
        type: 'feature',
        title: {
          en: 'GraphQL API',
          zh: 'GraphQL API',
          'zh-TW': 'GraphQL API',
        },
        description: {
          en: 'New GraphQL endpoint with full query capabilities and real-time subscriptions',
          zh: '具有完整查询功能和实时订阅的新GraphQL端点',
          'zh-TW': '具有完整查詢功能和實時訂閱的新GraphQL端點',
        },
        impact: 'high',
      },
      {
        id: '5-2',
        type: 'deprecated',
        title: {
          en: 'Legacy REST Endpoints',
          zh: '旧版REST端点',
          'zh-TW': '舊版REST端點',
        },
        description: {
          en: 'Several v1 REST endpoints are now deprecated and will be removed in v3.0',
          zh: '几个v1 REST端点现已弃用，将在v3.0中删除',
          'zh-TW': '幾個v1 REST端點現已棄用，將在v3.0中刪除',
        },
        impact: 'high',
      },
      {
        id: '5-3',
        type: 'removed',
        title: {
          en: 'XML Response Format',
          zh: 'XML响应格式',
          'zh-TW': 'XML響應格式',
        },
        description: {
          en: 'Removed support for XML response format, JSON is now the only supported format',
          zh: '删除了对XML响应格式的支持，JSON现在是唯一支持的格式',
          'zh-TW': '刪除了對XML響應格式的支持，JSON現在是唯一支持的格式',
        },
        impact: 'high',
      },
    ],
    featured: false,
    breaking: true,
    prerelease: false,
    downloadUrl: 'https://github.com/example/repo/releases/tag/v2.2.0',
    githubUrl: 'https://github.com/example/repo/releases/tag/v2.2.0',
    blogPostUrl: 'https://blog.example.com/graphql-api-introduction',
  },
  {
    id: '6',
    version: '2.5.0-beta.1',
    title: {
      en: 'AI-Powered Analytics (Beta)',
      zh: 'AI驱动的分析（测试版）',
      'zh-TW': 'AI驅動的分析（測試版）',
    },
    description: {
      en: 'Preview of our new AI-powered analytics features including predictive insights and automated reporting.',
      zh: '预览我们新的AI驱动分析功能，包括预测性洞察和自动化报告。',
      'zh-TW': '預覽我們新的AI驅動分析功能，包括預測性洞察和自動化報告。',
    },
    releaseDate: '2024-01-30T08:00:00Z',
    category: changelogCategories[0], // Platform Updates
    changes: [
      {
        id: '6-1',
        type: 'feature',
        title: {
          en: 'Predictive Analytics',
          zh: '预测分析',
          'zh-TW': '預測分析',
        },
        description: {
          en: 'AI-powered predictions for user behavior and business metrics',
          zh: 'AI驱动的用户行为和业务指标预测',
          'zh-TW': 'AI驅動的用戶行為和業務指標預測',
        },
        impact: 'high',
      },
      {
        id: '6-2',
        type: 'feature',
        title: {
          en: 'Automated Insights',
          zh: '自动化洞察',
          'zh-TW': '自動化洞察',
        },
        description: {
          en: 'Automatically generated insights and recommendations based on data patterns',
          zh: '基于数据模式自动生成的洞察和建议',
          'zh-TW': '基於數據模式自動生成的洞察和建議',
        },
        impact: 'medium',
      },
    ],
    featured: true,
    breaking: false,
    prerelease: true,
    downloadUrl: 'https://github.com/example/repo/releases/tag/v2.5.0-beta.1',
    githubUrl: 'https://github.com/example/repo/releases/tag/v2.5.0-beta.1',
  },
];

// Helper functions
export const getFeaturedChangelog = (): ChangelogEntry[] => {
  return changelog.filter(entry => entry.featured);
};

export const getChangelogByCategory = (
  categorySlug: string
): ChangelogEntry[] => {
  return changelog.filter(entry => entry.category.slug === categorySlug);
};

export const getChangelogByVersion = (version: string): ChangelogEntry[] => {
  return changelog.filter(entry => entry.version.includes(version));
};

export const getBreakingChanges = (): ChangelogEntry[] => {
  return changelog.filter(entry => entry.breaking);
};

export const getPrereleases = (): ChangelogEntry[] => {
  return changelog.filter(entry => entry.prerelease);
};

export const searchChangelog = (query: string): ChangelogEntry[] => {
  const lowercaseQuery = query.toLowerCase();

  return changelog.filter(
    entry =>
      entry.version.toLowerCase().includes(lowercaseQuery) ||
      entry.title.en.toLowerCase().includes(lowercaseQuery) ||
      entry.description.en.toLowerCase().includes(lowercaseQuery) ||
      entry.changes.some(
        change =>
          change.title.en.toLowerCase().includes(lowercaseQuery) ||
          change.description.en.toLowerCase().includes(lowercaseQuery)
      )
  );
};

export const getChangelogById = (id: string): ChangelogEntry | undefined => {
  return changelog.find(entry => entry.id === id);
};

export const getLatestVersion = (): ChangelogEntry | undefined => {
  return changelog
    .filter(entry => !entry.prerelease)
    .sort(
      (a, b) =>
        new Date(b.releaseDate).getTime() - new Date(a.releaseDate).getTime()
    )[0];
};

export const getVersionHistory = (limit: number = 10): ChangelogEntry[] => {
  return [...changelog]
    .sort(
      (a, b) =>
        new Date(b.releaseDate).getTime() - new Date(a.releaseDate).getTime()
    )
    .slice(0, limit);
};

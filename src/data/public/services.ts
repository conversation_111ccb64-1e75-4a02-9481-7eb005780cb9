import { Service, ServiceCategory } from '@/types/public/services';

export const serviceCategories: ServiceCategory[] = [
  {
    id: 'consulting',
    name: {
      en: 'Consulting',
      zh: '咨询服务',
      'zh-TW': '諮詢服務',
    },
    description: {
      en: 'Strategic consulting and advisory services',
      zh: '战略咨询和顾问服务',
      'zh-TW': '戰略諮詢和顧問服務',
    },
    icon: 'users',
  },
  {
    id: 'development',
    name: {
      en: 'Development',
      zh: '开发服务',
      'zh-TW': '開發服務',
    },
    description: {
      en: 'Custom software development solutions',
      zh: '定制软件开发解决方案',
      'zh-TW': '定制軟體開發解決方案',
    },
    icon: 'code',
  },
  {
    id: 'support',
    name: {
      en: 'Support',
      zh: '支持服务',
      'zh-TW': '支援服務',
    },
    description: {
      en: 'Ongoing support and maintenance services',
      zh: '持续支持和维护服务',
      'zh-TW': '持續支援和維護服務',
    },
    icon: 'headphones',
  },
  {
    id: 'training',
    name: {
      en: 'Training',
      zh: '培训服务',
      'zh-TW': '培訓服務',
    },
    description: {
      en: 'Professional training and education programs',
      zh: '专业培训和教育项目',
      'zh-TW': '專業培訓和教育項目',
    },
    icon: 'graduation-cap',
  },
];

export const services: Service[] = [
  {
    id: 'strategic-consulting',
    name: {
      en: 'Strategic Consulting',
      zh: '战略咨询',
      'zh-TW': '戰略諮詢',
    },
    description: {
      en: 'Comprehensive strategic planning and business transformation consulting to help your organization achieve its goals.',
      zh: '全面的战略规划和业务转型咨询，帮助您的组织实现目标。',
      'zh-TW': '全面的戰略規劃和業務轉型諮詢，幫助您的組織實現目標。',
    },
    features: [
      {
        en: 'Business strategy development',
        zh: '业务战略制定',
        'zh-TW': '業務戰略制定',
      },
      {
        en: 'Market analysis and research',
        zh: '市场分析和研究',
        'zh-TW': '市場分析和研究',
      },
      {
        en: 'Digital transformation roadmap',
        zh: '数字化转型路线图',
        'zh-TW': '數位化轉型路線圖',
      },
      {
        en: 'Performance optimization',
        zh: '性能优化',
        'zh-TW': '性能優化',
      },
    ],
    icon: 'chart-line',
    category: 'consulting',
    pricing: {
      startingPrice: 5000,
      currency: 'USD',
      period: 'month',
    },
    popular: true,
    ctaText: {
      en: 'Get Started',
      zh: '开始使用',
      'zh-TW': '開始使用',
    },
    ctaLink: '/contact',
  },
  {
    id: 'web-development',
    name: {
      en: 'Web Development',
      zh: 'Web开发',
      'zh-TW': 'Web開發',
    },
    description: {
      en: 'Custom web application development using modern technologies and best practices for scalable solutions.',
      zh: '使用现代技术和最佳实践进行定制Web应用程序开发，提供可扩展的解决方案。',
      'zh-TW':
        '使用現代技術和最佳實踐進行定制Web應用程式開發，提供可擴展的解決方案。',
    },
    features: [
      {
        en: 'React & TypeScript development',
        zh: 'React和TypeScript开发',
        'zh-TW': 'React和TypeScript開發',
      },
      {
        en: 'Responsive design implementation',
        zh: '响应式设计实现',
        'zh-TW': '響應式設計實現',
      },
      {
        en: 'API integration and development',
        zh: 'API集成和开发',
        'zh-TW': 'API整合和開發',
      },
      {
        en: 'Performance optimization',
        zh: '性能优化',
        'zh-TW': '性能優化',
      },
    ],
    icon: 'globe',
    category: 'development',
    pricing: {
      startingPrice: 8000,
      currency: 'USD',
      period: 'month',
    },
    ctaText: {
      en: 'Start Project',
      zh: '开始项目',
      'zh-TW': '開始項目',
    },
    ctaLink: '/contact',
  },
  {
    id: 'mobile-development',
    name: {
      en: 'Mobile Development',
      zh: '移动开发',
      'zh-TW': '移動開發',
    },
    description: {
      en: 'Native and cross-platform mobile application development for iOS and Android platforms.',
      zh: '为iOS和Android平台提供原生和跨平台移动应用程序开发。',
      'zh-TW': '為iOS和Android平台提供原生和跨平台移動應用程式開發。',
    },
    features: [
      {
        en: 'iOS and Android development',
        zh: 'iOS和Android开发',
        'zh-TW': 'iOS和Android開發',
      },
      {
        en: 'React Native solutions',
        zh: 'React Native解决方案',
        'zh-TW': 'React Native解決方案',
      },
      {
        en: 'App Store optimization',
        zh: '应用商店优化',
        'zh-TW': '應用商店優化',
      },
      {
        en: 'Push notifications integration',
        zh: '推送通知集成',
        'zh-TW': '推送通知整合',
      },
    ],
    icon: 'smartphone',
    category: 'development',
    pricing: {
      startingPrice: 12000,
      currency: 'USD',
      period: 'month',
    },
    ctaText: {
      en: 'Build App',
      zh: '构建应用',
      'zh-TW': '構建應用',
    },
    ctaLink: '/contact',
  },
  {
    id: 'technical-support',
    name: {
      en: 'Technical Support',
      zh: '技术支持',
      'zh-TW': '技術支援',
    },
    description: {
      en: '24/7 technical support and maintenance services to keep your systems running smoothly.',
      zh: '24/7技术支持和维护服务，确保您的系统平稳运行。',
      'zh-TW': '24/7技術支援和維護服務，確保您的系統平穩運行。',
    },
    features: [
      {
        en: '24/7 monitoring and support',
        zh: '24/7监控和支持',
        'zh-TW': '24/7監控和支援',
      },
      {
        en: 'Bug fixes and updates',
        zh: '错误修复和更新',
        'zh-TW': '錯誤修復和更新',
      },
      {
        en: 'Performance monitoring',
        zh: '性能监控',
        'zh-TW': '性能監控',
      },
      {
        en: 'Security updates',
        zh: '安全更新',
        'zh-TW': '安全更新',
      },
    ],
    icon: 'shield-check',
    category: 'support',
    pricing: {
      startingPrice: 2000,
      currency: 'USD',
      period: 'month',
    },
    ctaText: {
      en: 'Get Support',
      zh: '获取支持',
      'zh-TW': '獲取支援',
    },
    ctaLink: '/support',
  },
  {
    id: 'cloud-migration',
    name: {
      en: 'Cloud Migration',
      zh: '云迁移',
      'zh-TW': '雲遷移',
    },
    description: {
      en: 'Seamless migration of your applications and data to cloud platforms with minimal downtime.',
      zh: '将您的应用程序和数据无缝迁移到云平台，停机时间最短。',
      'zh-TW': '將您的應用程式和數據無縫遷移到雲平台，停機時間最短。',
    },
    features: [
      {
        en: 'AWS, Azure, GCP migration',
        zh: 'AWS、Azure、GCP迁移',
        'zh-TW': 'AWS、Azure、GCP遷移',
      },
      {
        en: 'Data migration and backup',
        zh: '数据迁移和备份',
        'zh-TW': '數據遷移和備份',
      },
      {
        en: 'Infrastructure optimization',
        zh: '基础设施优化',
        'zh-TW': '基礎設施優化',
      },
      {
        en: 'Cost optimization',
        zh: '成本优化',
        'zh-TW': '成本優化',
      },
    ],
    icon: 'cloud-upload',
    category: 'consulting',
    pricing: {
      startingPrice: 15000,
      currency: 'USD',
      period: 'one-time',
    },
    ctaText: {
      en: 'Migrate Now',
      zh: '立即迁移',
      'zh-TW': '立即遷移',
    },
    ctaLink: '/contact',
  },
  {
    id: 'team-training',
    name: {
      en: 'Team Training',
      zh: '团队培训',
      'zh-TW': '團隊培訓',
    },
    description: {
      en: 'Comprehensive training programs to upskill your development team with latest technologies.',
      zh: '全面的培训项目，帮助您的开发团队掌握最新技术。',
      'zh-TW': '全面的培訓項目，幫助您的開發團隊掌握最新技術。',
    },
    features: [
      {
        en: 'Custom curriculum development',
        zh: '定制课程开发',
        'zh-TW': '定制課程開發',
      },
      {
        en: 'Hands-on workshops',
        zh: '实践研讨会',
        'zh-TW': '實踐研討會',
      },
      {
        en: 'Certification programs',
        zh: '认证项目',
        'zh-TW': '認證項目',
      },
      {
        en: 'Ongoing mentorship',
        zh: '持续指导',
        'zh-TW': '持續指導',
      },
    ],
    icon: 'users-cog',
    category: 'training',
    pricing: {
      startingPrice: 3000,
      currency: 'USD',
      period: 'month',
    },
    ctaText: {
      en: 'Start Training',
      zh: '开始培训',
      'zh-TW': '開始培訓',
    },
    ctaLink: '/contact',
  },
];

export const getServicesByCategory = (categoryId: string): Service[] => {
  return services.filter(service => service.category === categoryId);
};

export const getServiceById = (id: string): Service | undefined => {
  return services.find(service => service.id === id);
};

export const getFeaturedServices = (): Service[] => {
  return services.filter(service => service.popular);
};

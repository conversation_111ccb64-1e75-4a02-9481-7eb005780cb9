/**
 * Support Data
 *
 * This file contains the structured data for the Support page, including
 * categories, articles, popular articles, and support options.
 * All content is organized with translations for supported languages (en, zh, zh-TW).
 */

import type {
  SupportCategoryInfo,
  SupportArticle,
  SupportArticleDetail,
} from '@/types/public/support';

/**
 * Support categories with translations
 */
export function getSupportCategories(): SupportCategoryInfo[] {
  return [
    {
      id: 'technical',
      name: {
        en: 'Technical Support',
        zh: '技术支持',
        'zh-TW': '技術支持',
      },
      description: {
        en: 'Help with technical issues and platform functionality',
        zh: '帮助解决技术问题和平台功能',
        'zh-TW': '幫助解決技術問題和平台功能',
      },
      icon: 'M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z M15 12a3 3 0 11-6 0 3 3 0 016 0z',
      color: '#3b82f6',
      articleCount: 8,
    },
    {
      id: 'billing',
      name: {
        en: 'Billing & Payments',
        zh: '账单和付款',
        'zh-TW': '帳單和付款',
      },
      description: {
        en: 'Questions about billing, payments, and subscriptions',
        zh: '关于账单、付款和订阅的问题',
        'zh-TW': '關於帳單、付款和訂閱的問題',
      },
      icon: 'M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z',
      color: '#10b981',
      articleCount: 6,
    },
    {
      id: 'account',
      name: {
        en: 'Account Management',
        zh: '账户管理',
        'zh-TW': '帳戶管理',
      },
      description: {
        en: 'Help with your account settings and preferences',
        zh: '帮助您管理账户设置和偏好',
        'zh-TW': '幫助您管理帳戶設置和偏好',
      },
      icon: 'M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z',
      color: '#8b5cf6',
      articleCount: 12,
    },
    {
      id: 'feature',
      name: {
        en: 'Feature Requests',
        zh: '功能请求',
        'zh-TW': '功能請求',
      },
      description: {
        en: 'Suggest new features or improvements',
        zh: '建议新功能或改进',
        'zh-TW': '建議新功能或改進',
      },
      icon: 'M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z',
      color: '#f59e0b',
      articleCount: 4,
    },
  ];
}

/**
 * Get popular support articles
 */
export function getPopularArticles(): SupportArticle[] {
  return [
    {
      id: 'login-issues',
      title: {
        en: 'Troubleshooting Login Issues',
        zh: '登录问题故障排除',
        'zh-TW': '登錄問題故障排除',
      },
      excerpt: {
        en: 'Common solutions for login and authentication problems',
        zh: '登录和身份验证问题的常见解决方案',
        'zh-TW': '登錄和身份驗證問題的常見解決方案',
      },
      category: 'technical',
      readingTime: 5,
      lastUpdated: '2023-11-20',
      featured: true,
      tags: ['login', 'authentication', 'troubleshooting'],
      helpfulness: 4.5,
      views: 1250,
    },
    {
      id: 'billing-faq',
      title: {
        en: 'Billing FAQ',
        zh: '账单常见问题',
        'zh-TW': '帳單常見問題',
      },
      excerpt: {
        en: 'Answers to common questions about billing and payments',
        zh: '关于账单和付款的常见问题解答',
        'zh-TW': '關於帳單和付款的常見問題解答',
      },
      category: 'billing',
      readingTime: 4,
      lastUpdated: '2023-11-15',
      featured: true,
      tags: ['billing', 'payments', 'subscription'],
      helpfulness: 4.8,
      views: 980,
    },
    {
      id: 'account-security',
      title: {
        en: 'Account Security Best Practices',
        zh: '账户安全最佳实践',
        'zh-TW': '帳戶安全最佳實踐',
      },
      excerpt: {
        en: 'Learn how to keep your account secure and protected',
        zh: '了解如何保持账户安全和受保护',
        'zh-TW': '了解如何保持帳戶安全和受保護',
      },
      category: 'account',
      readingTime: 6,
      lastUpdated: '2023-11-10',
      featured: true,
      tags: ['security', 'password', '2fa'],
      helpfulness: 4.7,
      views: 850,
    },
  ];
}

export async function getSupportArticleById(
  id: string
): Promise<SupportArticleDetail | null> {
  // Mock detailed articles - in real app this would fetch from API
  const mockDetailedArticles: SupportArticleDetail[] = [
    {
      id: 'login-issues',
      title: {
        en: 'Troubleshooting Login Issues',
        zh: '登录问题故障排除',
        'zh-TW': '登錄問題故障排除',
      },
      excerpt: {
        en: 'Common solutions for login and authentication problems',
        zh: '登录和身份验证问题的常见解决方案',
        'zh-TW': '登錄和身份驗證問題的常見解決方案',
      },
      category: 'technical',
      readingTime: 5,
      lastUpdated: '2023-11-20',
      featured: true,
      tags: ['login', 'authentication', 'troubleshooting'],
      helpfulness: 4.5,
      views: 1250,
      content: [
        {
          type: 'heading',
          content: {
            en: 'Common Login Issues',
            zh: '常见登录问题',
            'zh-TW': '常見登錄問題',
          },
        },
        {
          type: 'paragraph',
          content: {
            en: "If you're having trouble logging into your account, try these troubleshooting steps.",
            zh: '如果您在登录账户时遇到问题，请尝试这些故障排除步骤。',
            'zh-TW': '如果您在登錄帳戶時遇到問題，請嘗試這些故障排除步驟。',
          },
        },
        {
          type: 'list',
          content: {
            en: 'Try these solutions:',
            zh: '尝试这些解决方案：',
            'zh-TW': '嘗試這些解決方案：',
          },
          items: [
            {
              en: 'Check your email and password are correct',
              zh: '检查您的电子邮件和密码是否正确',
              'zh-TW': '檢查您的電子郵件和密碼是否正確',
            },
            {
              en: 'Clear your browser cache and cookies',
              zh: '清除浏览器缓存和cookie',
              'zh-TW': '清除瀏覽器緩存和cookie',
            },
            {
              en: 'Try using an incognito/private browsing window',
              zh: '尝试使用隐身/私人浏览窗口',
              'zh-TW': '嘗試使用隱身/私人瀏覽窗口',
            },
          ],
        },
      ],
    },
  ];

  return mockDetailedArticles.find(article => article.id === id) || null;
}

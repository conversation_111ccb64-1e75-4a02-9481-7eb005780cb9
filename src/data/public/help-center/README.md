# Help Center Data Structure

This directory contains the help center data structure and individual article files for the portal's help system.

## Directory Structure

```
/data/public/help-center/
├── README.md                    # This file - developer guide
├── index.ts                     # Main export file with categories, guides, resources
├── quick-start.ts              # Individual article files
└── [article-id].ts            # Future article files
```

## How to Add a New Article

### 1. Create the Article File

Create a new file named `[article-id].ts` in this directory:

```typescript
/**
 * [Article Title] Article
 */

import { HelpArticleDetail } from '@/types/public/help-center';

export const [articleName]Article: HelpArticleDetail = {
  id: 'article-id',
  title: {
    en: 'Article Title',
    zh: '文章标题',
    'zh-TW': '文章標題',
  },
  summary: {
    en: 'Brief summary of the article',
    zh: '文章简要摘要',
    'zh-TW': '文章簡要摘要',
  },
  excerpt: {
    en: 'Longer description for the article page',
    zh: '文章页面的详细描述',
    'zh-TW': '文章頁面的詳細描述',
  },
  category: 'category-id', // getting-started, features, account, troubleshooting
  readingTime: 5, // estimated reading time in minutes
  featured: false, // whether to feature this article
  tags: ['tag1', 'tag2'], // relevant tags
  lastUpdated: '2023-11-20', // last update date
  content: [
    {
      type: 'heading',
      content: {
        en: 'Section Heading',
        zh: '章节标题',
        'zh-TW': '章節標題',
      },
    },
    {
      type: 'paragraph',
      content: {
        en: 'Paragraph content...',
        zh: '段落内容...',
        'zh-TW': '段落內容...',
      },
    },
    // Add more content sections as needed
  ],
  relatedArticles: ['related-article-1', 'related-article-2'],
};
```

### 2. Add to Category in index.ts

Add your article to the appropriate category in `HELP_CATEGORIES`:

```typescript
{
  id: 'category-id',
  name: { /* category name translations */ },
  icon: HELP_ICONS.categoryIcon,
  articles: [
    // existing articles...
    {
      id: 'your-article-id',
      title: {
        en: 'Your Article Title',
        zh: '您的文章标题',
        'zh-TW': '您的文章標題',
      },
      summary: {
        en: 'Article summary',
        zh: '文章摘要',
        'zh-TW': '文章摘要',
      },
    },
  ],
},
```

### 3. Import and Export in index.ts

Add the import and export:

```typescript
// Add import
import { yourArticleNameArticle } from './your-article-id';

// Add to allHelpArticles array
export const allHelpArticles: HelpArticleDetail[] = [
  // existing articles...
  yourArticleNameArticle,
];
```

## Content Types

The `content` array supports these types:

- `heading` - Section headings
- `paragraph` - Regular text content
- `list` - Bulleted lists with items array
- `code` - Code blocks
- `note` - Information notes (blue styling)
- `warning` - Warning messages (yellow/red styling)

## Categories

Current categories:

- `getting-started` - Onboarding and basic setup
- `features` - Platform features and functionality
- `account` - Account management and settings
- `troubleshooting` - Problem solving and fixes

## Routes

Articles are accessible at:

- `/help` - Main help center page
- `/help/[article-id]` - Individual article pages

## Translations

All text content should include translations for:

- `en` - English (required)
- `zh` - Simplified Chinese
- `zh-TW` - Traditional Chinese

## Best Practices

1. **Article IDs**: Use kebab-case (e.g., `password-management`)
2. **Reading Time**: Estimate 200 words per minute
3. **Content Structure**: Start with overview, then step-by-step instructions
4. **Related Articles**: Link to 2-3 relevant articles
5. **Tags**: Use relevant, searchable keywords
6. **Updates**: Update `lastUpdated` when making changes

## Testing

After adding an article:

1. Check `/help` page shows the article in the correct category
2. Verify `/help/[article-id]` loads the full content
3. Test in all supported languages
4. Verify related articles links work

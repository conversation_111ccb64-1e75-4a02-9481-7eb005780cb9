# Testing Setup

This directory contains the Vitest testing configuration and test files for the base portal dashboard.

## Structure

- `setup.ts` - Test setup configuration including mocks and global test utilities
- `test-utils.tsx` - Custom React testing utilities with providers
- `services/` - Service layer tests
  - `system.test.ts` - Tests for the SystemService

## Running Tests

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Run tests with UI
npm run test:ui
```

## Writing Tests

### Service Tests
Service tests should be placed in the `services/` directory and follow the naming convention `*.test.ts`.

### Component Tests
Component tests should be placed next to their components and follow the naming convention `*.test.tsx`.

### Test Structure
All tests should use Vitest and Testing Library. The `test-utils.tsx` file provides a custom render function that wraps components with necessary providers.

## Example Test

```typescript
import { describe, it, expect, vi } from 'vitest';
import { render, screen } from '@/test/test-utils';
import { MyComponent } from './MyComponent';

describe('MyComponent', () => {
  it('should render correctly', () => {
    render(<MyComponent />);
    expect(screen.getByText('Hello World')).toBeInTheDocument();
  });
});
```

## Mocking

Use Vitest's built-in mocking capabilities:

```typescript
// Mock a module
vi.mock('@/services/api', () => ({
  fetchData: vi.fn(),
}));

// Mock a function
const mockFn = vi.fn();
```

## Coverage

The coverage configuration excludes:
- `node_modules/`
- `src/test/` (test utilities)
- `**/*.d.ts` (type definitions)
- `**/*.test.{ts,tsx}` (test files)
- `**/*.spec.{ts,tsx}` (spec files)

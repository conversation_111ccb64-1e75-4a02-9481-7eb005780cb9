/**
 * SystemService Tests
 *
 * This file contains comprehensive tests for the SystemService,
 * covering health checks, API availability monitoring, and caching functionality.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { SystemService } from '@/services/system';

// Mock the axios module
vi.mock('@/utils/api/axios', () => ({
  default: {
    get: vi.fn(),
  },
}));

import axiosServices from '@/utils/api/axios';
const mockAxiosGet = vi.mocked(axiosServices.get);

describe('SystemService', () => {
  beforeEach(() => {
    // Reset API status before each test
    SystemService.resetApiStatus();
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('checkApiAvailability', () => {
    it('should return true when API is available', async () => {
      // Mock successful API response
      mockAxiosGet.mockResolvedValueOnce({ data: { status: 'ok' } });

      const result = await SystemService.checkApiAvailability();

      expect(result).toBe(true);
      expect(mockAxiosGet).toHaveBeenCalledWith('/health', { timeout: 3000 });
    });

    it('should return false when API is not available', async () => {
      // Mock API error
      mockAxiosGet.mockRejectedValueOnce(new Error('Network Error'));

      const result = await SystemService.checkApiAvailability();

      expect(result).toBe(false);
      expect(mockAxiosGet).toHaveBeenCalledWith('/health', { timeout: 3000 });
    });

    it('should use custom timeout when provided', async () => {
      mockAxiosGet.mockResolvedValueOnce({ data: { status: 'ok' } });

      await SystemService.checkApiAvailability(5000);

      expect(mockAxiosGet).toHaveBeenCalledWith('/health', { timeout: 5000 });
    });

    it('should cache API status after successful check', async () => {
      mockAxiosGet.mockResolvedValueOnce({ data: { status: 'ok' } });

      await SystemService.checkApiAvailability();

      const status = SystemService.getApiStatus();
      expect(status).toBeTruthy();
      expect(status?.available).toBe(true);
      expect(status?.lastChecked).toBeInstanceOf(Date);
      expect(status?.responseTime).toBeGreaterThanOrEqual(0);
    });

    it('should cache API status after failed check', async () => {
      mockAxiosGet.mockRejectedValueOnce(new Error('Network Error'));

      await SystemService.checkApiAvailability();

      const status = SystemService.getApiStatus();
      expect(status).toBeTruthy();
      expect(status?.available).toBe(false);
      expect(status?.error).toBe('Network Error');
    });
  });

  describe('getApiStatus', () => {
    it('should return null when no previous check', () => {
      const status = SystemService.getApiStatus();
      expect(status).toBeNull();
    });

    it('should return cached status after API check', async () => {
      mockAxiosGet.mockResolvedValueOnce({ data: { status: 'ok' } });

      await SystemService.checkApiAvailability();
      const status = SystemService.getApiStatus();

      expect(status).toBeTruthy();
      expect(status?.available).toBe(true);
    });
  });

  describe('isApiAvailable', () => {
    it('should return null when no previous check', () => {
      const result = SystemService.isApiAvailable();
      expect(result).toBeNull();
    });

    it('should return true after successful check', async () => {
      mockAxiosGet.mockResolvedValueOnce({ data: { status: 'ok' } });

      await SystemService.checkApiAvailability();
      const result = SystemService.isApiAvailable();

      expect(result).toBe(true);
    });

    it('should return false after failed check', async () => {
      mockAxiosGet.mockRejectedValueOnce(new Error('Network Error'));

      await SystemService.checkApiAvailability();
      const result = SystemService.isApiAvailable();

      expect(result).toBe(false);
    });
  });

  describe('resetApiStatus', () => {
    it('should clear cached status', async () => {
      mockAxiosGet.mockResolvedValueOnce({ data: { status: 'ok' } });

      await SystemService.checkApiAvailability();
      expect(SystemService.getApiStatus()).toBeTruthy();

      SystemService.resetApiStatus();
      expect(SystemService.getApiStatus()).toBeNull();
    });
  });

  describe('ping', () => {
    it('should return true when ping succeeds', async () => {
      mockAxiosGet.mockResolvedValueOnce({ data: { status: 'ok' } });

      const result = await SystemService.ping();

      expect(result).toBe(true);
      expect(mockAxiosGet).toHaveBeenCalledWith('/health', { timeout: 1000 });
    });

    it('should return false when ping fails', async () => {
      mockAxiosGet.mockRejectedValueOnce(new Error('Network Error'));

      const result = await SystemService.ping();

      expect(result).toBe(false);
    });

    it('should use custom timeout for ping', async () => {
      mockAxiosGet.mockResolvedValueOnce({ data: { status: 'ok' } });

      await SystemService.ping(2000);

      expect(mockAxiosGet).toHaveBeenCalledWith('/health', { timeout: 2000 });
    });
  });

  describe('getLastResponseTime', () => {
    it('should return null when no previous check', () => {
      const responseTime = SystemService.getLastResponseTime();
      expect(responseTime).toBeNull();
    });

    it('should return response time after API check', async () => {
      mockAxiosGet.mockResolvedValueOnce({ data: { status: 'ok' } });

      await SystemService.checkApiAvailability();
      const responseTime = SystemService.getLastResponseTime();

      expect(responseTime).toBeGreaterThanOrEqual(0);
      expect(typeof responseTime).toBe('number');
    });
  });

  describe('isStatusFresh', () => {
    it('should return false when no previous check', () => {
      const isFresh = SystemService.isStatusFresh();
      expect(isFresh).toBe(false);
    });

    it('should return true for fresh status', async () => {
      mockAxiosGet.mockResolvedValueOnce({ data: { status: 'ok' } });

      await SystemService.checkApiAvailability();
      const isFresh = SystemService.isStatusFresh(5);

      expect(isFresh).toBe(true);
    });

    it('should return false for stale status', async () => {
      mockAxiosGet.mockResolvedValueOnce({ data: { status: 'ok' } });

      await SystemService.checkApiAvailability();
      
      // Mock the status to be old by directly manipulating the private property
      const status = SystemService.getApiStatus();
      if (status) {
        status.lastChecked = new Date(Date.now() - 10 * 60 * 1000); // 10 minutes ago
      }

      const isFresh = SystemService.isStatusFresh(5);
      expect(isFresh).toBe(false);
    });
  });

  describe('getOrCheckApiAvailability', () => {
    it('should return cached result when fresh', async () => {
      mockAxiosGet.mockResolvedValueOnce({ data: { status: 'ok' } });

      // First call - should hit API
      await SystemService.checkApiAvailability();
      
      // Second call - should use cache
      const result = await SystemService.getOrCheckApiAvailability();

      expect(result).toBe(true);
      expect(mockAxiosGet).toHaveBeenCalledTimes(1);
    });

    it('should perform fresh check when no cache', async () => {
      mockAxiosGet.mockResolvedValueOnce({ data: { status: 'ok' } });

      const result = await SystemService.getOrCheckApiAvailability();

      expect(result).toBe(true);
      expect(mockAxiosGet).toHaveBeenCalledTimes(1);
    });

    it('should perform fresh check when cache is stale', async () => {
      mockAxiosGet.mockResolvedValue({ data: { status: 'ok' } });

      // First call
      await SystemService.checkApiAvailability();
      
      // Make status stale
      const status = SystemService.getApiStatus();
      if (status) {
        status.lastChecked = new Date(Date.now() - 10 * 60 * 1000); // 10 minutes ago
      }

      // Second call - should hit API again
      const result = await SystemService.getOrCheckApiAvailability(5);

      expect(result).toBe(true);
      expect(mockAxiosGet).toHaveBeenCalledTimes(2);
    });
  });

  describe('Performance monitoring', () => {
    it('should track response time for performance monitoring', async () => {
      mockAxiosGet.mockResolvedValueOnce({ data: { status: 'ok' } });

      await SystemService.checkApiAvailability();
      
      const responseTime = SystemService.getLastResponseTime();
      expect(responseTime).toBeGreaterThanOrEqual(0);
    });

    it('should handle slow API responses', async () => {
      // Mock slow response
      mockAxiosGet.mockImplementationOnce(
        () => new Promise(resolve => setTimeout(() => resolve({ data: { status: 'ok' } }), 100))
      );

      await SystemService.checkApiAvailability();
      
      const responseTime = SystemService.getLastResponseTime();
      expect(responseTime).toBeGreaterThanOrEqual(100);
    });
  });

  describe('Error handling', () => {
    it('should handle network errors gracefully', async () => {
      mockAxiosGet.mockRejectedValueOnce(new Error('Network Error'));

      const result = await SystemService.checkApiAvailability();

      expect(result).toBe(false);
      
      const status = SystemService.getApiStatus();
      expect(status?.available).toBe(false);
      expect(status?.error).toBe('Network Error');
    });

    it('should handle timeout errors', async () => {
      mockAxiosGet.mockRejectedValueOnce(new Error('Timeout'));

      const result = await SystemService.checkApiAvailability();

      expect(result).toBe(false);
      
      const status = SystemService.getApiStatus();
      expect(status?.available).toBe(false);
      expect(status?.error).toBe('Timeout');
    });

    it('should handle unknown errors', async () => {
      mockAxiosGet.mockRejectedValueOnce('Unknown error');

      const result = await SystemService.checkApiAvailability();

      expect(result).toBe(false);
      
      const status = SystemService.getApiStatus();
      expect(status?.available).toBe(false);
      expect(status?.error).toBe('Unknown error');
    });
  });
});

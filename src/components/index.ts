/**
 * Component Barrel Exports
 *
 * Organized component exports following the established directory structure:
 * - base/ - Foundation components for layout and core functionality
 * - layout/ - Navigation and layout components
 * - forms/ - Form-related components
 * - features/ - Domain-specific feature components
 * - common/ - Reusable business components
 * - ui/ - Third-party UI library components
 */

// ==========================================
// BASE COMPONENTS
// ==========================================
export * from './base/loadable';
export * from './base/enhanced-loadable';
export * from './base/boundary/error-boundary';
export * from './base/boundary/api-error-boundary';
export * from './base/boundary/form-error-boundary';
export * from './base/boundary/async-error-boundary';
export * from './base/boundary/data-error-boundary';
export * from './base/boundary/route-error-boundary';
export * from './base/loader';
export * from './base/page-loader';
export * from './base/locales';

// ==========================================
// LAYOUT COMPONENTS
// ==========================================
export * from './layout/app-sidebar';
export * from './layout/user-sidebar';
export * from './layout/nav-breadcrumb';
export * from './layout/nav-main';
export * from './layout/nav-user';
export * from './layout/nav-projects';
export * from './layout/navbar-public';
export * from './layout/footer-public';
export * from './layout/team-switcher';

// ==========================================
// FORM COMPONENTS
// ==========================================
export * from './forms/login-form';
export * from './forms/login-form-alt';

// ==========================================
// FEATURE COMPONENTS
// ==========================================
export * from './features/login/password-input';

// ==========================================
// COMMON COMPONENTS
// ==========================================
export * from './common/markdown-renderer-lazy';

// ==========================================
// UI COMPONENTS (Third-party libraries)
// ==========================================

// ShadCN UI Components (selective re-export)
export * from './ui/shadcn/button';
export * from './ui/shadcn/dialog';
export * from './ui/shadcn/sidebar';
export * from './ui/shadcn/hover-card';
export * from './ui/shadcn/card';
export * from './ui/shadcn/badge';
export * from './ui/shadcn/input';
export * from './ui/shadcn/textarea';
export * from './ui/shadcn/select';
export * from './ui/shadcn/alert';
export * from './ui/shadcn/toast';
export * from './ui/shadcn/form';
export * from './ui/shadcn/toggle-group';

// MagicUI Components (selective re-export)
export * from './ui/magicui/animated-list';
export * from './ui/magicui/box-reveal';
export * from './ui/magicui/dock';

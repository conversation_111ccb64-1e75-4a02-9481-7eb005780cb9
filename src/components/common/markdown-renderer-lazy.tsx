import { lazy } from 'react';
import Loadable from '@/components/base/loadable';

/**
 * Lazy-loaded MarkdownRenderer using simple React.lazy pattern
 *
 * This component provides:
 * - Simple, maintainable lazy loading
 * - Consistent loading states
 * - Clean separation of concerns
 */
export const LazyMarkdownRenderer = Loadable(
  lazy(() => import('./markdown-renderer'))
);

// Export as both named and default for compatibility
export { LazyMarkdownRenderer as MarkdownRender };
export default LazyMarkdownRenderer;

import React from 'react';
import { useIntl } from 'react-intl';
import { Link } from 'react-router-dom';
import { Calendar, ArrowRight, Tag } from 'lucide-react';
import { getTranslation } from '@/utils/translation';
import { Translation } from '@/types/shared';

export interface RelatedItem {
  id: string;
  title: Translation;
  description?: Translation;
  image?: string;
  path: string;
  publishedDate?: string;
  category?: {
    name: Translation;
    color?: string;
  };
  readingTime?: number;
}

export interface RelatedItemsProps {
  items: RelatedItem[];
  title?: Translation;
  maxItems?: number;
  layout?: 'grid' | 'list';
  showImages?: boolean;
  showCategory?: boolean;
  showDate?: boolean;
  showReadingTime?: boolean;
  showDescription?: boolean;
}

/**
 * Related Items Component
 *
 * A component for displaying related content suggestions.
 * Features include:
 * - Grid or list layout options
 * - Configurable metadata display
 * - Responsive design
 * - Hover effects and animations
 *
 * @param {RelatedItemsProps} props - The component props
 * @returns {JSX.Element} The Related Items component
 */
const RelatedItems: React.FC<RelatedItemsProps> = ({
  items,
  title = {
    en: 'Related Articles',
    zh: '相关文章',
    'zh-TW': '相關文章',
  },
  maxItems = 3,
  layout = 'grid',
  showImages = true,
  showCategory = true,
  showDate = true,
  showReadingTime = true,
  showDescription = true,
}) => {
  const intl = useIntl();
  const locale = intl.locale;

  // Helper function to get translated text
  const getText = (text: Translation) => {
    return getTranslation(text, locale);
  };

  // Format date for display
  const formatDate = (dateString: string): string => {
    const options: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    };
    return new Date(dateString).toLocaleDateString(locale, options);
  };

  // Limit items to maxItems
  const displayItems = items.slice(0, maxItems);

  if (displayItems.length === 0) {
    return null;
  }

  return (
    <section className="rounded-xl bg-white p-6 shadow-sm md:p-8">
      {/* Section Header */}
      <div className="mb-6 flex items-center justify-between">
        <h2 className="text-2xl font-bold text-gray-900">{getText(title)}</h2>
        <div className="h-0.5 w-12 bg-blue-600"></div>
      </div>

      {/* Items Grid/List */}
      <div
        className={`${
          layout === 'grid'
            ? 'grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3'
            : 'space-y-6'
        }`}
      >
        {displayItems.map(item => (
          <Link
            key={item.id}
            to={item.path}
            className={`group block transition-all duration-300 hover:scale-105 hover:transform ${
              layout === 'list' ? 'flex gap-4' : ''
            }`}
          >
            <article
              className={`overflow-hidden rounded-lg bg-gray-50 transition-shadow duration-300 hover:shadow-md ${
                layout === 'list' ? 'flex-1' : 'h-full'
              }`}
            >
              {/* Image */}
              {showImages && item.image && (
                <div
                  className={`relative overflow-hidden ${
                    layout === 'list' ? 'h-24 w-32 flex-shrink-0' : 'h-48'
                  }`}
                >
                  <img
                    src={item.image}
                    alt={getText(item.title)}
                    className="h-full w-full object-cover transition-transform duration-300 group-hover:scale-110"
                  />
                  {showCategory && item.category && (
                    <div className="absolute left-3 top-3">
                      <span
                        className="inline-flex items-center rounded-full px-2 py-1 text-xs font-medium text-white"
                        style={{
                          backgroundColor: item.category.color || '#6b7280',
                        }}
                      >
                        <Tag className="mr-1 h-3 w-3" />
                        {getText(item.category.name)}
                      </span>
                    </div>
                  )}
                </div>
              )}

              {/* Content */}
              <div className="p-4">
                {/* Category (if no image) */}
                {showCategory && item.category && !showImages && (
                  <div className="mb-2">
                    <span
                      className="inline-flex items-center rounded-full px-2 py-1 text-xs font-medium"
                      style={{
                        backgroundColor: item.category.color
                          ? `${item.category.color}20`
                          : '#f3f4f6',
                        color: item.category.color || '#6b7280',
                      }}
                    >
                      <Tag className="mr-1 h-3 w-3" />
                      {getText(item.category.name)}
                    </span>
                  </div>
                )}

                {/* Title */}
                <h3
                  className={`line-clamp-2 font-semibold text-gray-900 transition-colors group-hover:text-blue-600 ${
                    layout === 'list' ? 'mb-2 text-base' : 'mb-3 text-lg'
                  }`}
                >
                  {getText(item.title)}
                </h3>

                {/* Description */}
                {showDescription && item.description && (
                  <p
                    className={`line-clamp-2 text-gray-600 ${
                      layout === 'list' ? 'mb-2 text-sm' : 'mb-3 text-sm'
                    }`}
                  >
                    {getText(item.description)}
                  </p>
                )}

                {/* Metadata */}
                <div className="flex items-center gap-3 text-xs text-gray-500">
                  {showDate && item.publishedDate && (
                    <div className="flex items-center gap-1">
                      <Calendar className="h-3 w-3" />
                      {formatDate(item.publishedDate)}
                    </div>
                  )}

                  {showReadingTime && item.readingTime && (
                    <div>
                      {intl.formatMessage(
                        {
                          id: 'related.reading.time',
                          defaultMessage: '{time} min read',
                        },
                        { time: item.readingTime }
                      )}
                    </div>
                  )}
                </div>

                {/* Read More Arrow */}
                <div className="mt-3 flex items-center justify-end">
                  <ArrowRight className="h-4 w-4 text-blue-600 transition-transform group-hover:translate-x-1" />
                </div>
              </div>
            </article>
          </Link>
        ))}
      </div>

      {/* View All Link */}
      {items.length > maxItems && (
        <div className="mt-8 text-center">
          <button className="inline-flex items-center gap-2 font-medium text-blue-600 transition-colors hover:text-blue-700">
            {intl.formatMessage({
              id: 'related.view.all',
              defaultMessage: 'View all articles',
            })}
            <ArrowRight className="h-4 w-4" />
          </button>
        </div>
      )}
    </section>
  );
};

export default RelatedItems;

import React, { useState } from 'react';
import { useIntl } from 'react-intl';
import {
  Share2,
  Twitter,
  Facebook,
  Linkedin,
  Link2,
  Mail,
  Check,
} from 'lucide-react';

export interface SocialShareProps {
  title: string;
  description?: string;
  url?: string;
  hashtags?: string[];
  via?: string; // Twitter handle
  showLabel?: boolean;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'minimal' | 'colorful';
}

/**
 * Social Share Component
 *
 * A component for sharing content on social media platforms.
 * Features include:
 * - Multiple social platforms (Twitter, Facebook, LinkedIn, Email)
 * - Copy link functionality
 * - Customizable appearance
 * - Responsive design
 *
 * @param {SocialShareProps} props - The component props
 * @returns {JSX.Element} The Social Share component
 */
const SocialShare: React.FC<SocialShareProps> = ({
  title,
  description,
  url = typeof window !== 'undefined' ? window.location.href : '',
  hashtags = [],
  via,
  showLabel = true,
  size = 'md',
  variant = 'default',
}) => {
  const intl = useIntl();
  const [copied, setCopied] = useState(false);

  // Encode text for URLs
  const encodeText = (text: string) => encodeURIComponent(text);

  // Copy link to clipboard
  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(url);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy link:', err);
    }
  };

  // Generate share URLs
  const shareUrls = {
    twitter: `https://twitter.com/intent/tweet?text=${encodeText(title)}&url=${encodeText(url)}${hashtags.length > 0 ? `&hashtags=${hashtags.join(',')}` : ''}${via ? `&via=${via}` : ''}`,
    facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodeText(url)}`,
    linkedin: `https://www.linkedin.com/sharing/share-offsite/?url=${encodeText(url)}`,
    email: `mailto:?subject=${encodeText(title)}&body=${encodeText(`${description || title}\n\n${url}`)}`,
  };

  // Size classes
  const sizeClasses = {
    sm: 'w-8 h-8 text-sm',
    md: 'w-10 h-10 text-base',
    lg: 'w-12 h-12 text-lg',
  };

  // Icon size classes
  const iconSizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6',
  };

  // Variant classes
  const getVariantClasses = (platform: string) => {
    if (variant === 'colorful') {
      const colorClasses = {
        twitter: 'bg-blue-500 hover:bg-blue-600 text-white',
        facebook: 'bg-blue-600 hover:bg-blue-700 text-white',
        linkedin: 'bg-blue-700 hover:bg-blue-800 text-white',
        email: 'bg-gray-600 hover:bg-gray-700 text-white',
        copy: 'bg-green-600 hover:bg-green-700 text-white',
      };
      return (
        colorClasses[platform as keyof typeof colorClasses] ||
        'bg-gray-600 hover:bg-gray-700 text-white'
      );
    }

    if (variant === 'minimal') {
      return 'bg-transparent hover:bg-gray-100 text-gray-600 hover:text-gray-900 border border-gray-300';
    }

    return 'bg-gray-100 hover:bg-gray-200 text-gray-700 hover:text-gray-900';
  };

  // Share button component
  const ShareButton: React.FC<{
    href?: string;
    onClick?: () => void;
    icon: React.ReactNode;
    label: string;
    platform: string;
  }> = ({ href, onClick, icon, label, platform }) => {
    const buttonProps = href
      ? {
          as: 'a' as const,
          href,
          target: '_blank',
          rel: 'noopener noreferrer',
        }
      : {
          as: 'button' as const,
          onClick,
        };

    const Component = buttonProps.as;

    return (
      <Component
        {...(buttonProps.as === 'a'
          ? {
              href: buttonProps.href,
              target: buttonProps.target,
              rel: buttonProps.rel,
            }
          : { onClick: buttonProps.onClick })}
        className={`inline-flex items-center justify-center rounded-lg transition-all duration-200 ${sizeClasses[size]} ${getVariantClasses(platform)} focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2`}
        title={label}
        aria-label={label}
      >
        {icon}
      </Component>
    );
  };

  return (
    <div className="flex flex-col gap-4">
      {showLabel && (
        <div className="flex items-center gap-2">
          <Share2 className="h-5 w-5 text-gray-600" />
          <span className="text-sm font-medium text-gray-700">
            {intl.formatMessage({
              id: 'social.share.title',
              defaultMessage: 'Share this article',
            })}
          </span>
        </div>
      )}

      <div className="flex flex-wrap items-center gap-3">
        {/* Twitter */}
        <ShareButton
          href={shareUrls.twitter}
          icon={<Twitter className={iconSizeClasses[size]} />}
          label={intl.formatMessage({
            id: 'social.share.twitter',
            defaultMessage: 'Share on Twitter',
          })}
          platform="twitter"
        />

        {/* Facebook */}
        <ShareButton
          href={shareUrls.facebook}
          icon={<Facebook className={iconSizeClasses[size]} />}
          label={intl.formatMessage({
            id: 'social.share.facebook',
            defaultMessage: 'Share on Facebook',
          })}
          platform="facebook"
        />

        {/* LinkedIn */}
        <ShareButton
          href={shareUrls.linkedin}
          icon={<Linkedin className={iconSizeClasses[size]} />}
          label={intl.formatMessage({
            id: 'social.share.linkedin',
            defaultMessage: 'Share on LinkedIn',
          })}
          platform="linkedin"
        />

        {/* Email */}
        <ShareButton
          href={shareUrls.email}
          icon={<Mail className={iconSizeClasses[size]} />}
          label={intl.formatMessage({
            id: 'social.share.email',
            defaultMessage: 'Share via Email',
          })}
          platform="email"
        />

        {/* Copy Link */}
        <ShareButton
          onClick={copyToClipboard}
          icon={
            copied ? (
              <Check className={iconSizeClasses[size]} />
            ) : (
              <Link2 className={iconSizeClasses[size]} />
            )
          }
          label={
            copied
              ? intl.formatMessage({
                  id: 'social.share.copied',
                  defaultMessage: 'Link copied!',
                })
              : intl.formatMessage({
                  id: 'social.share.copy',
                  defaultMessage: 'Copy link',
                })
          }
          platform="copy"
        />
      </div>

      {copied && (
        <div className="animate-fade-in text-sm font-medium text-green-600">
          {intl.formatMessage({
            id: 'social.share.success',
            defaultMessage: 'Link copied to clipboard!',
          })}
        </div>
      )}
    </div>
  );
};

export default SocialShare;

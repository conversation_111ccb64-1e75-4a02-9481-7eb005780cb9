'use client';

import * as React from 'react';
import { useState, useRef, useEffect } from 'react';
import { motion, HTMLMotionProps } from 'framer-motion';
import { cn } from '@/lib/utils';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/shadcn/tooltip';

/**
 * DockItem Component Props
 */
interface DockItemProps extends Omit<HTMLMotionProps<'div'>, 'animate'> {
  /**
   * The current active magnification level
   */
  active?: number;

  /**
   * Children elements
   */
  children: React.ReactNode;

  /**
   * Optional width for the item
   */
  width?: number;

  /**
   * Whether the item is disabled
   */
  disabled?: boolean;

  /**
   * Optional class name for styling
   */
  className?: string;

  /**
   * Optional tooltip text to display on hover
   */
  tooltip?: string;
}

/**
 * DockItem Component
 *
 * Represents a single item in the Dock with magnification capabilities
 */
export const DockItem = React.forwardRef<HTMLDivElement, DockItemProps>(
  (
    {
      active = 0,
      width = 40,
      className,
      children,
      disabled,
      tooltip,
      ...props
    },
    ref
  ) => {
    const scale = 1 + active * 0.5; // Scale based on active level

    const itemContent = (
      <motion.div
        ref={ref}
        className={cn(
          'flex items-center justify-center',
          disabled && 'cursor-not-allowed opacity-50',
          className
        )}
        style={{
          width: width,
          height: width,
        }}
        animate={{ scale }}
        transition={{ type: 'spring', stiffness: 300, damping: 20 }}
        whileTap={disabled ? undefined : { scale: 0.95 }}
        {...props}
      >
        {children}
      </motion.div>
    );

    // If tooltip is provided and accessible, wrap item in tooltip component
    if (tooltip && !disabled) {
      return (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>{itemContent}</TooltipTrigger>
            <TooltipContent>
              <p>{tooltip}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      );
    }

    // Otherwise return the item directly
    return itemContent;
  }
);
DockItem.displayName = 'DockItem';

/**
 * DockDivider Component Props
 */
interface DockDividerProps extends React.HTMLAttributes<HTMLDivElement> {
  /**
   * Optional class name for styling
   */
  className?: string;
}

/**
 * DockDivider Component
 *
 * A divider for separation between dock items
 */
export const DockDivider = React.forwardRef<HTMLDivElement, DockDividerProps>(
  ({ className, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn('mx-1 h-8 w-px bg-gray-200 dark:bg-gray-700', className)}
        {...props}
      />
    );
  }
);
DockDivider.displayName = 'DockDivider';

/**
 * Dock Component Props
 */
interface DockProps extends React.HTMLAttributes<HTMLDivElement> {
  /**
   * Maximum magnification scale
   * @default 1
   */
  magnification?: number;

  /**
   * Children elements
   */
  children: React.ReactNode;

  /**
   * Direction of the dock
   * @default "horizontal"
   */
  direction?: 'horizontal' | 'vertical';

  /**
   * Optional class name for styling
   */
  className?: string;
}

/**
 * Dock Component
 *
 * A macOS-inspired dock for navigation or actions with magnification effect
 */
export const Dock = React.forwardRef<HTMLDivElement, DockProps>(
  ({
    className,
    direction = 'horizontal',
    magnification = 1,
    children,
    ...props
  }) => {
    const [activeIndex, setActiveIndex] = useState<number | null>(null);
    const containerRef = useRef<HTMLDivElement>(null);

    const getMagnification = (index: number): number => {
      if (activeIndex === null) return 0;

      // Distance calculation
      const distance = Math.abs(index - activeIndex);
      if (distance > 1) return 0;

      // Magnification decreases with distance from active item
      return magnification * (1 - distance * 0.5);
    };

    // Add mouse leave handler to reset active index
    useEffect(() => {
      const container = containerRef.current;
      if (!container) return;

      const handleMouseLeave = () => setActiveIndex(null);
      container.addEventListener('mouseleave', handleMouseLeave);

      return () => {
        container.removeEventListener('mouseleave', handleMouseLeave);
      };
    }, []);

    // Clone children with appropriate magnification
    const items = React.Children.map(children, (child, index) => {
      if (!React.isValidElement(child)) return child;

      if (child.type === DockDivider) return child;

      return React.cloneElement(child as React.ReactElement<DockItemProps>, {
        active: getMagnification(index),
        onMouseEnter: () => setActiveIndex(index),
      });
    });

    return (
      <div
        ref={containerRef}
        className={cn(
          'flex rounded-full bg-white/20 p-1.5 shadow-lg backdrop-blur-md dark:bg-black/20',
          direction === 'vertical' ? 'flex-col' : 'flex-row',
          className
        )}
        {...props}
      >
        {items}
      </div>
    );
  }
);
Dock.displayName = 'Dock';

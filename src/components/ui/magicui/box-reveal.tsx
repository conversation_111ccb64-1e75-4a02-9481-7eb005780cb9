'use client';

import React, { useEffect, useRef, forwardRef } from 'react';
import { motion, useAnimation, useInView } from 'motion/react';

interface BoxRevealProps {
  children: React.JSX.Element;
  width?: 'fit-content' | '100%';
  boxColor?: string;
  duration?: number;
}

/**
 * BoxReveal Component
 *
 * This component creates a box reveal animation effect for its children.
 * It uses motion for animations and shows a sliding colored box that reveals content underneath.
 *
 * @param children - The content to be revealed
 * @param width - Width of the container, either "fit-content" or "100%"
 * @param boxColor - Color of the reveal box
 * @param duration - Duration of the animation in seconds
 */
export const BoxReveal = forwardRef<HTMLDivElement, BoxRevealProps>(
  (
    { children, width = 'fit-content', boxColor = '#5046e6', duration },
    ref
  ) => {
    const mainControls = useAnimation();
    const slideControls = useAnimation();

    const localRef = useRef(null);
    const internalRef = ref || localRef;
    const isInView = useInView(localRef, { once: true });

    useEffect(() => {
      if (isInView) {
        slideControls.start('visible');
        mainControls.start('visible');
      } else {
        slideControls.start('hidden');
        mainControls.start('hidden');
      }
    }, [isInView, mainControls, slideControls]);

    return (
      <div
        ref={internalRef}
        style={{ position: 'relative', width, overflow: 'hidden' }}
      >
        <motion.div
          variants={{
            hidden: { opacity: 0, y: 75 },
            visible: { opacity: 1, y: 0 },
          }}
          initial="hidden"
          animate={mainControls}
          transition={{ duration: duration ? duration : 0.5, delay: 0.25 }}
        >
          {children}
        </motion.div>

        <motion.div
          variants={{
            hidden: { left: 0 },
            visible: { left: '100%' },
          }}
          initial="hidden"
          animate={slideControls}
          transition={{ duration: duration ? duration : 0.5, ease: 'easeIn' }}
          style={{
            position: 'absolute',
            top: 4,
            bottom: 4,
            left: 0,
            right: 0,
            zIndex: 20,
            background: boxColor,
          }}
        />
      </div>
    );
  }
);

BoxReveal.displayName = 'BoxReveal';

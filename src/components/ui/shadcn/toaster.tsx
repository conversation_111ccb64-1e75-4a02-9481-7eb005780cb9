import { useToast } from '@/hooks/use-toast';
import {
  Toast,
  ToastClose,
  ToastDescription,
  ToastProvider,
  ToastTitle,
  ToastViewport,
} from '@/components/ui/shadcn/toast';
import { AnimatedList } from '@/components/ui/shadcn/animated-list';

export function Toaster() {
  const { toasts } = useToast();

  return (
    <ToastProvider>
      <AnimatedList
        className="fixed right-8 top-2 z-[100] flex w-[350px] flex-col-reverse gap-4"
        delay={100}
      >
        {toasts.map(function ({ id, title, description, action, ...props }) {
          return (
            <Toast key={id} {...props}>
              <div className="grid gap-1">
                {title && <ToastTitle>{title}</ToastTitle>}
                {description && (
                  <ToastDescription>{description}</ToastDescription>
                )}
              </div>
              {action}
              <ToastClose />
            </Toast>
          );
        })}
      </AnimatedList>
      <ToastViewport className="fixed right-8 top-0 z-[100] flex w-[350px] flex-col-reverse gap-4" />
    </ToastProvider>
  );
}

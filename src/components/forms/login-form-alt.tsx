import React, { useState } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { useIntl } from 'react-intl';
import { Input } from '@/components/ui/shadcn/input';
import { PasswordInput } from '@/components/features/login/password-input';
import { Label } from '@/components/ui/shadcn/label';
import { Alert, AlertTitle } from '@/components/ui/shadcn/alert';
import { Button } from '@/components/ui/shadcn/button';
import useAuth from '@/hooks/use-auth';
import { DASHBOARD_PATH } from '@/config';

export function LoginFormAlt() {
  const navigate = useNavigate();
  const intl = useIntl();
  const { login } = useAuth();
  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('123456');
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setIsLoading(true);

    try {
      await login(email, password);
      navigate(DASHBOARD_PATH);
    } catch {
      setError(intl.formatMessage({ id: 'page.login.error' }));
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="grid gap-4">
      {error && (
        <Alert variant="destructive">
          <AlertTitle>{error}</AlertTitle>
        </Alert>
      )}
      <div className="grid gap-2">
        <Label htmlFor="email">
          {intl.formatMessage({ id: 'page.login.email' })}
        </Label>
        <Input
          id="email"
          type="email"
          placeholder="<EMAIL>"
          value={email}
          onChange={e => setEmail(e.target.value)}
          required
        />
      </div>
      <div className="grid gap-2">
        <div className="flex items-center justify-between">
          <Label htmlFor="password">
            {intl.formatMessage({ id: 'page.login.password' })}
          </Label>
          <Link
            to="/forgot"
            className="text-sm font-medium text-primary hover:underline"
          >
            {intl.formatMessage({ id: 'page.login.forgotPassword' })}
          </Link>
        </div>
        <PasswordInput
          id="password"
          value={password}
          onChange={e => setPassword(e.target.value)}
          required
        />
      </div>
      <Button type="submit" className="w-full" disabled={isLoading}>
        {isLoading
          ? `${intl.formatMessage({ id: 'page.login.submit' })}...`
          : intl.formatMessage({ id: 'page.login.submit' })}
      </Button>
    </form>
  );
}

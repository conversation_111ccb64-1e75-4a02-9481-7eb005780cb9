import React, { useState } from 'react';
import { useIntl } from 'react-intl';
import { Button } from '@/components/ui/shadcn/button';
import { Input } from '@/components/ui/shadcn/input';
import { Label } from '@/components/ui/shadcn/label';
import { Textarea } from '@/components/ui/shadcn/textarea';
import {
  Alert,
  AlertTitle,
  AlertDescription,
} from '@/components/ui/shadcn/alert';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/shadcn/card';
import { JobApplicationForm as JobApplicationFormType } from '@/types/public/careers';
import '@/styles/animations.css';

interface JobApplicationFormProps {
  jobId: string;
  jobTitle: string;
  onSubmit: (_formData: JobApplicationFormType) => Promise<void>;
  onCancel?: () => void;
}

/**
 * Job Application Form Component
 *
 * Provides a comprehensive form for job applications with validation
 * and file upload capabilities.
 */
export function JobApplicationForm({
  jobId: _,
  jobTitle: __,
  onSubmit,
  onCancel,
}: JobApplicationFormProps) {
  const intl = useIntl();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);

  const [formData, setFormData] = useState<JobApplicationFormType>({
    personalInfo: {
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      location: '',
    },
    documents: {
      resume: null,
      coverLetter: null,
      portfolio: '',
    },
    experience: {
      yearsExperience: 0,
      currentRole: '',
      expectedSalary: '',
    },
    additionalInfo: '',
  });

  const [validationErrors, setValidationErrors] = useState<
    Record<string, string>
  >({});

  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    // Personal info validation
    if (!formData.personalInfo.firstName.trim()) {
      errors.firstName = intl.formatMessage({ id: 'form.validation.required' });
    }
    if (!formData.personalInfo.lastName.trim()) {
      errors.lastName = intl.formatMessage({ id: 'form.validation.required' });
    }
    if (!formData.personalInfo.email.trim()) {
      errors.email = intl.formatMessage({ id: 'form.validation.required' });
    } else if (
      !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.personalInfo.email)
    ) {
      errors.email = intl.formatMessage({
        id: 'form.validation.email.invalid',
      });
    }
    if (!formData.personalInfo.phone.trim()) {
      errors.phone = intl.formatMessage({ id: 'form.validation.required' });
    }
    if (!formData.personalInfo.location.trim()) {
      errors.location = intl.formatMessage({ id: 'form.validation.required' });
    }

    // Documents validation
    if (!formData.documents.resume) {
      errors.resume = intl.formatMessage({ id: 'form.validation.required' });
    }

    // Experience validation
    if (formData.experience.yearsExperience < 0) {
      errors.yearsExperience = intl.formatMessage({
        id: 'form.validation.number.positive',
      });
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleInputChange = (field: string, value: string | number) => {
    const keys = field.split('.');
    setFormData(prev => {
      const newData = { ...prev };
      let current: any = newData;

      for (let i = 0; i < keys.length - 1; i++) {
        current = current[keys[i]];
      }

      current[keys[keys.length - 1]] = value;
      return newData;
    });

    // Clear validation error when user starts typing
    if (validationErrors[field]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  const handleFileChange = (
    field: 'resume' | 'coverLetter',
    file: File | null
  ) => {
    setFormData(prev => ({
      ...prev,
      documents: {
        ...prev.documents,
        [field]: file,
      },
    }));

    // Clear validation error
    if (validationErrors[field]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    if (!validateForm()) {
      return;
    }

    setIsLoading(true);

    try {
      await onSubmit(formData);
      setSuccess(true);
    } catch {
      setError(intl.formatMessage({ id: 'form.error.submission' }));
    } finally {
      setIsLoading(false);
    }
  };

  if (success) {
    return (
      <Card className="animate-fadeIn mx-auto max-w-2xl">
        <CardContent className="p-8 text-center">
          <div className="mb-4 text-green-600">
            <svg
              className="mx-auto h-16 w-16"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M5 13l4 4L19 7"
              />
            </svg>
          </div>
          <h2 className="mb-4 text-2xl font-bold text-green-600">
            {intl.formatMessage({
              id: 'page.careers.application.success.title',
            })}
          </h2>
          <p className="mb-6 text-muted-foreground">
            {intl.formatMessage({
              id: 'page.careers.application.success.message',
            })}
          </p>
          <Button onClick={() => window.history.back()} variant="outline">
            {intl.formatMessage({ id: 'common.back' })}
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <form
      onSubmit={handleSubmit}
      className="animate-fadeIn mx-auto max-w-4xl space-y-8"
    >
      {error && (
        <Alert variant="destructive" className="animate-fadeIn">
          <AlertTitle>{intl.formatMessage({ id: 'common.error' })}</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Personal Information */}
      <Card className="animate-fadeIn">
        <CardHeader>
          <CardTitle>
            {intl.formatMessage({
              id: 'page.careers.application.personal.title',
            })}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <div>
              <Label htmlFor="firstName">
                {intl.formatMessage({
                  id: 'page.careers.application.firstName',
                })}{' '}
                *
              </Label>
              <Input
                id="firstName"
                value={formData.personalInfo.firstName}
                onChange={e =>
                  handleInputChange('personalInfo.firstName', e.target.value)
                }
                className={validationErrors.firstName ? 'border-red-500' : ''}
              />
              {validationErrors.firstName && (
                <p className="mt-1 text-sm text-red-500">
                  {validationErrors.firstName}
                </p>
              )}
            </div>
            <div>
              <Label htmlFor="lastName">
                {intl.formatMessage({
                  id: 'page.careers.application.lastName',
                })}{' '}
                *
              </Label>
              <Input
                id="lastName"
                value={formData.personalInfo.lastName}
                onChange={e =>
                  handleInputChange('personalInfo.lastName', e.target.value)
                }
                className={validationErrors.lastName ? 'border-red-500' : ''}
              />
              {validationErrors.lastName && (
                <p className="mt-1 text-sm text-red-500">
                  {validationErrors.lastName}
                </p>
              )}
            </div>
          </div>

          <div className="grid gap-4 md:grid-cols-2">
            <div>
              <Label htmlFor="email">
                {intl.formatMessage({ id: 'page.careers.application.email' })} *
              </Label>
              <Input
                id="email"
                type="email"
                value={formData.personalInfo.email}
                onChange={e =>
                  handleInputChange('personalInfo.email', e.target.value)
                }
                className={validationErrors.email ? 'border-red-500' : ''}
              />
              {validationErrors.email && (
                <p className="mt-1 text-sm text-red-500">
                  {validationErrors.email}
                </p>
              )}
            </div>
            <div>
              <Label htmlFor="phone">
                {intl.formatMessage({ id: 'page.careers.application.phone' })} *
              </Label>
              <Input
                id="phone"
                type="tel"
                value={formData.personalInfo.phone}
                onChange={e =>
                  handleInputChange('personalInfo.phone', e.target.value)
                }
                className={validationErrors.phone ? 'border-red-500' : ''}
              />
              {validationErrors.phone && (
                <p className="mt-1 text-sm text-red-500">
                  {validationErrors.phone}
                </p>
              )}
            </div>
          </div>

          <div>
            <Label htmlFor="location">
              {intl.formatMessage({ id: 'page.careers.application.location' })}{' '}
              *
            </Label>
            <Input
              id="location"
              value={formData.personalInfo.location}
              onChange={e =>
                handleInputChange('personalInfo.location', e.target.value)
              }
              placeholder={intl.formatMessage({
                id: 'page.careers.application.location.placeholder',
              })}
              className={validationErrors.location ? 'border-red-500' : ''}
            />
            {validationErrors.location && (
              <p className="mt-1 text-sm text-red-500">
                {validationErrors.location}
              </p>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Documents */}
      <Card className="animate-fadeIn animation-delay-200">
        <CardHeader>
          <CardTitle>
            {intl.formatMessage({
              id: 'page.careers.application.documents.title',
            })}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="resume">
              {intl.formatMessage({ id: 'page.careers.application.resume' })} *
            </Label>
            <Input
              id="resume"
              type="file"
              accept=".pdf,.doc,.docx"
              onChange={e =>
                handleFileChange('resume', e.target.files?.[0] || null)
              }
              className={validationErrors.resume ? 'border-red-500' : ''}
            />
            <p className="mt-1 text-sm text-muted-foreground">
              {intl.formatMessage({
                id: 'page.careers.application.resume.help',
              })}
            </p>
            {validationErrors.resume && (
              <p className="mt-1 text-sm text-red-500">
                {validationErrors.resume}
              </p>
            )}
          </div>

          <div>
            <Label htmlFor="coverLetter">
              {intl.formatMessage({
                id: 'page.careers.application.coverLetter',
              })}
            </Label>
            <Input
              id="coverLetter"
              type="file"
              accept=".pdf,.doc,.docx"
              onChange={e =>
                handleFileChange('coverLetter', e.target.files?.[0] || null)
              }
            />
            <p className="mt-1 text-sm text-muted-foreground">
              {intl.formatMessage({
                id: 'page.careers.application.coverLetter.help',
              })}
            </p>
          </div>

          <div>
            <Label htmlFor="portfolio">
              {intl.formatMessage({ id: 'page.careers.application.portfolio' })}
            </Label>
            <Input
              id="portfolio"
              type="url"
              value={formData.documents.portfolio || ''}
              onChange={e =>
                handleInputChange('documents.portfolio', e.target.value)
              }
              placeholder="https://..."
            />
            <p className="mt-1 text-sm text-muted-foreground">
              {intl.formatMessage({
                id: 'page.careers.application.portfolio.help',
              })}
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Experience */}
      <Card className="animate-fadeIn animation-delay-300">
        <CardHeader>
          <CardTitle>
            {intl.formatMessage({
              id: 'page.careers.application.experience.title',
            })}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <div>
              <Label htmlFor="yearsExperience">
                {intl.formatMessage({
                  id: 'page.careers.application.yearsExperience',
                })}
              </Label>
              <Input
                id="yearsExperience"
                type="number"
                min="0"
                value={formData.experience.yearsExperience}
                onChange={e =>
                  handleInputChange(
                    'experience.yearsExperience',
                    parseInt(e.target.value) || 0
                  )
                }
                className={
                  validationErrors.yearsExperience ? 'border-red-500' : ''
                }
              />
              {validationErrors.yearsExperience && (
                <p className="mt-1 text-sm text-red-500">
                  {validationErrors.yearsExperience}
                </p>
              )}
            </div>
            <div>
              <Label htmlFor="expectedSalary">
                {intl.formatMessage({
                  id: 'page.careers.application.expectedSalary',
                })}
              </Label>
              <Input
                id="expectedSalary"
                value={formData.experience.expectedSalary || ''}
                onChange={e =>
                  handleInputChange('experience.expectedSalary', e.target.value)
                }
                placeholder={intl.formatMessage({
                  id: 'page.careers.application.expectedSalary.placeholder',
                })}
              />
            </div>
          </div>

          <div>
            <Label htmlFor="currentRole">
              {intl.formatMessage({
                id: 'page.careers.application.currentRole',
              })}
            </Label>
            <Input
              id="currentRole"
              value={formData.experience.currentRole || ''}
              onChange={e =>
                handleInputChange('experience.currentRole', e.target.value)
              }
              placeholder={intl.formatMessage({
                id: 'page.careers.application.currentRole.placeholder',
              })}
            />
          </div>
        </CardContent>
      </Card>

      {/* Additional Information */}
      <Card className="animate-fadeIn animation-delay-400">
        <CardHeader>
          <CardTitle>
            {intl.formatMessage({
              id: 'page.careers.application.additional.title',
            })}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div>
            <Label htmlFor="additionalInfo">
              {intl.formatMessage({
                id: 'page.careers.application.additionalInfo',
              })}
            </Label>
            <Textarea
              id="additionalInfo"
              rows={4}
              value={formData.additionalInfo || ''}
              onChange={e =>
                handleInputChange('additionalInfo', e.target.value)
              }
              placeholder={intl.formatMessage({
                id: 'page.careers.application.additionalInfo.placeholder',
              })}
            />
          </div>
        </CardContent>
      </Card>

      {/* Form Actions */}
      <div className="animate-fadeIn animation-delay-500 flex justify-end gap-4">
        {onCancel && (
          <Button type="button" variant="outline" onClick={onCancel}>
            {intl.formatMessage({ id: 'common.cancel' })}
          </Button>
        )}
        <Button type="submit" disabled={isLoading} className="shimmer-effect">
          {isLoading
            ? intl.formatMessage({ id: 'common.submitting' })
            : intl.formatMessage({ id: 'page.careers.application.submit' })}
        </Button>
      </div>
    </form>
  );
}

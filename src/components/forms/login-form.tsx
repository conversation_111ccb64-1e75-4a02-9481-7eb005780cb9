import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useIntl } from 'react-intl';
import { ArrowLeft } from 'lucide-react';

import { Button } from '@/components/ui/shadcn/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/shadcn/card';
import { Input } from '@/components/ui/shadcn/input';
import { PasswordInput } from '@/components/features/login/password-input';
import { Label } from '@/components/ui/shadcn/label';
import { Alert, AlertTitle } from '@/components/ui/shadcn/alert';
import useAuth from '@/hooks/use-auth';
import { DASHBOARD_PATH } from '@/config';

interface LoginFormProps {
  showBackButton?: boolean;
}

/**
 * Login Form Component
 *
 * Provides authenticated login functionality with fast, responsive animations
 * for enhanced user experience.
 *
 * @param {LoginFormProps} props - Component props
 * @returns {JSX.Element} The Login Form component
 */
export function LoginForm({ showBackButton = true }: LoginFormProps) {
  const navigate = useNavigate();
  const intl = useIntl();
  const { login } = useAuth();
  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('123456');
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setIsLoading(true);

    try {
      await login(email, password);
      navigate(DASHBOARD_PATH);
    } catch {
      setError(intl.formatMessage({ id: 'page.login.error' }));
    } finally {
      setIsLoading(false);
    }
  };

  const handleBack = () => {
    navigate(-1); // Navigate to the previous page in history
  };

  return (
    <Card className="animate-fadeIn relative mx-auto max-w-sm">
      {showBackButton && (
        <Button
          variant="ghost"
          size="icon"
          className="animate-fadeIn absolute left-2 top-2"
          onClick={handleBack}
          aria-label={intl.formatMessage({ id: 'back' })}
        >
          <ArrowLeft className="h-4 w-4" />
        </Button>
      )}
      <CardHeader className="pt-10">
        <CardTitle className="animate-slideUp text-2xl">
          {intl.formatMessage({ id: 'page.login.title' })}
        </CardTitle>
        <CardDescription className="animate-slideUp animation-delay-100">
          {intl.formatMessage({ id: 'page.login.description' })}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="grid gap-4">
          {error && (
            <Alert variant="destructive" className="animate-fadeIn">
              <AlertTitle>{error}</AlertTitle>
            </Alert>
          )}
          <div className="animate-fadeIn animation-delay-100 grid gap-2">
            <Label htmlFor="email">
              {intl.formatMessage({ id: 'page.login.email' })}
            </Label>
            <Input
              id="email"
              type="email"
              placeholder="<EMAIL>"
              value={email}
              onChange={e => setEmail(e.target.value)}
              required
            />
          </div>
          <div className="animate-fadeIn animation-delay-200 grid gap-2">
            <div className="flex items-center">
              <Label htmlFor="password">
                {intl.formatMessage({ id: 'page.login.password' })}
              </Label>
              <Link
                to="/forgot"
                className="shimmer-effect ml-auto inline-block text-sm underline"
              >
                {intl.formatMessage({ id: 'page.login.forgotPassword' })}
              </Link>
            </div>
            <PasswordInput
              id="password"
              value={password}
              onChange={e => setPassword(e.target.value)}
              required
            />
          </div>
          <Button
            type="submit"
            className="shimmer-effect animate-fadeIn animation-delay-200 w-full"
            disabled={isLoading}
          >
            {isLoading
              ? `${intl.formatMessage({ id: 'page.login.submit' })}...`
              : intl.formatMessage({ id: 'page.login.submit' })}
          </Button>
          <Button
            variant="outline"
            className="shimmer-effect animate-fadeIn animation-delay-300 w-full"
          >
            <svg className="mr-2 h-4 w-4" viewBox="0 0 24 24">
              <path
                d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                fill="#4285F4"
              />
              <path
                d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                fill="#34A853"
              />
              <path
                d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                fill="#FBBC05"
              />
              <path
                d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                fill="#EA4335"
              />
            </svg>
            {intl.formatMessage({ id: 'page.login.googleSignIn' })}
          </Button>
          <div className="animate-fadeIn animation-delay-300 mt-4 text-center text-sm">
            {intl.formatMessage({ id: 'page.login.noAccount' })}{' '}
            <Link to="/register" className="shimmer-effect underline">
              {intl.formatMessage({ id: 'page.login.signUp' })}
            </Link>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}

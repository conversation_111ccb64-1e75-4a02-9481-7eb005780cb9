import React, { useState, useCallback } from 'react';
import { useIntl } from 'react-intl';
import { ChevronLeft, ChevronRight, Check } from 'lucide-react';
import { Button } from '@/components/ui/shadcn/button';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/shadcn/card';
import { Progress } from '@/components/ui/shadcn/progress';

export interface FormStep {
  id: string;
  title: string;
  description?: string;
  component: React.ComponentType<FormStepProps>;
  isValid?: boolean;
  isOptional?: boolean;
}

export interface FormStepProps {
  data: Record<string, any>;
  onDataChange: (_data: Record<string, any>) => void;
  onValidationChange: (_isValid: boolean) => void;
  isActive: boolean;
}

export interface FormWizardProps {
  steps: FormStep[];
  initialData?: Record<string, any>;
  onComplete: (_data: Record<string, any>) => Promise<void>;
  onCancel?: () => void;
  title?: string;
  description?: string;
  showProgress?: boolean;
  allowSkipOptional?: boolean;
  isSubmitting?: boolean;
}

/**
 * Form Wizard Component
 *
 * A multi-step form wizard with navigation, validation, and progress tracking.
 * Features include:
 * - Step-by-step navigation
 * - Progress indicator
 * - Form validation
 * - Optional steps
 * - Data persistence across steps
 *
 * @param {FormWizardProps} props - The component props
 * @returns {JSX.Element} The Form Wizard component
 */
const FormWizard: React.FC<FormWizardProps> = ({
  steps,
  initialData = {},
  onComplete,
  onCancel,
  title,
  description,
  showProgress = true,
  allowSkipOptional = true,
  isSubmitting = false,
}) => {
  const intl = useIntl();
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const [formData, setFormData] = useState<Record<string, any>>(initialData);
  const [stepValidation, setStepValidation] = useState<Record<string, boolean>>(
    {}
  );
  const [completedSteps, setCompletedSteps] = useState<Set<string>>(new Set());

  const currentStep = steps[currentStepIndex];
  const isFirstStep = currentStepIndex === 0;
  const isLastStep = currentStepIndex === steps.length - 1;
  const progress = ((currentStepIndex + 1) / steps.length) * 100;

  // Handle data changes from step components
  const handleDataChange = useCallback((stepData: Record<string, any>) => {
    setFormData(prev => ({ ...prev, ...stepData }));
  }, []);

  // Handle validation changes from step components
  const handleValidationChange = useCallback(
    (isValid: boolean) => {
      setStepValidation(prev => ({
        ...prev,
        [currentStep.id]: isValid,
      }));
    },
    [currentStep.id]
  );

  // Check if current step is valid
  const isCurrentStepValid = () => {
    const stepIsValid = stepValidation[currentStep.id];
    const stepIsOptional = currentStep.isOptional && allowSkipOptional;
    return stepIsValid || stepIsOptional;
  };

  // Navigate to next step
  const handleNext = () => {
    if (isCurrentStepValid()) {
      setCompletedSteps(prev => new Set([...prev, currentStep.id]));
      if (isLastStep) {
        handleSubmit();
      } else {
        setCurrentStepIndex(prev => prev + 1);
      }
    }
  };

  // Navigate to previous step
  const handlePrevious = () => {
    if (!isFirstStep) {
      setCurrentStepIndex(prev => prev - 1);
    }
  };

  // Navigate to specific step
  const handleStepClick = (stepIndex: number) => {
    // Only allow navigation to completed steps or the next step
    const targetStep = steps[stepIndex];
    const canNavigate =
      completedSteps.has(targetStep.id) || stepIndex <= currentStepIndex + 1;

    if (canNavigate) {
      setCurrentStepIndex(stepIndex);
    }
  };

  // Submit the form
  const handleSubmit = async () => {
    try {
      await onComplete(formData);
    } catch (error) {
      console.error('Form submission error:', error);
    }
  };

  // Render step indicator
  const renderStepIndicator = () => {
    return (
      <div className="mb-8 flex items-center justify-between">
        {steps.map((step, index) => {
          const isCompleted = completedSteps.has(step.id);
          const isCurrent = index === currentStepIndex;
          const isClickable =
            completedSteps.has(step.id) || index <= currentStepIndex + 1;

          return (
            <div key={step.id} className="flex items-center">
              <button
                onClick={() => handleStepClick(index)}
                disabled={!isClickable}
                className={`flex h-10 w-10 items-center justify-center rounded-full border-2 transition-all duration-200 ${
                  isCompleted
                    ? 'border-green-500 bg-green-500 text-white'
                    : isCurrent
                      ? 'border-blue-500 bg-blue-500 text-white'
                      : isClickable
                        ? 'border-gray-300 text-gray-500 hover:border-blue-500 hover:text-blue-500'
                        : 'cursor-not-allowed border-gray-200 text-gray-300'
                }`}
              >
                {isCompleted ? (
                  <Check className="h-5 w-5" />
                ) : (
                  <span className="text-sm font-medium">{index + 1}</span>
                )}
              </button>

              {index < steps.length - 1 && (
                <div
                  className={`mx-2 h-0.5 w-12 ${
                    isCompleted ? 'bg-green-500' : 'bg-gray-200'
                  }`}
                />
              )}
            </div>
          );
        })}
      </div>
    );
  };

  // Render current step component
  const renderCurrentStep = () => {
    const StepComponent = currentStep.component;

    return (
      <StepComponent
        data={formData}
        onDataChange={handleDataChange}
        onValidationChange={handleValidationChange}
        isActive={true}
      />
    );
  };

  return (
    <div className="mx-auto max-w-4xl">
      <Card>
        <CardHeader>
          {title && (
            <CardTitle className="text-center text-2xl font-bold">
              {title}
            </CardTitle>
          )}
          {description && (
            <p className="mt-2 text-center text-gray-600">{description}</p>
          )}

          {showProgress && (
            <div className="mt-6">
              <div className="mb-2 flex justify-between text-sm text-gray-600">
                <span>
                  {intl.formatMessage(
                    {
                      id: 'wizard.step.progress',
                      defaultMessage: 'Step {current} of {total}',
                    },
                    { current: currentStepIndex + 1, total: steps.length }
                  )}
                </span>
                <span>{Math.round(progress)}%</span>
              </div>
              <Progress value={progress} className="h-2" />
            </div>
          )}
        </CardHeader>

        <CardContent>
          {/* Step Indicator */}
          {renderStepIndicator()}

          {/* Current Step Header */}
          <div className="mb-6">
            <h3 className="mb-2 text-xl font-semibold text-gray-900">
              {currentStep.title}
            </h3>
            {currentStep.description && (
              <p className="text-gray-600">{currentStep.description}</p>
            )}
            {currentStep.isOptional && (
              <span className="mt-2 inline-block rounded bg-gray-100 px-2 py-1 text-xs text-gray-600">
                {intl.formatMessage({
                  id: 'wizard.optional',
                  defaultMessage: 'Optional',
                })}
              </span>
            )}
          </div>

          {/* Current Step Content */}
          <div className="mb-8">{renderCurrentStep()}</div>

          {/* Navigation Buttons */}
          <div className="flex items-center justify-between border-t border-gray-200 pt-6">
            <div>
              {!isFirstStep && (
                <Button
                  type="button"
                  variant="outline"
                  onClick={handlePrevious}
                  disabled={isSubmitting}
                  className="flex items-center gap-2"
                >
                  <ChevronLeft className="h-4 w-4" />
                  {intl.formatMessage({
                    id: 'wizard.previous',
                    defaultMessage: 'Previous',
                  })}
                </Button>
              )}
            </div>

            <div className="flex gap-3">
              {onCancel && (
                <Button
                  type="button"
                  variant="ghost"
                  onClick={onCancel}
                  disabled={isSubmitting}
                >
                  {intl.formatMessage({
                    id: 'wizard.cancel',
                    defaultMessage: 'Cancel',
                  })}
                </Button>
              )}

              <Button
                type="button"
                onClick={handleNext}
                disabled={!isCurrentStepValid() || isSubmitting}
                className="flex items-center gap-2"
              >
                {isLastStep ? (
                  intl.formatMessage({
                    id: 'wizard.submit',
                    defaultMessage: 'Submit',
                  })
                ) : (
                  <>
                    {intl.formatMessage({
                      id: 'wizard.next',
                      defaultMessage: 'Next',
                    })}
                    <ChevronRight className="h-4 w-4" />
                  </>
                )}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default FormWizard;

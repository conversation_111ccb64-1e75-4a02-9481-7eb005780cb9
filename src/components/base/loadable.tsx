import { Suspense, ElementType } from 'react';
import { ErrorBoundary } from './boundary/error-boundary';
import { PageLoader } from './page-loader';

// ==============================|| LOADABLE - LAZY LOADING ||============================== //

/**
 * Simple and clean lazy loading wrapper for template projects
 *
 * Usage:
 * const LazyComponent = Loadable(lazy(() => import('./MyComponent')));
 *
 * This provides:
 * - Consistent loading states across the app
 * - Error boundaries for component failures
 * - Simple, maintainable pattern
 */
export const Loadable = (Component: ElementType) => (props: any) => (
  <ErrorBoundary level="component">
    <Suspense fallback={<PageLoader />}>
      <Component {...props} />
    </Suspense>
  </ErrorBoundary>
);

export default Loadable;

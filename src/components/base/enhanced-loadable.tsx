import { Suspense, ElementType, ComponentType } from 'react';
import { ErrorBoundary } from './boundary/error-boundary';
import { ApiErrorBoundary } from './boundary/api-error-boundary';
import { AsyncErrorBoundary } from './boundary/async-error-boundary';
import { DataErrorBoundary } from './boundary/data-error-boundary';
import { FormErrorBoundary } from './boundary/form-error-boundary';
import { PageLoader } from './page-loader';

// ==============================|| ENHANCED LOADABLE - LAZY LOADING WITH ERROR BOUNDARIES ||============================== //

type ErrorBoundaryType = 'generic' | 'api' | 'async' | 'data' | 'form';

interface LoadableOptions {
  errorBoundary?: ErrorBoundaryType;
  errorBoundaryLevel?: 'page' | 'component' | 'critical';
  fallback?: ComponentType;
  errorBoundaryProps?: Record<string, any>;
  onError?: (_error: Error, _errorInfo: any) => void;
  showOfflineMessage?: boolean;
  maxRetries?: number;
  retryDelay?: number;
  showProgress?: boolean;
}

/**
 * Enhanced lazy loading wrapper with configurable error boundaries
 *
 * Usage:
 * const LazyComponent = EnhancedLoadable(lazy(() => import('./MyComponent')), {
 *   errorBoundary: 'api',
 *   errorBoundaryLevel: 'page',
 *   maxRetries: 3
 * });
 *
 * This provides:
 * - Configurable error boundary types
 * - Consistent loading states across the app
 * - Specialized error handling based on component type
 * - Retry mechanisms and recovery options
 */
export const EnhancedLoadable = (
  Component: ElementType,
  options: LoadableOptions = {}
) => {
  const {
    errorBoundary = 'generic',
    errorBoundaryLevel = 'component',
    fallback: CustomFallback,
    errorBoundaryProps = {},
    onError,
    showOfflineMessage = false,
    maxRetries = 3,
    retryDelay = 1000,
    showProgress = true,
  } = options;

  const LoadingFallback = CustomFallback || PageLoader;

  const WrappedComponent = (props: any) => (
    <Suspense fallback={<LoadingFallback />}>
      <Component {...props} />
    </Suspense>
  );

  // Select appropriate error boundary based on type
  switch (errorBoundary) {
    case 'api':
      return (props: any) => (
        <ApiErrorBoundary
          onRetry={() => window.location.reload()}
          showOfflineMessage={showOfflineMessage}
          {...errorBoundaryProps}
        >
          <WrappedComponent {...props} />
        </ApiErrorBoundary>
      );

    case 'async':
      return (props: any) => (
        <AsyncErrorBoundary
          maxRetries={maxRetries}
          retryDelay={retryDelay}
          showProgress={showProgress}
          onRetry={async () => {
            // Custom retry logic can be passed via props
            if (errorBoundaryProps.onRetry) {
              await errorBoundaryProps.onRetry();
            }
          }}
          {...errorBoundaryProps}
        >
          <WrappedComponent {...props} />
        </AsyncErrorBoundary>
      );

    case 'data':
      return (props: any) => (
        <DataErrorBoundary
          dataSource="component"
          onRetry={() => window.location.reload()}
          {...errorBoundaryProps}
        >
          <WrappedComponent {...props} />
        </DataErrorBoundary>
      );

    case 'form':
      return (props: any) => (
        <FormErrorBoundary
          onReset={() => {
            // Custom reset logic can be passed via props
            if (errorBoundaryProps.onReset) {
              errorBoundaryProps.onReset();
            }
          }}
          showResetButton={true}
          {...errorBoundaryProps}
        >
          <WrappedComponent {...props} />
        </FormErrorBoundary>
      );

    case 'generic':
    default:
      return (props: any) => (
        <ErrorBoundary
          level={errorBoundaryLevel}
          onError={onError}
          showDetails={import.meta.env.DEV}
          {...errorBoundaryProps}
        >
          <WrappedComponent {...props} />
        </ErrorBoundary>
      );
  }
};

/**
 * Convenience functions for common error boundary patterns
 */

export const ApiLoadable = (
  Component: ElementType,
  options?: Omit<LoadableOptions, 'errorBoundary'>
) => EnhancedLoadable(Component, { ...options, errorBoundary: 'api' });

export const AsyncLoadable = (
  Component: ElementType,
  options?: Omit<LoadableOptions, 'errorBoundary'>
) => EnhancedLoadable(Component, { ...options, errorBoundary: 'async' });

export const DataLoadable = (
  Component: ElementType,
  options?: Omit<LoadableOptions, 'errorBoundary'>
) => EnhancedLoadable(Component, { ...options, errorBoundary: 'data' });

export const FormLoadable = (
  Component: ElementType,
  options?: Omit<LoadableOptions, 'errorBoundary'>
) => EnhancedLoadable(Component, { ...options, errorBoundary: 'form' });

export const PageLoadable = (
  Component: ElementType,
  options?: Omit<LoadableOptions, 'errorBoundary'>
) =>
  EnhancedLoadable(Component, {
    ...options,
    errorBoundary: 'generic',
    errorBoundaryLevel: 'page',
  });

export const ComponentLoadable = (
  Component: ElementType,
  options?: Omit<LoadableOptions, 'errorBoundary'>
) =>
  EnhancedLoadable(Component, {
    ...options,
    errorBoundary: 'generic',
    errorBoundaryLevel: 'component',
  });

export default EnhancedLoadable;

import React, { useState, useEffect } from 'react';

// third-party
import { IntlProvider, MessageFormatElement } from 'react-intl';
import useConfig from '@/hooks/use-config';

// Define array of translation categories
const translationCategories = [
  'auth',
  'common',
  'navigation',
  'pages',
  'support-center',
  'user',
];

// Define array of nested categories with their respective subcategories
const nestedCategories = [
  {
    parent: 'components',
    subcategories: ['filters'],
  },
  {
    parent: 'public',
    subcategories: [
      'about',
      'blog',
      'careers',
      'case-studies',
      'changelog',
      'contact',
      'cookies',
      'event',
      'faq',
      'gdpr',
      'help',
      'home',
      'integrations',
      'news',
      'partners',
      'pricing',
      'resources',
      'security',
      'services',
      'support',
      'team',
      'testimonials',
      'tickets'
    ],
  },
  // Add more nested categories as needed
];

/**
 * Dynamically loads and merges all translation files for a specific locale
 *
 * @param locale - The locale code to load translations for
 * @returns A promise resolving to the merged translations object
 */
const loadLocaleData = async (
  locale: string
): Promise<Record<string, string>> => {
  let messages: Record<string, string> = {};

  try {
    // Load all category files for the requested locale
    for (const category of translationCategories) {
      try {
        const module = await import(
          `@/utils/localization/locales/${locale}/${category}.json`
        );
        // Merge translations from this category into the messages object
        messages = { ...messages, ...module.default };
      } catch (error) {
        console.warn(
          `Failed to load translations for ${locale}/${category}:`,
          error
        );
      }
    }

    // Load all nested subcategory files
    for (const nestedCategory of nestedCategories) {
      const { parent, subcategories } = nestedCategory;
      for (const subcategory of subcategories) {
        try {
          const module = await import(
            `@/utils/localization/locales/${locale}/${parent}/${subcategory}.json`
          );
          // Add a namespace prefix to each key to avoid conflicts
          const prefixedTranslations: Record<string, string> = {};
          Object.entries(module.default).forEach(([key, value]) => {
            // Skip comment keys that start with "//"
            if (key.startsWith('//')) {
              return;
            }
            
            // Check if the key already starts with the parent name to avoid double prefixing
            if (key.startsWith(`${parent}.`)) {
              prefixedTranslations[key] = value as string;
            } else {
              prefixedTranslations[`${parent}.${key}`] = value as string;
            }
          });
          // Merge translations from this subcategory into the messages object
          messages = { ...messages, ...prefixedTranslations };
        } catch (error) {
          console.warn(
            `Failed to load translations for ${locale}/${parent}/${subcategory}:`,
            error
          );
        }
      }
    }

    if (Object.keys(messages).length === 0) {
      console.warn(
        `No translations found for locale: ${locale}, falling back to English`
      );
      if (locale !== 'en') {
        return loadLocaleData('en');
      }
    }

    return messages;
  } catch (error) {
    console.error(`Error loading translations for ${locale}:`, error);
    // Fallback to English if not already trying to load English
    if (locale !== 'en') {
      return loadLocaleData('en');
    }
    return {};
  }
};

// ==============================|| LOCALIZATION ||============================== //

interface LocalsProps {
  children: React.ReactNode;
}

const Locales = ({ children }: LocalsProps) => {
  const { locale } = useConfig();
  const [messages, setMessages] = useState<
    Record<string, string> | Record<string, MessageFormatElement[]> | undefined
  >();

  useEffect(() => {
    loadLocaleData(locale).then(loadedMessages => {
      setMessages(loadedMessages);
    });
  }, [locale]);

  return (
    <>
      {messages && (
        <IntlProvider locale={locale} defaultLocale="en" messages={messages}>
          {children}
        </IntlProvider>
      )}
    </>
  );
};

export default Locales;

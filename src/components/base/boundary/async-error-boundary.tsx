import { Component, ErrorInfo, ReactNode } from 'react';
import { <PERSON><PERSON><PERSON>riangle, Refresh<PERSON>w, Clock, Wifi } from 'lucide-react';
import { Button } from '@/components/ui/shadcn/button';
import {
  <PERSON>,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/shadcn/card';
import { Progress } from '@/components/ui/shadcn/progress';

interface Props {
  children: ReactNode;
  onRetry?: () => Promise<void>;
  maxRetries?: number;
  retryDelay?: number;
  showProgress?: boolean;
}

interface State {
  hasError: boolean;
  error: Error | null;
  retryCount: number;
  isRetrying: boolean;
  retryProgress: number;
}

/**
 * Async Operation Error Boundary
 *
 * Specialized for handling errors in async operations with:
 * - Automatic retry with exponential backoff
 * - Progress indication during retries
 * - Timeout handling
 * - Promise rejection handling
 */
export class AsyncErrorBoundary extends Component<Props, State> {
  private retryTimeoutId: number | null = null;
  private progressIntervalId: number | null = null;

  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      retryCount: 0,
      isRetrying: false,
      retryProgress: 0,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    // Only handle async-related errors
    if (AsyncErrorBoundary.isAsyncError(error)) {
      return {
        hasError: true,
        error,
      };
    }

    // Let other error boundaries handle non-async errors
    throw error;
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Async Error Boundary caught an error:', error);
    console.error('Error Info:', errorInfo);

    // Report async-specific errors
    this.reportAsyncError(error, errorInfo);

    // Start automatic retry for certain error types
    if (this.shouldAutoRetry(error)) {
      this.scheduleRetry();
    }
  }

  componentWillUnmount() {
    this.clearTimeouts();
  }

  private static isAsyncError(error: Error): boolean {
    const asyncErrorPatterns = [
      /promise/i,
      /async/i,
      /await/i,
      /timeout/i,
      /fetch/i,
      /network/i,
      /loading/i,
      /suspended/i,
    ];

    return asyncErrorPatterns.some(
      pattern =>
        pattern.test(error.message) ||
        pattern.test(error.name) ||
        pattern.test(error.stack || '')
    );
  }

  private shouldAutoRetry(error: Error): boolean {
    const { maxRetries = 3 } = this.props;

    if (this.state.retryCount >= maxRetries) {
      return false;
    }

    // Auto-retry for network and timeout errors
    const retryablePatterns = [
      /network/i,
      /timeout/i,
      /fetch/i,
      /connection/i,
      /temporary/i,
    ];

    return retryablePatterns.some(
      pattern => pattern.test(error.message) || pattern.test(error.name)
    );
  }

  private scheduleRetry = () => {
    const { retryDelay = 1000 } = this.props;
    const delay = retryDelay * Math.pow(2, this.state.retryCount); // Exponential backoff

    this.setState({ isRetrying: true, retryProgress: 0 });

    // Show progress during retry delay
    if (this.props.showProgress) {
      this.startProgressIndicator(delay);
    }

    this.retryTimeoutId = window.setTimeout(() => {
      this.handleRetry();
    }, delay);
  };

  private startProgressIndicator = (duration: number) => {
    const interval = 100; // Update every 100ms
    const steps = duration / interval;
    let currentStep = 0;

    this.progressIntervalId = window.setInterval(() => {
      currentStep++;
      const progress = (currentStep / steps) * 100;

      this.setState({ retryProgress: Math.min(progress, 100) });

      if (currentStep >= steps) {
        this.clearProgressIndicator();
      }
    }, interval);
  };

  private clearProgressIndicator = () => {
    if (this.progressIntervalId) {
      clearInterval(this.progressIntervalId);
      this.progressIntervalId = null;
    }
  };

  private clearTimeouts = () => {
    if (this.retryTimeoutId) {
      clearTimeout(this.retryTimeoutId);
      this.retryTimeoutId = null;
    }
    this.clearProgressIndicator();
  };

  private handleRetry = async () => {
    try {
      this.setState({
        retryCount: this.state.retryCount + 1,
        retryProgress: 100,
      });

      // Call custom retry handler if provided
      if (this.props.onRetry) {
        await this.props.onRetry();
      }

      // Reset error state on successful retry
      this.setState({
        hasError: false,
        error: null,
        retryCount: 0,
        isRetrying: false,
        retryProgress: 0,
      });
    } catch (retryError) {
      console.error('Retry failed:', retryError);

      // Update error and potentially schedule another retry
      this.setState({
        error: retryError as Error,
        isRetrying: false,
        retryProgress: 0,
      });

      if (this.shouldAutoRetry(retryError as Error)) {
        this.scheduleRetry();
      }
    }
  };

  private handleManualRetry = () => {
    this.setState({ retryCount: 0 }); // Reset retry count for manual retry
    this.handleRetry();
  };

  private reportAsyncError = (error: Error, errorInfo: ErrorInfo) => {
    // Use centralized async error boundary reporting
    import('@/utils/error-reporting').then(({ reportAsyncErrorBoundary }) => {
      reportAsyncErrorBoundary(error, errorInfo, {
        extra: {
          retryCount: this.state.retryCount,
          isOnline: navigator.onLine,
          isRetrying: this.state.isRetrying,
          retryProgress: this.state.retryProgress,
        },
        tags: {
          errorType: 'async',
          boundaryType: 'async',
        },
      });
    });
  };

  private getErrorType():
    | 'timeout'
    | 'network'
    | 'promise'
    | 'loading'
    | 'generic' {
    if (!this.state.error) return 'generic';

    const message = this.state.error.message.toLowerCase();

    if (message.includes('timeout')) return 'timeout';
    if (message.includes('network') || message.includes('fetch'))
      return 'network';
    if (message.includes('promise') || message.includes('async'))
      return 'promise';
    if (message.includes('loading') || message.includes('suspended'))
      return 'loading';

    return 'generic';
  }

  private renderErrorContent() {
    const errorType = this.getErrorType();
    const { maxRetries = 3, showProgress = true } = this.props;
    const { retryCount, isRetrying, retryProgress } = this.state;

    const errorConfig = {
      timeout: {
        icon: Clock,
        title: 'Operation Timed Out',
        description:
          'The operation took too long to complete. This might be due to a slow connection or server issues.',
        color: 'text-orange-600',
      },
      network: {
        icon: Wifi,
        title: 'Network Error',
        description:
          'Unable to connect to the server. Please check your internet connection and try again.',
        color: 'text-red-600',
      },
      promise: {
        icon: AlertTriangle,
        title: 'Async Operation Failed',
        description:
          'An asynchronous operation failed to complete successfully.',
        color: 'text-yellow-600',
      },
      loading: {
        icon: RefreshCw,
        title: 'Loading Error',
        description:
          'There was a problem loading the required data or components.',
        color: 'text-blue-600',
      },
      generic: {
        icon: AlertTriangle,
        title: 'Async Error',
        description:
          'An unexpected error occurred during an asynchronous operation.',
        color: 'text-red-600',
      },
    };

    const config = errorConfig[errorType];
    const Icon = config.icon;
    const canRetry = retryCount < maxRetries;

    return (
      <Card className="mx-auto w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-gray-100">
            <Icon
              className={`h-6 w-6 ${config.color} ${isRetrying ? 'animate-spin' : ''}`}
            />
          </div>
          <CardTitle className="text-lg">{config.title}</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-center text-sm text-muted-foreground">
            {config.description}
          </p>

          {/* Retry Information */}
          {retryCount > 0 && (
            <div className="text-center text-sm text-muted-foreground">
              Retry attempt {retryCount} of {maxRetries}
            </div>
          )}

          {/* Progress Bar */}
          {isRetrying && showProgress && (
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>Retrying...</span>
                <span>{Math.round(retryProgress)}%</span>
              </div>
              <Progress value={retryProgress} className="w-full" />
            </div>
          )}

          {/* Connection Status */}
          <div className="flex items-center justify-center gap-2 text-xs">
            <div
              className={`h-2 w-2 rounded-full ${
                navigator.onLine ? 'bg-green-600' : 'bg-red-600'
              }`}
            />
            <span
              className={navigator.onLine ? 'text-green-600' : 'text-red-600'}
            >
              {navigator.onLine ? 'Online' : 'Offline'}
            </span>
          </div>

          {/* Error Details in Development */}
          {import.meta.env.DEV && this.state.error && (
            <details className="rounded bg-muted p-3 text-xs">
              <summary className="mb-2 cursor-pointer font-medium">
                Development Error Details
              </summary>
              <div className="space-y-2">
                <div>
                  <strong>Message:</strong> {this.state.error.message}
                </div>
                <div>
                  <strong>Type:</strong> {errorType}
                </div>
                <div>
                  <strong>Retry Count:</strong> {retryCount}
                </div>
                {this.state.error.stack && (
                  <div>
                    <strong>Stack:</strong>
                    <pre className="mt-1 overflow-auto whitespace-pre-wrap text-xs">
                      {this.state.error.stack}
                    </pre>
                  </div>
                )}
              </div>
            </details>
          )}

          {/* Action Buttons */}
          <div className="flex gap-2">
            {canRetry && (
              <Button
                onClick={this.handleManualRetry}
                disabled={isRetrying}
                className="flex-1"
              >
                {isRetrying ? (
                  <>
                    <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                    Retrying...
                  </>
                ) : (
                  <>
                    <RefreshCw className="mr-2 h-4 w-4" />
                    Retry Now
                  </>
                )}
              </Button>
            )}

            <Button
              variant="outline"
              onClick={() => window.location.reload()}
              className="flex-1"
            >
              Reload Page
            </Button>
          </div>

          {!canRetry && !isRetrying && (
            <p className="text-center text-sm text-muted-foreground">
              Maximum retry attempts reached. Please reload the page or contact
              support.
            </p>
          )}
        </CardContent>
      </Card>
    );
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="flex min-h-[300px] items-center justify-center p-4">
          {this.renderErrorContent()}
        </div>
      );
    }

    return this.props.children;
  }
}

export default AsyncErrorBoundary;

import { Component, ErrorInfo, ReactNode } from 'react';
import { <PERSON>ert<PERSON>ircle, RefreshCw, Wifi, WifiOff } from 'lucide-react';
import { Button } from '@/components/ui/shadcn/button';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/shadcn/card';
import { Alert, AlertDescription } from '@/components/ui/shadcn/alert';

interface Props {
  children: ReactNode;
  onRetry?: () => void;
  showOfflineMessage?: boolean;
}

interface State {
  hasError: boolean;
  error: Error | null;
  isRetrying: boolean;
  isOnline: boolean;
  apiAvailable: boolean | null;
}

/**
 * API-specific Error Boundary
 *
 * Handles API-related errors with specific recovery strategies:
 * - Network connectivity issues
 * - API availability problems
 * - Authentication errors
 * - Rate limiting
 */
export class ApiErrorBoundary extends Component<Props, State> {
  private retryTimeoutId: number | null = null;

  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      isRetrying: false,
      isOnline: navigator.onLine,
      apiAvailable: null,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    // Check if this is an API-related error
    const isApiError = ApiErrorBoundary.isApiRelatedError(error);

    if (isApiError) {
      return {
        hasError: true,
        error,
      };
    }

    // If not API-related, let other error boundaries handle it
    throw error;
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('API Error Boundary caught an error:', error);
    console.error('Error Info:', errorInfo);

    // Check API availability when an error occurs
    this.checkApiAvailability();

    // Report API errors differently
    this.reportApiError(error, errorInfo);
  }

  componentDidMount() {
    // Listen for online/offline events
    window.addEventListener('online', this.handleOnline);
    window.addEventListener('offline', this.handleOffline);

    // Set initial API availability based on online status (no actual API call)
    this.setState({ apiAvailable: navigator.onLine });
  }

  componentWillUnmount() {
    window.removeEventListener('online', this.handleOnline);
    window.removeEventListener('offline', this.handleOffline);

    if (this.retryTimeoutId) {
      clearTimeout(this.retryTimeoutId);
    }
  }

  private static isApiRelatedError(error: Error): boolean {
    const apiErrorPatterns = [
      /network/i,
      /fetch/i,
      /axios/i,
      /api/i,
      /timeout/i,
      /connection/i,
      /cors/i,
      /401/,
      /403/,
      /404/,
      /500/,
      /502/,
      /503/,
      /504/,
    ];

    return apiErrorPatterns.some(
      pattern =>
        pattern.test(error.message) ||
        pattern.test(error.name) ||
        pattern.test(error.stack || '')
    );
  }

  private checkApiAvailability = async () => {
    try {
      // For demo purposes, skip actual API health check to prevent 405 errors
      // In a real application, you would check your actual health endpoint

      // Simulate API availability check without making real requests
      const isOnline = navigator.onLine;
      this.setState({ apiAvailable: isOnline });
    } catch {
      this.setState({ apiAvailable: false });
    }
  };

  private reportApiError = (error: Error, errorInfo: ErrorInfo) => {
    // Use centralized API error boundary reporting
    import('@/utils/error-reporting').then(({ reportApiErrorBoundary }) => {
      reportApiErrorBoundary(error, errorInfo, {
        extra: {
          isOnline: navigator.onLine,
          apiAvailable: this.state.apiAvailable,
          isRetrying: this.state.isRetrying,
        },
        tags: {
          errorType: 'api',
          boundaryType: 'api',
        },
      });
    });
  };

  private handleOnline = () => {
    this.setState({ isOnline: true });
    // Recheck API availability when coming back online
    this.checkApiAvailability();
  };

  private handleOffline = () => {
    this.setState({ isOnline: false, apiAvailable: false });
  };

  private handleRetry = async () => {
    this.setState({ isRetrying: true });

    try {
      // Check API availability before retrying
      await this.checkApiAvailability();

      // Call custom retry handler if provided
      if (this.props.onRetry) {
        this.props.onRetry();
      }

      // Reset error state after successful retry
      this.setState({
        hasError: false,
        error: null,
        isRetrying: false,
      });
    } catch (retryError) {
      console.error('Retry failed:', retryError);
      this.setState({ isRetrying: false });

      // Auto-retry after delay for certain errors
      this.scheduleAutoRetry();
    }
  };

  private scheduleAutoRetry = () => {
    // Auto-retry after 30 seconds for network issues
    if (!this.state.isOnline || !this.state.apiAvailable) {
      this.retryTimeoutId = window.setTimeout(() => {
        if (!this.state.isRetrying) {
          this.handleRetry();
        }
      }, 30000);
    }
  };

  private getErrorType(): 'network' | 'api' | 'auth' | 'generic' {
    if (!this.state.error) return 'generic';

    const message = this.state.error.message.toLowerCase();

    if (!this.state.isOnline) return 'network';
    if (message.includes('401') || message.includes('unauthorized'))
      return 'auth';
    if (message.includes('api') || message.includes('server')) return 'api';

    return 'generic';
  }

  private renderErrorContent() {
    const errorType = this.getErrorType();
    const { isRetrying, isOnline, apiAvailable } = this.state;

    const errorConfig = {
      network: {
        icon: WifiOff,
        title: 'Connection Problem',
        description: 'Please check your internet connection and try again.',
        color: 'text-orange-600',
      },
      api: {
        icon: AlertCircle,
        title: 'Service Unavailable',
        description:
          'Our servers are temporarily unavailable. Please try again in a moment.',
        color: 'text-red-600',
      },
      auth: {
        icon: AlertCircle,
        title: 'Authentication Error',
        description:
          'Your session may have expired. Please refresh the page or log in again.',
        color: 'text-yellow-600',
      },
      generic: {
        icon: AlertCircle,
        title: 'Something Went Wrong',
        description: 'An unexpected error occurred. Please try again.',
        color: 'text-red-600',
      },
    };

    const config = errorConfig[errorType];
    const Icon = config.icon;

    return (
      <Card className="mx-auto w-full max-w-md">
        <CardHeader className="text-center">
          <div
            className={`mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-gray-100`}
          >
            <Icon className={`h-6 w-6 ${config.color}`} />
          </div>
          <CardTitle className="text-lg">{config.title}</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-center text-sm text-muted-foreground">
            {config.description}
          </p>

          {/* Connection Status */}
          <div className="flex items-center justify-center gap-4 text-xs">
            <div className="flex items-center gap-1">
              {isOnline ? (
                <Wifi className="h-3 w-3 text-green-600" />
              ) : (
                <WifiOff className="h-3 w-3 text-red-600" />
              )}
              <span className={isOnline ? 'text-green-600' : 'text-red-600'}>
                {isOnline ? 'Online' : 'Offline'}
              </span>
            </div>

            {apiAvailable !== null && (
              <div className="flex items-center gap-1">
                <div
                  className={`h-2 w-2 rounded-full ${
                    apiAvailable ? 'bg-green-600' : 'bg-red-600'
                  }`}
                />
                <span
                  className={apiAvailable ? 'text-green-600' : 'text-red-600'}
                >
                  API {apiAvailable ? 'Available' : 'Unavailable'}
                </span>
              </div>
            )}
          </div>

          {/* Offline Message */}
          {this.props.showOfflineMessage && !isOnline && (
            <Alert>
              <WifiOff className="h-4 w-4" />
              <AlertDescription>
                You're currently offline. Some features may not be available.
              </AlertDescription>
            </Alert>
          )}

          {/* Error Details in Development */}
          {import.meta.env.DEV && this.state.error && (
            <details className="rounded bg-muted p-3 text-xs">
              <summary className="mb-2 cursor-pointer font-medium">
                Development Error Details
              </summary>
              <div className="space-y-1">
                <div>
                  <strong>Message:</strong> {this.state.error.message}
                </div>
                <div>
                  <strong>Stack:</strong>
                </div>
                <pre className="overflow-auto whitespace-pre-wrap text-xs">
                  {this.state.error.stack}
                </pre>
              </div>
            </details>
          )}

          <Button
            onClick={this.handleRetry}
            disabled={isRetrying}
            className="w-full"
          >
            {isRetrying ? (
              <>
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                Retrying...
              </>
            ) : (
              <>
                <RefreshCw className="mr-2 h-4 w-4" />
                Try Again
              </>
            )}
          </Button>
        </CardContent>
      </Card>
    );
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="flex min-h-[300px] items-center justify-center p-4">
          {this.renderErrorContent()}
        </div>
      );
    }

    return this.props.children;
  }
}

export default ApiErrorBoundary;

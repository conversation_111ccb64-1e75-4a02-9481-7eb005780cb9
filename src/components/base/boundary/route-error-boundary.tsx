import { useRouteError, useNavigate } from 'react-router-dom';
import { AlertTriangle, Home, ArrowLeft, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/shadcn/button';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/shadcn/card';

interface RouteError {
  status?: number;
  statusText?: string;
  message?: string;
  data?: any;
}

/**
 * Route Error Boundary Component
 *
 * Handles routing errors with React Router's error boundary system.
 * Provides different UI based on error type (404, 500, etc.)
 */
export function RouteErrorBoundary() {
  const error = useRouteError() as RouteError;
  const navigate = useNavigate();

  const handleGoBack = () => {
    navigate(-1);
  };

  const handleGoHome = () => {
    navigate('/');
  };

  const handleReload = () => {
    window.location.reload();
  };

  const getErrorInfo = () => {
    if (error?.status === 404) {
      return {
        title: 'Page Not Found',
        description:
          "The page you're looking for doesn't exist or has been moved.",
        showBackButton: true,
        showReloadButton: false,
      };
    }

    if (error?.status === 403) {
      return {
        title: 'Access Denied',
        description: "You don't have permission to access this page.",
        showBackButton: true,
        showReloadButton: false,
      };
    }

    if (error?.status === 500) {
      return {
        title: 'Server Error',
        description: 'Something went wrong on our end. Please try again later.',
        showBackButton: true,
        showReloadButton: true,
      };
    }

    // Generic error
    return {
      title: 'Something Went Wrong',
      description: 'An unexpected error occurred while loading this page.',
      showBackButton: true,
      showReloadButton: true,
    };
  };

  const errorInfo = getErrorInfo();

  return (
    <div className="flex min-h-screen items-center justify-center bg-background p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-destructive/10">
            <AlertTriangle className="h-6 w-6 text-destructive" />
          </div>
          <CardTitle className="text-xl">{errorInfo.title}</CardTitle>
          {error?.status && (
            <p className="text-sm text-muted-foreground">
              Error {error.status}
            </p>
          )}
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-center text-sm text-muted-foreground">
            {errorInfo.description}
          </p>

          {/* Error Details in Development */}
          {import.meta.env.DEV && (
            <details className="rounded bg-muted p-3 text-xs">
              <summary className="mb-2 cursor-pointer font-medium">
                Development Error Details
              </summary>
              <div className="space-y-2">
                {error?.status && (
                  <div>
                    <strong>Status:</strong> {error.status}
                  </div>
                )}
                {error?.statusText && (
                  <div>
                    <strong>Status Text:</strong> {error.statusText}
                  </div>
                )}
                {error?.message && (
                  <div>
                    <strong>Message:</strong> {error.message}
                  </div>
                )}
                {error?.data && (
                  <div>
                    <strong>Data:</strong>
                    <pre className="mt-1 overflow-auto text-xs">
                      {JSON.stringify(error.data, null, 2)}
                    </pre>
                  </div>
                )}
              </div>
            </details>
          )}

          <div className="flex flex-col gap-2">
            {errorInfo.showBackButton && (
              <Button
                onClick={handleGoBack}
                variant="outline"
                className="w-full"
              >
                <ArrowLeft className="mr-2 h-4 w-4" />
                Go Back
              </Button>
            )}

            <Button onClick={handleGoHome} className="w-full">
              <Home className="mr-2 h-4 w-4" />
              Go to Home
            </Button>

            {errorInfo.showReloadButton && (
              <Button
                onClick={handleReload}
                variant="outline"
                className="w-full"
              >
                <RefreshCw className="mr-2 h-4 w-4" />
                Reload Page
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default RouteErrorBoundary;

import { Component, ErrorInfo, ReactNode } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>gle, RefreshCw, Home, Bug } from 'lucide-react';
import { Button } from '@/components/ui/shadcn/button';
import {
  <PERSON>,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/shadcn/card';
import { handleErrorBoundary } from '@/utils/error-reporting';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (_error: Error, _errorInfo: ErrorInfo) => void;
  showDetails?: boolean;
  level?: 'page' | 'component' | 'critical';
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  errorId: string;
}

/**
 * Generic Error Boundary Component
 *
 * Provides different error UI based on the error level:
 * - 'critical': Full page error with navigation options
 * - 'page': Page-level error with retry options
 * - 'component': Component-level error with minimal UI
 */
export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: '',
    };
  }

  static getDerivedStateFromError(_error: Error): Partial<State> {
    return {
      hasError: true,
      error: _error,
      errorId: `error_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
    };
  }

  componentDidCatch(_error: Error, _errorInfo: ErrorInfo) {
    this.setState({ errorInfo: _errorInfo });

    // Log error details
    console.error('Error Boundary caught an error:', _error);
    console.error('Error Info:', _errorInfo);

    // Call custom error handler if provided
    this.props.onError?.(_error, _errorInfo);

    // In production, you might want to send this to an error reporting service
    if (import.meta.env.PROD) {
      this.reportError(_error, _errorInfo);
    }
  }

  private reportError = (_error: Error, _errorInfo: ErrorInfo) => {
    // Use centralized error reporting
    handleErrorBoundary(_error, _errorInfo);
  };

  private handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: '',
    });
  };

  private handleReload = () => {
    window.location.reload();
  };

  private handleGoHome = () => {
    window.location.href = '/';
  };

  private renderCriticalError() {
    return (
      <div className="flex min-h-screen items-center justify-center bg-background p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-destructive/10">
              <AlertTriangle className="h-6 w-6 text-destructive" />
            </div>
            <CardTitle className="text-xl">Application Error</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-center text-sm text-muted-foreground">
              Something went wrong and the application crashed. Please try
              refreshing the page or contact support if the problem persists.
            </p>

            {this.props.showDetails && this.state.error && (
              <details className="rounded bg-muted p-3 text-xs">
                <summary className="mb-2 cursor-pointer font-medium">
                  Error Details
                </summary>
                <div className="space-y-2">
                  <div>
                    <strong>Error ID:</strong> {this.state.errorId}
                  </div>
                  <div>
                    <strong>Message:</strong> {this.state.error.message}
                  </div>
                  {import.meta.env.DEV && (
                    <div>
                      <strong>Stack:</strong>
                      <pre className="mt-1 overflow-auto text-xs">
                        {this.state.error.stack}
                      </pre>
                    </div>
                  )}
                </div>
              </details>
            )}

            <div className="flex flex-col gap-2">
              <Button onClick={this.handleReload} className="w-full">
                <RefreshCw className="mr-2 h-4 w-4" />
                Reload Page
              </Button>
              <Button
                onClick={this.handleGoHome}
                variant="outline"
                className="w-full"
              >
                <Home className="mr-2 h-4 w-4" />
                Go to Home
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  private renderPageError() {
    return (
      <div className="flex min-h-[400px] items-center justify-center p-4">
        <Card className="w-full max-w-lg">
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 flex h-10 w-10 items-center justify-center rounded-full bg-destructive/10">
              <AlertTriangle className="h-5 w-5 text-destructive" />
            </div>
            <CardTitle>Page Error</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-center text-sm text-muted-foreground">
              This page encountered an error and couldn't load properly.
            </p>

            {this.props.showDetails && this.state.error && (
              <div className="rounded bg-muted p-3 text-xs">
                <strong>Error:</strong> {this.state.error.message}
              </div>
            )}

            <div className="flex gap-2">
              <Button onClick={this.handleRetry} className="flex-1">
                <RefreshCw className="mr-2 h-4 w-4" />
                Try Again
              </Button>
              <Button
                onClick={this.handleGoHome}
                variant="outline"
                className="flex-1"
              >
                <Home className="mr-2 h-4 w-4" />
                Go Home
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  private renderComponentError() {
    return (
      <div className="rounded-lg border border-destructive/20 bg-destructive/5 p-4">
        <div className="mb-2 flex items-center gap-2 text-sm text-destructive">
          <Bug className="h-4 w-4" />
          <span className="font-medium">Component Error</span>
        </div>
        <p className="mb-3 text-xs text-muted-foreground">
          This component failed to render properly.
        </p>
        <Button onClick={this.handleRetry} size="sm" variant="outline">
          <RefreshCw className="mr-1 h-3 w-3" />
          Retry
        </Button>
      </div>
    );
  }

  render() {
    if (this.state.hasError) {
      // Return custom fallback if provided
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Return appropriate error UI based on level
      switch (this.props.level) {
        case 'critical':
          return this.renderCriticalError();
        case 'page':
          return this.renderPageError();
        case 'component':
        default:
          return this.renderComponentError();
      }
    }

    return this.props.children;
  }
}

export default ErrorBoundary;

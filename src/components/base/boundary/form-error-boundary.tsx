import { Component, ErrorInfo, ReactNode } from 'react';
import { <PERSON><PERSON><PERSON>riangle, RefreshCw, FileX } from 'lucide-react';
import { Button } from '@/components/ui/shadcn/button';
import {
  <PERSON>,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/shadcn/card';
import { Alert, AlertDescription } from '@/components/ui/shadcn/alert';

interface Props {
  children: ReactNode;
  onReset?: () => void;
  fallbackMessage?: string;
  showResetButton?: boolean;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorId: string;
}

/**
 * Form-specific Error Boundary
 *
 * Handles form-related errors with specific recovery strategies:
 * - Validation errors
 * - Submission failures
 * - Field rendering errors
 * - Form state corruption
 */
export class FormErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorId: '',
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    return {
      hasError: true,
      error,
      errorId: `form_error_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Form Error Boundary caught an error:', error);
    console.error('Error Info:', errorInfo);

    // Report form-specific errors
    this.reportFormError(error, errorInfo);
  }

  private reportFormError = (error: Error, errorInfo: ErrorInfo) => {
    // Use centralized form error boundary reporting
    import('@/utils/error-reporting').then(({ reportFormErrorBoundary }) => {
      reportFormErrorBoundary(error, errorInfo, {
        extra: {
          errorId: this.state.errorId,
          formData: this.extractFormData(),
        },
        tags: {
          errorType: 'form',
          boundaryType: 'form',
        },
      });
    });
  };

  private extractFormData = () => {
    try {
      // Try to extract form data from the DOM
      const forms = document.querySelectorAll('form');
      const formData: Record<string, any> = {};

      forms.forEach((form, index) => {
        const data = new FormData(form);
        const formObject: Record<string, any> = {};

        for (const [key, value] of data.entries()) {
          // Don't capture sensitive data
          if (this.isSensitiveField(key)) {
            formObject[key] = '[REDACTED]';
          } else {
            formObject[key] = value;
          }
        }

        formData[`form_${index}`] = formObject;
      });

      return formData;
    } catch {
      return { error: 'Could not extract form data' };
    }
  };

  private isSensitiveField = (fieldName: string): boolean => {
    const sensitivePatterns = [
      /password/i,
      /token/i,
      /secret/i,
      /key/i,
      /credit/i,
      /card/i,
      /ssn/i,
      /social/i,
    ];

    return sensitivePatterns.some(pattern => pattern.test(fieldName));
  };

  private handleReset = () => {
    // Call custom reset handler if provided
    this.props.onReset?.();

    // Reset error state
    this.setState({
      hasError: false,
      error: null,
      errorId: '',
    });
  };

  private getErrorType():
    | 'validation'
    | 'submission'
    | 'rendering'
    | 'generic' {
    if (!this.state.error) return 'generic';

    const message = this.state.error.message.toLowerCase();
    const stack = this.state.error.stack?.toLowerCase() || '';

    if (message.includes('validation') || message.includes('invalid'))
      return 'validation';
    if (
      message.includes('submit') ||
      message.includes('post') ||
      message.includes('put')
    )
      return 'submission';
    if (stack.includes('render') || message.includes('render'))
      return 'rendering';

    return 'generic';
  }

  private renderErrorContent() {
    const errorType = this.getErrorType();
    const { showResetButton = true, fallbackMessage } = this.props;

    const errorConfig = {
      validation: {
        icon: AlertTriangle,
        title: 'Form Validation Error',
        description:
          fallbackMessage ||
          'There was a problem validating your form data. Please check your inputs and try again.',
        color: 'text-yellow-600',
        bgColor: 'bg-yellow-50',
      },
      submission: {
        icon: FileX,
        title: 'Form Submission Failed',
        description:
          fallbackMessage ||
          'Your form could not be submitted. Please try again or contact support if the problem persists.',
        color: 'text-red-600',
        bgColor: 'bg-red-50',
      },
      rendering: {
        icon: AlertTriangle,
        title: 'Form Display Error',
        description:
          fallbackMessage ||
          'There was a problem displaying this form. Please refresh the page and try again.',
        color: 'text-orange-600',
        bgColor: 'bg-orange-50',
      },
      generic: {
        icon: AlertTriangle,
        title: 'Form Error',
        description:
          fallbackMessage ||
          'An unexpected error occurred with this form. Please try again.',
        color: 'text-red-600',
        bgColor: 'bg-red-50',
      },
    };

    const config = errorConfig[errorType];
    const Icon = config.icon;

    return (
      <Card className="mx-auto w-full max-w-lg">
        <CardHeader className="text-center">
          <div
            className={`mx-auto mb-4 h-12 w-12 rounded-full ${config.bgColor} flex items-center justify-center`}
          >
            <Icon className={`h-6 w-6 ${config.color}`} />
          </div>
          <CardTitle className="text-lg">{config.title}</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-center text-sm text-muted-foreground">
            {config.description}
          </p>

          {/* Error ID for support */}
          <Alert>
            <AlertDescription className="text-xs">
              <strong>Error ID:</strong> {this.state.errorId}
              <br />
              <span className="text-muted-foreground">
                Please include this ID when contacting support.
              </span>
            </AlertDescription>
          </Alert>

          {/* Error Details in Development */}
          {import.meta.env.DEV && this.state.error && (
            <details className="rounded bg-muted p-3 text-xs">
              <summary className="mb-2 cursor-pointer font-medium">
                Development Error Details
              </summary>
              <div className="space-y-2">
                <div>
                  <strong>Message:</strong> {this.state.error.message}
                </div>
                <div>
                  <strong>Type:</strong> {errorType}
                </div>
                {this.state.error.stack && (
                  <div>
                    <strong>Stack:</strong>
                    <pre className="mt-1 overflow-auto whitespace-pre-wrap text-xs">
                      {this.state.error.stack}
                    </pre>
                  </div>
                )}
              </div>
            </details>
          )}

          {showResetButton && (
            <div className="flex gap-2">
              <Button onClick={this.handleReset} className="flex-1">
                <RefreshCw className="mr-2 h-4 w-4" />
                Reset Form
              </Button>
              <Button
                variant="outline"
                onClick={() => window.location.reload()}
                className="flex-1"
              >
                Reload Page
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    );
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="flex min-h-[200px] items-center justify-center p-4">
          {this.renderErrorContent()}
        </div>
      );
    }

    return this.props.children;
  }
}

export default FormErrorBoundary;

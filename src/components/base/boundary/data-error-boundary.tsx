import { Component, ErrorInfo, ReactNode } from 'react';
import {
  AlertTriangle,
  RefreshCw,
  Database,
  FileX,
  Search,
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/shadcn/button';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/shadcn/card';
import { Alert, AlertDescription } from '@/components/ui/shadcn/alert';

interface Props {
  children: ReactNode;
  onRetry?: () => void;
  onClearCache?: () => void;
  fallbackData?: any;
  dataSource?: string;
  showFallback?: boolean;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorId: string;
  usingFallback: boolean;
}

/**
 * Data-specific Error Boundary
 *
 * Handles data-related errors with specific recovery strategies:
 * - Data parsing errors
 * - Schema validation failures
 * - Cache corruption
 * - Data transformation errors
 * - Fallback to cached or mock data
 */
export class DataErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorId: '',
      usingFallback: false,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    // Only handle data-related errors
    if (DataErrorBoundary.isDataError(error)) {
      return {
        hasError: true,
        error,
        errorId: `data_error_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
      };
    }

    // Let other error boundaries handle non-data errors
    throw error;
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Data Error Boundary caught an error:', error);
    console.error('Error Info:', errorInfo);

    // Report data-specific errors
    this.reportDataError(error, errorInfo);

    // Try to use fallback data if available
    if (this.props.fallbackData && this.props.showFallback) {
      this.setState({ usingFallback: true });
    }
  }

  private static isDataError(error: Error): boolean {
    const dataErrorPatterns = [
      /parse/i,
      /json/i,
      /schema/i,
      /validation/i,
      /transform/i,
      /serialize/i,
      /deserialize/i,
      /mapping/i,
      /property.*undefined/i,
      /cannot read property/i,
      /cannot access before initialization/i,
      /unexpected token/i,
    ];

    return dataErrorPatterns.some(
      pattern =>
        pattern.test(error.message) ||
        pattern.test(error.name) ||
        pattern.test(error.stack || '')
    );
  }

  private reportDataError = (error: Error, errorInfo: ErrorInfo) => {
    // Use centralized data error boundary reporting
    import('@/utils/error-reporting').then(({ reportDataErrorBoundary }) => {
      reportDataErrorBoundary(error, errorInfo, {
        extra: {
          errorId: this.state.errorId,
          dataSource: this.props.dataSource,
          hasFallback: !!this.props.fallbackData,
          usingFallback: this.state.usingFallback,
        },
        tags: {
          errorType: 'data',
          boundaryType: 'data',
        },
      });
    });
  };

  private handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorId: '',
      usingFallback: false,
    });

    this.props.onRetry?.();
  };

  private handleClearCache = () => {
    this.props.onClearCache?.();
    this.handleRetry();
  };

  private handleUseFallback = () => {
    this.setState({ usingFallback: true });
  };

  private getErrorType():
    | 'parsing'
    | 'validation'
    | 'transformation'
    | 'access'
    | 'generic' {
    if (!this.state.error) return 'generic';

    const message = this.state.error.message.toLowerCase();

    if (
      message.includes('parse') ||
      message.includes('json') ||
      message.includes('unexpected token')
    ) {
      return 'parsing';
    }
    if (message.includes('validation') || message.includes('schema')) {
      return 'validation';
    }
    if (
      message.includes('transform') ||
      message.includes('serialize') ||
      message.includes('mapping')
    ) {
      return 'transformation';
    }
    if (
      message.includes('property') ||
      message.includes('access') ||
      message.includes('undefined')
    ) {
      return 'access';
    }

    return 'generic';
  }

  private renderErrorContent() {
    const errorType = this.getErrorType();
    const { fallbackData, showFallback = true, dataSource } = this.props;
    const { usingFallback } = this.state;

    const errorConfig = {
      parsing: {
        icon: FileX,
        title: 'Data Parsing Error',
        description:
          'The data received could not be parsed correctly. This might be due to invalid JSON or unexpected data format.',
        color: 'text-red-600',
        suggestions: [
          'Check if the data source is returning valid JSON',
          'Verify the API response format',
          'Clear cache and try again',
        ],
      },
      validation: {
        icon: AlertTriangle,
        title: 'Data Validation Error',
        description: 'The data does not match the expected schema or format.',
        color: 'text-yellow-600',
        suggestions: [
          'Check if the data structure has changed',
          'Verify required fields are present',
          'Update data validation rules if needed',
        ],
      },
      transformation: {
        icon: Database,
        title: 'Data Transformation Error',
        description: 'There was a problem transforming or processing the data.',
        color: 'text-orange-600',
        suggestions: [
          'Check data transformation logic',
          'Verify data types and formats',
          'Review mapping configurations',
        ],
      },
      access: {
        icon: Search,
        title: 'Data Access Error',
        description:
          'Unable to access a required property or field in the data.',
        color: 'text-blue-600',
        suggestions: [
          'Check if the property exists in the data',
          'Verify object structure',
          'Add null/undefined checks',
        ],
      },
      generic: {
        icon: AlertTriangle,
        title: 'Data Error',
        description: 'An unexpected error occurred while processing data.',
        color: 'text-red-600',
        suggestions: [
          'Try refreshing the data',
          'Clear cache if available',
          'Contact support if the problem persists',
        ],
      },
    };

    const config = errorConfig[errorType];
    const Icon = config.icon;

    if (usingFallback && fallbackData) {
      return (
        <div className="space-y-4">
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              Using fallback data due to a data error. Some information may be
              outdated or incomplete.
            </AlertDescription>
          </Alert>
          {this.props.children}
        </div>
      );
    }

    return (
      <Card className="mx-auto w-full max-w-lg">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-gray-100">
            <Icon className={`h-6 w-6 ${config.color}`} />
          </div>
          <CardTitle className="text-lg">{config.title}</CardTitle>
          {dataSource && (
            <p className="text-sm text-muted-foreground">
              Data Source: {dataSource}
            </p>
          )}
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-center text-sm text-muted-foreground">
            {config.description}
          </p>

          {/* Error ID */}
          <Alert>
            <AlertDescription className="text-xs">
              <strong>Error ID:</strong> {this.state.errorId}
            </AlertDescription>
          </Alert>

          {/* Suggestions */}
          <div className="space-y-2">
            <h4 className="text-sm font-medium">
              Troubleshooting suggestions:
            </h4>
            <ul className="space-y-1 text-xs text-muted-foreground">
              {config.suggestions.map((suggestion, index) => (
                <li key={index} className="flex items-start gap-2">
                  <span className="text-primary">•</span>
                  <span>{suggestion}</span>
                </li>
              ))}
            </ul>
          </div>

          {/* Error Details in Development */}
          {import.meta.env.DEV && this.state.error && (
            <details className="rounded bg-muted p-3 text-xs">
              <summary className="mb-2 cursor-pointer font-medium">
                Development Error Details
              </summary>
              <div className="space-y-2">
                <div>
                  <strong>Message:</strong> {this.state.error.message}
                </div>
                <div>
                  <strong>Type:</strong> {errorType}
                </div>
                <div>
                  <strong>Data Source:</strong> {dataSource || 'Unknown'}
                </div>
                {this.state.error.stack && (
                  <div>
                    <strong>Stack:</strong>
                    <pre className="mt-1 overflow-auto whitespace-pre-wrap text-xs">
                      {this.state.error.stack}
                    </pre>
                  </div>
                )}
              </div>
            </details>
          )}

          {/* Action Buttons */}
          <div className="space-y-2">
            <div className="flex gap-2">
              <Button onClick={this.handleRetry} className="flex-1">
                <RefreshCw className="mr-2 h-4 w-4" />
                Retry
              </Button>

              {this.props.onClearCache && (
                <Button
                  variant="outline"
                  onClick={this.handleClearCache}
                  className="flex-1"
                >
                  <Database className="mr-2 h-4 w-4" />
                  Clear Cache
                </Button>
              )}
            </div>

            {fallbackData && showFallback && !usingFallback && (
              <Button
                variant="secondary"
                onClick={this.handleUseFallback}
                className="w-full"
              >
                Use Fallback Data
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    );
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="flex min-h-[200px] items-center justify-center p-4">
          {this.renderErrorContent()}
        </div>
      );
    }

    return this.props.children;
  }
}

export default DataErrorBoundary;

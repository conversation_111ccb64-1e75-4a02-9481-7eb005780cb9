/**
 * Loader Component
 *
 * A versatile loading component that provides both a linear progress bar
 * and skeleton loading states for different UI elements.
 *
 * Features:
 * - Two variants: "linear" (default) and "skeleton"
 * - Linear variant: Fixed positioning at the top of the viewport with smooth animations
 * - Skeleton variant: Customizable placeholder with pulsing animation
 * - Uses theme's primary color with variable opacity
 * - Smooth animations powered by Framer Motion
 * - Configurable properties for both variants
 *
 * @component
 * @example
 * // Basic linear loader
 * return <Loader />
 *
 * // Linear loader with custom height
 * return <Loader height={4} />
 *
 * // Skeleton loader for text
 * return <Loader variant="skeleton" className="h-4 w-[250px]" />
 *
 * // Skeleton loader for image
 * return <Loader variant="skeleton" className="h-[200px] w-full rounded-md" />
 */

import { memo } from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

interface LoaderProps {
  /**
   * Variant of the loader component
   * @default "linear"
   */
  variant?: 'linear' | 'skeleton';

  /**
   * Height of the linear loader bar in pixels
   * Only applies to the linear variant
   * @default 2
   */
  height?: number;

  /**
   * Additional classes to apply to the component
   * Particularly useful for the skeleton variant to control dimensions
   */
  className?: string;
}

export const Loader = memo(
  ({ variant = 'linear', height = 2, className }: LoaderProps = {}) => {
    // Skeleton variant - similar to the Skeleton component but with Framer Motion
    if (variant === 'skeleton') {
      return (
        <motion.div
          className={cn('rounded-md bg-muted', className)}
          animate={{ opacity: [0.5, 0.8, 0.5] }}
          transition={{
            repeat: Infinity,
            duration: 1.5,
            ease: 'easeInOut',
          }}
        />
      );
    }

    // Linear progress bar variant (default)
    return (
      <div
        className="fixed left-0 top-0 z-[1301] w-full bg-primary/10"
        style={{ height: `${height}px` }}
      >
        <motion.div
          className="h-full origin-left bg-primary"
          initial={{ scaleX: 0.1, x: '0%' }}
          animate={{
            scaleX: [0.1, 0.3, 0.7, 0.9],
            x: ['0%', '30%', '60%', '100%'],
          }}
          transition={{
            repeat: Infinity,
            duration: 1.5,
            ease: 'easeInOut',
          }}
        />
      </div>
    );
  }
);

Loader.displayName = 'Loader';

export default Loader;

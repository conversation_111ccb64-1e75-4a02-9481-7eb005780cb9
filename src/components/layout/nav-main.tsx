'use client';

import { memo, useState } from 'react';
import { ChevronRight, type LucideIcon } from 'lucide-react';
import { useIntl } from 'react-intl';
import { Link } from 'react-router-dom';
import { cn } from '@/lib/utils';

import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/shadcn/collapsible';
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubItem,
  useSidebar,
} from '@/components/ui/shadcn/sidebar';
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from '@/components/ui/shadcn/hover-card';
import { useActiveRoute } from '@/hooks/use-active-route';

interface NavMainItem {
  title: string;
  url: string;
  icon?: LucideIcon;
  isActive?: boolean;
  items?: {
    title: string;
    url: string;
    isActive?: boolean;
  }[];
}

export const NavMain = memo(({ items }: { items: NavMainItem[] }) => {
  const { isActive } = useActiveRoute();
  const { state } = useSidebar();
  const intl = useIntl();
  const [openSections, setOpenSections] = useState<Record<string, boolean>>({});

  const toggleSection = (title: string) => {
    setOpenSections(prev => ({
      ...prev,
      [title]: !prev[title],
    }));
  };

  // Component for collapsed sidebar with sub-items navigation
  const CollapsedNavigationItem = ({ item }: { item: NavMainItem }) => {
    const isItemActive = isActive(item.url);
    const isChildActive = item.items?.some(child => isActive(child.url));

    // If item has children, show HoverCard (no tooltip, parent not clickable)
    if (item.items && item.items.length > 0) {
      return (
        <HoverCard openDelay={200} closeDelay={100}>
          <HoverCardTrigger asChild>
            <button
              className={cn(
                'peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-none ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0',
                'cursor-pointer transition-colors duration-200',
                isItemActive || isChildActive
                  ? 'bg-sidebar-accent font-medium text-sidebar-accent-foreground'
                  : 'text-sidebar-foreground'
              )}
            >
              {item.icon && <item.icon />}
              <span>{item.title}</span>
            </button>
          </HoverCardTrigger>
          <HoverCardContent
            side="right"
            align="start"
            className="w-64 border-sidebar-border bg-sidebar p-1 shadow-lg"
            sideOffset={8}
          >
            <div className="space-y-1">
              {/* Parent item as header - not clickable when it has children */}
              <div
                className={cn(
                  'flex items-center gap-2 rounded-md px-2 py-2 text-sm font-medium',
                  'text-sidebar-foreground',
                  isItemActive
                    ? 'bg-sidebar-accent font-medium text-sidebar-accent-foreground'
                    : ''
                )}
              >
                {item.icon && <item.icon className="h-4 w-4 shrink-0" />}
                <span className="truncate">{item.title}</span>
              </div>

              {/* Separator */}
              {item.items && item.items.length > 0 && (
                <div className="mx-2 my-1 h-px bg-sidebar-border" />
              )}

              {/* Sub-items */}
              {item.items?.map(subItem => (
                <Link
                  key={subItem.title}
                  to={subItem.url}
                  className={cn(
                    'flex items-center rounded-md px-2 py-1.5 text-sm outline-none transition-colors',
                    'text-sidebar-foreground hover:bg-sidebar-accent hover:text-sidebar-accent-foreground',
                    'focus-visible:bg-sidebar-accent focus-visible:text-sidebar-accent-foreground focus-visible:ring-2 focus-visible:ring-sidebar-ring',
                    isActive(subItem.url)
                      ? 'bg-sidebar-accent font-medium text-sidebar-accent-foreground'
                      : ''
                  )}
                >
                  <span className="ml-6 truncate">{subItem.title}</span>
                </Link>
              ))}
            </div>
          </HoverCardContent>
        </HoverCard>
      );
    }

    // If item has no children, show with tooltip (clickable parent)
    return (
      <SidebarMenuButton
        tooltip={item.title}
        isActive={isItemActive}
        asChild
        className={cn(
          'transition-colors duration-200',
          isItemActive
            ? 'bg-sidebar-accent font-medium text-sidebar-accent-foreground'
            : ''
        )}
      >
        <Link to={item.url}>
          {item.icon && <item.icon />}
          <span>{item.title}</span>
        </Link>
      </SidebarMenuButton>
    );
  };

  return (
    <SidebarGroup>
      <SidebarGroupLabel>
        {intl.formatMessage({ id: 'nav.main.platform' })}
      </SidebarGroupLabel>
      <SidebarMenu>
        {items.map(item => {
          const isItemActive = isActive(item.url);
          const isChildActive = item.items?.some(child => isActive(child.url));
          const shouldBeOpen =
            openSections[item.title] || isItemActive || isChildActive;

          // Handle collapsed sidebar state
          if (state === 'collapsed') {
            return (
              <SidebarMenuItem key={item.title}>
                {item.items && item.items.length > 0 ? (
                  <CollapsedNavigationItem item={item} />
                ) : (
                  <SidebarMenuButton
                    tooltip={item.title}
                    isActive={isItemActive}
                    asChild
                    className={cn(
                      'transition-colors duration-200',
                      isItemActive
                        ? 'bg-sidebar-accent font-medium text-sidebar-accent-foreground'
                        : ''
                    )}
                  >
                    <Link to={item.url}>
                      {item.icon && <item.icon />}
                      <span>{item.title}</span>
                    </Link>
                  </SidebarMenuButton>
                )}
              </SidebarMenuItem>
            );
          }

          // Handle expanded sidebar state (existing behavior)
          // Handle menu items without sub-items differently
          if (!item.items || item.items.length === 0) {
            return (
              <SidebarMenuItem key={item.title}>
                <SidebarMenuButton
                  tooltip={item.title}
                  isActive={isItemActive}
                  asChild
                  className={cn(
                    'transition-colors duration-200',
                    isItemActive
                      ? 'bg-sidebar-accent font-medium text-sidebar-accent-foreground'
                      : ''
                  )}
                >
                  <Link to={item.url}>
                    {item.icon && <item.icon />}
                    <span>{item.title}</span>
                  </Link>
                </SidebarMenuButton>
              </SidebarMenuItem>
            );
          }

          // Handle menu items with sub-items (existing code)
          return (
            <Collapsible
              key={item.title}
              asChild
              defaultOpen={item.isActive}
              className="group/collapsible"
              open={shouldBeOpen}
              onOpenChange={() => toggleSection(item.title)}
            >
              <SidebarMenuItem>
                <CollapsibleTrigger asChild>
                  <SidebarMenuButton
                    tooltip={item.title}
                    className={cn(
                      'transition-colors duration-200',
                      isItemActive || isChildActive
                        ? 'bg-sidebar-accent font-medium text-sidebar-accent-foreground'
                        : ''
                    )}
                  >
                    {item.icon && <item.icon />}
                    <span>{item.title}</span>
                    <ChevronRight className="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
                  </SidebarMenuButton>
                </CollapsibleTrigger>

                <CollapsibleContent>
                  <SidebarMenuSub>
                    {item.items?.map(subItem => (
                      <SidebarMenuSubItem key={subItem.title}>
                        <Link
                          to={subItem.url}
                          className={cn(
                            'flex w-full items-center rounded-md px-2 py-1.5 text-sm outline-none transition-colors duration-200',
                            'text-sidebar-foreground hover:bg-sidebar-accent hover:text-sidebar-accent-foreground',
                            'focus-visible:bg-sidebar-accent focus-visible:text-sidebar-accent-foreground focus-visible:ring-2 focus-visible:ring-sidebar-ring',
                            isActive(subItem.url)
                              ? 'bg-sidebar-accent font-medium text-sidebar-accent-foreground'
                              : ''
                          )}
                        >
                          <span>{subItem.title}</span>
                        </Link>
                      </SidebarMenuSubItem>
                    ))}
                  </SidebarMenuSub>
                </CollapsibleContent>
              </SidebarMenuItem>
            </Collapsible>
          );
        })}
      </SidebarMenu>
    </SidebarGroup>
  );
});

NavMain.displayName = 'NavMain';

import * as React from 'react';
import { useIntl } from 'react-intl';
import { memo, useMemo } from 'react';
import {
  Frame,
  GalleryVerticalEnd,
  Home,
  SquareTerminal,
  BookOpen,
  TestTube,
} from 'lucide-react';

import { NavMain } from '@/components/layout/nav-main';
import { NavProjects } from '@/components/layout/nav-projects';
import { NavUser } from '@/components/layout/nav-user';
import { TeamSwitcher } from '@/components/layout/team-switcher';
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
} from '@/components/ui/shadcn/sidebar';
import { useActiveRoute } from '@/hooks/use-active-route';

// Memoize the entire AppSidebar component
export const AppSidebar = memo(
  ({ ...props }: React.ComponentProps<typeof Sidebar>) => {
    const intl = useIntl();
    const { isActive } = useActiveRoute();

    // Memoize the static data structure with active states
    const data = useMemo(
      () => ({
        teams: [
          {
            name: 'Capax Tech',
            logo: GalleryVerticalEnd,
            plan: 'Enterprise',
            iconBackground: 'bg-primary/10',
            iconForeground: 'text-primary',
          },
        ],
        navMain: [
          {
            title: intl.formatMessage({ id: 'nav.main.home' }),
            url: '/home',
            icon: Home,
            isActive: isActive('/home'),
            items: [
              {
                title: intl.formatMessage({ id: 'nav.main.home.dashboard' }),
                url: '/dashboard',
                isActive: isActive('/dashboard'),
              },
            ],
          },
          {
            title: intl.formatMessage({ id: 'nav.main.playground' }),
            url: '#',
            icon: SquareTerminal,
            isActive: isActive('/playground'),
            items: [
              {
                title: intl.formatMessage({
                  id: 'nav.main.playground.history',
                }),
                url: '/history',
                isActive: isActive('/history'),
              },
              {
                title: intl.formatMessage({
                  id: 'nav.main.playground.starred',
                }),
                url: '#',
                isActive: isActive('/starred'),
              },
            ],
          },
          {
            title: intl.formatMessage({ id: 'nav.main.docs' }),
            url: '/docs',
            icon: BookOpen,
            isActive: isActive('/docs'),
          },
          {
            title: intl.formatMessage({ id: 'nav.main.demo' }),
            url: '#',
            icon: TestTube,
            isActive: isActive('/demo'),
            items: [
              {
                title: intl.formatMessage({
                  id: 'nav.main.demo.error-boundaries',
                }),
                url: '/demo/error-boundaries',
                isActive: isActive('/demo/error-boundaries'),
              },
              {
                title: intl.formatMessage({ id: 'nav.main.demo.toast' }),
                url: '/demo/toast',
                isActive: isActive('/demo/toast'),
              },
              {
                title: intl.formatMessage({
                  id: 'nav.main.demo.api-integration',
                }),
                url: '/demo/api-integration',
                isActive: isActive('/demo/api-integration'),
              },
            ],
          },
        ],
        projects: [
          {
            name: intl.formatMessage({ id: 'nav.projects.design' }),
            url: '#',
            icon: Frame,
            isActive: isActive('/design'),
          },
        ],
      }),
      [intl, isActive]
    );

    // Check if current route is a user page
    const isUserPage =
      isActive('user/profile') ||
      isActive('user/security') ||
      isActive('user/notifications') ||
      isActive('user/settings');

    // Don't render the AppSidebar on user pages, as they will use UserSidebar instead
    if (isUserPage) {
      return null;
    }

    return (
      <Sidebar collapsible="icon" {...props}>
        <SidebarHeader>
          <TeamSwitcher teams={data.teams} />
        </SidebarHeader>
        <SidebarContent>
          <NavMain items={data.navMain} />
          <NavProjects projects={data.projects} />
        </SidebarContent>
        <SidebarFooter>
          <NavUser />
        </SidebarFooter>
        <SidebarRail />
      </Sidebar>
    );
  }
);

AppSidebar.displayName = 'AppSidebar';

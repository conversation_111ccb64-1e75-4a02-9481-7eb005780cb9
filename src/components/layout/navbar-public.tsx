import React, { useState, useEffect, useRef } from 'react';
import { Link } from 'react-router-dom';
import { useIntl } from 'react-intl';
import { SITE_INFO } from '@/constants/site-config';
import useConfig from '@/hooks/use-config';
import useAuth from '@/hooks/use-auth'; // Import useAuth hook
import { Globe, Check } from 'lucide-react';
// import { useRoutePreloader } from '@/hooks/use-route-preloader'; // Removed over-engineered preloader
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/shadcn/dropdown-menu';
import { ThemeSwitcher } from '@/components/ui/theme-switcher';
import { cn } from '@/lib/utils';
import '@/styles/animations.css'; // Import the centralized animations

/**
 * NavbarPublic Component
 *
 * @description Provides the header navigation for public pages including
 * logo with link to homepage, desktop/mobile navigation menu, login button,
 * and language switcher dropdown
 *
 * @returns {JSX.Element} The NavbarPublic component
 */
const NavbarPublic = (): React.JSX.Element => {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const companyName = SITE_INFO.organization.name;
  const displayName = SITE_INFO.organization.displayName || companyName;
  const intl = useIntl();
  const { locale, onChangeLocale } = useConfig();
  const { isLoggedIn } = useAuth(); // Get authentication state
  // const { createHoverHandler } = useRoutePreloader(); // Removed over-engineered preloader

  // State to track which sections are visible
  const [visibleSections, setVisibleSections] = useState({
    header: false,
  });

  // Refs for the sections
  const headerRef = useRef(null);

  // Add intersection observer to check when sections are visible
  useEffect(() => {
    const observerOptions = {
      root: null,
      rootMargin: '0px',
      threshold: 0.1,
    };

    const observerCallback = (entries: IntersectionObserverEntry[]) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const sectionId = entry.target.getAttribute('data-section');
          if (sectionId) {
            setVisibleSections(prev => ({
              ...prev,
              [sectionId]: true,
            }));
          }
        }
      });
    };

    const observer = new IntersectionObserver(
      observerCallback,
      observerOptions
    );

    // Observe header section
    if (headerRef.current) observer.observe(headerRef.current);

    return () => {
      observer.disconnect();
    };
  }, []);

  const mainNavItems = [
    { name: intl.formatMessage({ id: 'nav.main.home' }), path: '/' },
    {
      name: intl.formatMessage(
        { id: 'navigation.about' },
        { defaultMessage: 'About' }
      ),
      path: '/about',
    },
    {
      name: intl.formatMessage(
        { id: 'navigation.blog' },
        { defaultMessage: 'Blog' }
      ),
      path: '/blog',
    },
    {
      name: intl.formatMessage(
        { id: 'navigation.contact' },
        { defaultMessage: 'Contact' }
      ),
      path: '/contact',
    },
    {
      name: intl.formatMessage(
        { id: 'navigation.faq' },
        { defaultMessage: 'FAQ' }
      ),
      path: '/faq',
    },
  ];

  /**
   * Renders the appropriate authentication button based on login status
   * @returns {JSX.Element} Authentication button (login or dashboard)
   */
  const renderAuthButton = () => {
    if (isLoggedIn) {
      return (
        <Link
          to="/dashboard"
          className="shimmer-effect rounded-md bg-primary px-4 py-2 text-primary-foreground transition-colors hover:bg-primary/90"
        >
          {intl.formatMessage(
            { id: 'navigation.dashboard' },
            { defaultMessage: 'Dashboard' }
          )}
        </Link>
      );
    }

    return (
      <Link
        to="/login"
        className="shimmer-effect rounded-md bg-primary px-4 py-2 text-primary-foreground transition-colors hover:bg-primary/90"
      >
        {intl.formatMessage({ id: 'page.login.submit' })}
      </Link>
    );
  };

  return (
    <header
      ref={headerRef}
      data-section="header"
      className={`section-reveal border-b border-border bg-background ${visibleSections.header ? 'visible' : ''}`}
    >
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between py-4">
          {/* Logo */}
          <Link to="/" className="animate-fadeIn flex items-center">
            <span className="text-xl font-bold text-primary">
              {displayName}
            </span>
          </Link>

          {/* Desktop Navigation */}
          <nav className="animate-fadeIn animation-delay-200 hidden space-x-8 md:flex">
            {mainNavItems.map((item, index) => (
              <Link
                key={item.path}
                to={item.path}
                className="shimmer-effect font-medium text-muted-foreground hover:text-primary"
                style={{ animationDelay: `${index * 100 + 200}ms` }}
              >
                {item.name}
              </Link>
            ))}
          </nav>

          {/* Desktop Actions */}
          <div className="animate-fadeIn animation-delay-400 hidden items-center space-x-4 md:flex">
            {/* Theme Switcher */}
            <ThemeSwitcher />

            {/* Language Switcher */}
            <DropdownMenu>
              <DropdownMenuTrigger className="flex items-center justify-center text-muted-foreground transition-colors hover:text-primary">
                <Globe className="h-5 w-5" />
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="min-w-[180px]">
                <DropdownMenuItem
                  onClick={() => onChangeLocale('en')}
                  className="flex items-center space-x-2"
                >
                  <div className="relative flex h-4 w-4 items-center justify-center">
                    <Check
                      className={cn(
                        'h-4 w-4',
                        locale === 'en' ? 'opacity-100' : 'opacity-0'
                      )}
                    />
                  </div>
                  <span>
                    {intl.formatMessage({ id: 'nav.user.language.english' })}
                  </span>
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => onChangeLocale('zh')}
                  className="flex items-center space-x-2"
                >
                  <div className="relative flex h-4 w-4 items-center justify-center">
                    <Check
                      className={cn(
                        'h-4 w-4',
                        locale === 'zh' ? 'opacity-100' : 'opacity-0'
                      )}
                    />
                  </div>
                  <span>
                    {intl.formatMessage({ id: 'nav.user.language.chinese' })}
                  </span>
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => onChangeLocale('zh-TW')}
                  className="flex items-center space-x-2"
                >
                  <div className="relative flex h-4 w-4 items-center justify-center">
                    <Check
                      className={cn(
                        'h-4 w-4',
                        locale === 'zh-TW' ? 'opacity-100' : 'opacity-0'
                      )}
                    />
                  </div>
                  <span>
                    {intl.formatMessage({
                      id: 'nav.user.language.traditional',
                    })}
                  </span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>

            {/* Auth Button (Login or Dashboard) */}
            {renderAuthButton()}
          </div>

          {/* Mobile menu button */}
          <button
            className="animate-fadeIn text-muted-foreground md:hidden"
            onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
          >
            {mobileMenuOpen ? (
              <svg
                className="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            ) : (
              <svg
                className="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 6h16M4 12h16M4 18h16"
                />
              </svg>
            )}
          </button>
        </div>

        {/* Mobile Navigation */}
        {mobileMenuOpen && (
          <nav className="animate-slideUp border-t border-border py-4 md:hidden">
            <div className="flex flex-col space-y-4">
              {mainNavItems.map((item, index) => (
                <Link
                  key={item.path}
                  to={item.path}
                  className="stagger-card font-medium text-muted-foreground hover:text-primary"
                  style={{ animationDelay: `${index * 100}ms` }}
                  onClick={() => setMobileMenuOpen(false)}
                >
                  {item.name}
                </Link>
              ))}

              {/* Theme Switcher for Mobile */}
              <div className="animate-fadeIn animation-delay-200 flex flex-col space-y-2 border-t border-border pt-2">
                <span className="text-sm font-medium text-muted-foreground">
                  {intl.formatMessage(
                    { id: 'nav.user.theme' },
                    { defaultMessage: 'Theme' }
                  )}
                </span>
                <div className="flex justify-start">
                  <ThemeSwitcher />
                </div>
              </div>

              {/* Language Switcher for Mobile */}
              <div className="animate-fadeIn animation-delay-300 flex flex-col space-y-2 border-t border-border pt-2">
                <span className="text-sm font-medium text-muted-foreground">
                  {intl.formatMessage({ id: 'nav.user.language' })}
                </span>
                <button
                  onClick={() => onChangeLocale('en')}
                  className="flex items-center justify-between text-muted-foreground hover:text-primary"
                >
                  <span>
                    {intl.formatMessage({ id: 'nav.user.language.english' })}
                  </span>
                  {locale === 'en' && <Check className="h-4 w-4" />}
                </button>
                <button
                  onClick={() => onChangeLocale('zh')}
                  className="flex items-center justify-between text-muted-foreground hover:text-primary"
                >
                  <span>
                    {intl.formatMessage({ id: 'nav.user.language.chinese' })}
                  </span>
                  {locale === 'zh' && <Check className="h-4 w-4" />}
                </button>
                <button
                  onClick={() => onChangeLocale('zh-TW')}
                  className="flex items-center justify-between text-muted-foreground hover:text-primary"
                >
                  <span>
                    {intl.formatMessage({
                      id: 'nav.user.language.traditional',
                    })}
                  </span>
                  {locale === 'zh-TW' && <Check className="h-4 w-4" />}
                </button>
              </div>

              {/* Auth Button for Mobile (Login or Dashboard) */}
              <div
                onClick={() => setMobileMenuOpen(false)}
                className="inline-block text-center"
              >
                {renderAuthButton()}
              </div>
            </div>
          </nav>
        )}
      </div>
    </header>
  );
};

export default NavbarPublic;

import * as React from 'react';
import { useIntl } from 'react-intl';
import { memo, useMemo } from 'react';
import { Link } from 'react-router-dom';
import { Bell, Lock, Settings, User, ArrowLeft } from 'lucide-react';

import {
  Sidebar,
  SidebarContent,
  SidebarHeader,
  SidebarFooter,
  SidebarRail,
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
} from '@/components/ui/shadcn/sidebar';
import { useActiveRoute } from '@/hooks/use-active-route';
import { cn } from '@/lib/utils';
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from '@/components/ui/shadcn/avatar';

/**
 * UserSidebar Component
 *
 * Provides navigation for user-related pages including:
 * - Profile
 * - Security
 * - Notifications
 * - Settings
 *
 * This sidebar is displayed only when users navigate to user-related pages.
 */
export const UserSidebar = memo(
  ({ ...props }: React.ComponentProps<typeof Sidebar>) => {
    const intl = useIntl();
    const { isActive } = useActiveRoute();

    // Memoize the static data structure with active states
    const data = useMemo(
      () => ({
        user: {
          name: 'capax',
          email: '<EMAIL>',
          avatar: '/avatars/capax.jpg',
        },
        navItems: [
          {
            title: intl.formatMessage({ id: 'nav.user.profile' }),
            url: '/user/profile',
            icon: User,
            isActive: isActive('/user/profile'),
          },
          {
            title: intl.formatMessage({ id: 'nav.user.security' }),
            url: '/user/security',
            icon: Lock,
            isActive: isActive('/user/security'),
          },
          {
            title: intl.formatMessage({ id: 'nav.user.notifications' }),
            url: '/user/notifications',
            icon: Bell,
            isActive: isActive('/user/notifications'),
          },
          {
            title: intl.formatMessage({ id: 'nav.user.settings' }),
            url: '/user/settings',
            icon: Settings,
            isActive: isActive('/user/settings'),
          },
        ],
      }),
      [intl, isActive]
    );

    return (
      <Sidebar collapsible="icon" {...props}>
        <SidebarHeader className="px-4 py-4">
          <div className="flex items-center gap-3">
            <Avatar className="h-10 w-10 rounded-full">
              <AvatarImage src={data.user.avatar} alt={data.user.name} />
              <AvatarFallback className="rounded-full">CA</AvatarFallback>
            </Avatar>
            <div className="hidden flex-col md:flex">
              <span className="text-sm font-semibold">{data.user.name}</span>
              <span className="text-xs text-muted-foreground">
                {data.user.email}
              </span>
            </div>
          </div>
        </SidebarHeader>
        <SidebarContent>
          <SidebarGroup>
            <SidebarGroupLabel>
              {intl.formatMessage({ id: 'nav.user.accountSettings' })}
            </SidebarGroupLabel>
            <SidebarMenu>
              {data.navItems.map(item => (
                <SidebarMenuItem key={item.url}>
                  <SidebarMenuButton
                    tooltip={item.title}
                    asChild
                    className={cn(
                      item.isActive &&
                        'bg-sidebar-accent text-sidebar-accent-foreground'
                    )}
                  >
                    <Link to={item.url}>
                      <item.icon className="h-4 w-4" />
                      <span>{item.title}</span>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroup>
        </SidebarContent>
        <SidebarFooter>
          <SidebarMenu>
            <SidebarMenuItem>
              <SidebarMenuButton
                tooltip={intl.formatMessage({ id: 'nav.user.backToHome' })}
                asChild
              >
                <Link to="/dashboard">
                  <ArrowLeft className="h-4 w-4" />
                  <span>
                    {intl.formatMessage({ id: 'nav.user.backToHome' })}
                  </span>
                </Link>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarFooter>
        <SidebarRail />
      </Sidebar>
    );
  }
);

UserSidebar.displayName = 'UserSidebar';

import React from 'react';
import { useIntl } from 'react-intl';
import { ArrowLeft, Calendar, User, Clock, Tag } from 'lucide-react';
import { Link } from 'react-router-dom';
import { getTranslation } from '@/utils/translation';
import { Translation } from '@/types/shared';
import SocialShare from '@/components/common/social-share';
import RelatedItems from '@/components/common/related-items';

export interface DetailLayoutProps {
  // Content data
  title: Translation;
  description?: Translation;
  content: Translation;

  // Metadata
  publishedDate?: string;
  lastUpdated?: string;
  author?: {
    name: string;
    avatar?: string;
    bio?: Translation;
  };
  category?: {
    name: Translation;
    slug: string;
    color?: string;
  };
  tags?: Array<{
    name: Translation;
    slug: string;
  }>;
  readingTime?: number;

  // Navigation
  backLink: {
    path: string;
    label: Translation;
  };

  // Related content
  relatedItems?: Array<{
    id: string;
    title: Translation;
    description?: Translation;
    image?: string;
    path: string;
    publishedDate?: string;
    category?: {
      name: Translation;
      color?: string;
    };
  }>;

  // Layout options
  showSocialShare?: boolean;
  showRelatedItems?: boolean;
  showAuthor?: boolean;
  showReadingTime?: boolean;
  showLastUpdated?: boolean;

  // Custom sections
  children?: React.ReactNode;
  sidebar?: React.ReactNode;
}

/**
 * Detail Layout Component
 *
 * A reusable layout component for detail pages (blog posts, news articles, case studies, etc.)
 * Features include:
 * - Responsive layout with optional sidebar
 * - Author information and metadata
 * - Social sharing buttons
 * - Related content suggestions
 * - Breadcrumb navigation
 * - Reading time estimation
 *
 * @param {DetailLayoutProps} props - The component props
 * @returns {JSX.Element} The Detail Layout component
 */
const DetailLayout: React.FC<DetailLayoutProps> = ({
  title,
  description,
  content,
  publishedDate,
  lastUpdated,
  author,
  category,
  tags,
  readingTime,
  backLink,
  relatedItems,
  showSocialShare = true,
  showRelatedItems = true,
  showAuthor = true,
  showReadingTime = true,
  showLastUpdated = true,
  children,
  sidebar,
}) => {
  const intl = useIntl();
  const locale = intl.locale;

  // Helper function to get translated text
  const getText = (text: Translation) => {
    return getTranslation(text, locale);
  };

  // Format date for display
  const formatDate = (dateString: string): string => {
    const options: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    };
    return new Date(dateString).toLocaleDateString(locale, options);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        {/* Back Navigation */}
        <div className="mb-6">
          <Link
            to={backLink.path}
            className="inline-flex items-center gap-2 text-blue-600 transition-colors hover:text-blue-700"
          >
            <ArrowLeft className="h-4 w-4" />
            {getText(backLink.label)}
          </Link>
        </div>

        <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
          {/* Main Content */}
          <div className="lg:col-span-2">
            <article className="overflow-hidden rounded-xl bg-white shadow-sm">
              {/* Article Header */}
              <div className="border-b border-gray-100 p-6 md:p-8">
                {/* Category */}
                {category && (
                  <div className="mb-4">
                    <span
                      className="inline-flex items-center rounded-full px-3 py-1 text-sm font-medium"
                      style={{
                        backgroundColor: category.color
                          ? `${category.color}20`
                          : '#f3f4f6',
                        color: category.color || '#6b7280',
                      }}
                    >
                      <Tag className="mr-1 h-3 w-3" />
                      {getText(category.name)}
                    </span>
                  </div>
                )}

                {/* Title */}
                <h1 className="mb-4 text-3xl font-bold text-gray-900 md:text-4xl">
                  {getText(title)}
                </h1>

                {/* Description */}
                {description && (
                  <p className="mb-6 text-xl text-gray-600">
                    {getText(description)}
                  </p>
                )}

                {/* Metadata */}
                <div className="flex flex-wrap items-center gap-4 text-sm text-gray-500">
                  {publishedDate && (
                    <div className="flex items-center gap-1">
                      <Calendar className="h-4 w-4" />
                      {formatDate(publishedDate)}
                    </div>
                  )}

                  {showReadingTime && readingTime && (
                    <div className="flex items-center gap-1">
                      <Clock className="h-4 w-4" />
                      {intl.formatMessage(
                        {
                          id: 'detail.reading.time',
                          defaultMessage: '{time} min read',
                        },
                        { time: readingTime }
                      )}
                    </div>
                  )}

                  {showLastUpdated && lastUpdated && (
                    <div className="text-xs">
                      {intl.formatMessage(
                        {
                          id: 'detail.last.updated',
                          defaultMessage: 'Updated {date}',
                        },
                        { date: formatDate(lastUpdated) }
                      )}
                    </div>
                  )}
                </div>

                {/* Author */}
                {showAuthor && author && (
                  <div className="mt-6 flex items-center gap-3 border-t border-gray-100 pt-6">
                    {author.avatar && (
                      <img
                        src={author.avatar}
                        alt={author.name}
                        className="h-12 w-12 rounded-full object-cover"
                      />
                    )}
                    <div>
                      <div className="flex items-center gap-1 text-sm font-medium text-gray-900">
                        <User className="h-4 w-4" />
                        {author.name}
                      </div>
                      {author.bio && (
                        <p className="mt-1 text-sm text-gray-600">
                          {getText(author.bio)}
                        </p>
                      )}
                    </div>
                  </div>
                )}
              </div>

              {/* Article Content */}
              <div className="p-6 md:p-8">
                <div
                  className="prose prose-gray prose-headings:text-gray-900 prose-a:text-blue-600 prose-a:no-underline hover:prose-a:underline max-w-none"
                  dangerouslySetInnerHTML={{ __html: getText(content) }}
                />

                {/* Custom Children Content */}
                {children && <div className="mt-8">{children}</div>}
              </div>

              {/* Tags */}
              {tags && tags.length > 0 && (
                <div className="border-t border-gray-100 px-6 pb-6 md:px-8">
                  <div className="mt-4 flex flex-wrap gap-2">
                    {tags.map(tag => (
                      <span
                        key={tag.slug}
                        className="inline-flex items-center rounded-full bg-gray-100 px-3 py-1 text-sm text-gray-700 transition-colors hover:bg-gray-200"
                      >
                        #{getText(tag.name)}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {/* Social Share */}
              {showSocialShare && (
                <div className="border-t border-gray-100 px-6 pb-6 md:px-8">
                  <SocialShare
                    title={getText(title)}
                    description={description ? getText(description) : undefined}
                  />
                </div>
              )}
            </article>

            {/* Related Items */}
            {showRelatedItems && relatedItems && relatedItems.length > 0 && (
              <div className="mt-8">
                <RelatedItems
                  items={relatedItems}
                  title={{
                    en: 'Related Articles',
                    zh: '相关文章',
                    'zh-TW': '相關文章',
                  }}
                />
              </div>
            )}
          </div>

          {/* Sidebar */}
          {sidebar && (
            <div className="lg:col-span-1">
              <div className="sticky top-8">{sidebar}</div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default DetailLayout;

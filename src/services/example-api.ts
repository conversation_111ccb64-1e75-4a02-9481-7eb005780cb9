/**
 * Example API Service
 *
 * Comprehensive example demonstrating best practices for API integration
 * including error handling, caching, retries, and type safety.
 */

import axiosServices from '@/utils/api/axios';
import { reportApiError } from '@/utils/error-reporting';
import type {
  PaginationParams,
  PaginatedResponse,
  ApiResponse,
  BaseEntity,
} from '@/types/api';

// ==========================================
// TYPE DEFINITIONS
// ==========================================

export interface ExampleItem extends BaseEntity {
  name: string;
  description: string;
  category: string;
  status: 'active' | 'inactive' | 'pending';
  metadata?: Record<string, any>;
}

export interface CreateExampleRequest {
  name: string;
  description: string;
  category: string;
  metadata?: Record<string, any>;
}

export interface UpdateExampleRequest {
  name?: string;
  description?: string;
  category?: string;
  status?: 'active' | 'inactive' | 'pending';
  metadata?: Record<string, any>;
}

export interface ExampleFilters {
  search?: string;
  category?: string;
  status?: 'active' | 'inactive' | 'pending';
  createdFrom?: string;
  createdTo?: string;
}

// ==========================================
// API ENDPOINTS
// ==========================================

const ENDPOINTS = {
  ITEMS: '/api/examples',
  ITEM_BY_ID: '/api/examples/{id}',
  BULK_OPERATIONS: '/api/examples/bulk',
  CATEGORIES: '/api/examples/categories',
  EXPORT: '/api/examples/export',
} as const;

// ==========================================
// CACHE MANAGEMENT
// ==========================================

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  expiresAt: number;
}

class ExampleCache {
  private static cache = new Map<string, CacheEntry<any>>();
  private static readonly DEFAULT_TTL = 5 * 60 * 1000; // 5 minutes

  static get<T>(key: string): T | null {
    const entry = this.cache.get(key);
    if (!entry) return null;

    if (Date.now() > entry.expiresAt) {
      this.cache.delete(key);
      return null;
    }

    return entry.data;
  }

  static set<T>(key: string, data: T, ttl = this.DEFAULT_TTL): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      expiresAt: Date.now() + ttl,
    });
  }

  static clear(pattern?: string): void {
    if (pattern) {
      const regex = new RegExp(pattern);
      for (const key of this.cache.keys()) {
        if (regex.test(key)) {
          this.cache.delete(key);
        }
      }
    } else {
      this.cache.clear();
    }
  }

  static invalidate(id: string): void {
    // Invalidate related cache entries
    this.clear(`example_${id}`);
    this.clear('examples_list');
    this.clear('examples_categories');
  }
}

// ==========================================
// MOCK DATA GENERATION (FOR DEMO)
// ==========================================

const generateMockExampleItems = (
  params: PaginationParams & ExampleFilters = {}
): PaginatedResponse<ExampleItem> => {
  const { page = 0, size = 10, search, category, status } = params;

  // Generate mock items
  const allItems: ExampleItem[] = Array.from({ length: 50 }, (_, index) => ({
    id: `item-${index + 1}`,
    name: `Example Item ${index + 1}`,
    description: `This is a description for example item ${index + 1}. It demonstrates the API integration patterns.`,
    category: ['example', 'demo', 'test', 'sample'][index % 4],
    status: (['active', 'inactive', 'pending'] as const)[index % 3],
    createdAt: new Date(
      Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000
    ).toISOString(),
    updatedAt: new Date(
      Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000
    ).toISOString(),
    metadata: {
      priority: ['high', 'medium', 'low'][index % 3],
      tags: [`tag-${index % 5}`, `category-${index % 3}`],
    },
  }));

  // Apply filters
  let filteredItems = allItems;

  if (search) {
    const searchLower = search.toLowerCase();
    filteredItems = filteredItems.filter(
      item =>
        item.name.toLowerCase().includes(searchLower) ||
        item.description.toLowerCase().includes(searchLower)
    );
  }

  if (category) {
    filteredItems = filteredItems.filter(item => item.category === category);
  }

  if (status) {
    filteredItems = filteredItems.filter(item => item.status === status);
  }

  // Apply pagination
  const startIndex = page * size;
  const endIndex = startIndex + size;
  const paginatedItems = filteredItems.slice(startIndex, endIndex);

  return {
    content: paginatedItems,
    totalElements: filteredItems.length,
    totalPages: Math.ceil(filteredItems.length / size),
    size,
    number: page,
    first: page === 0,
    last: page >= Math.ceil(filteredItems.length / size) - 1,
    numberOfElements: paginatedItems.length,
    empty: paginatedItems.length === 0,
  };
};

// ==========================================
// UTILITY FUNCTIONS
// ==========================================

const buildUrl = (template: string, params: Record<string, string>): string => {
  return Object.entries(params).reduce(
    (url, [key, value]) => url.replace(`{${key}}`, encodeURIComponent(value)),
    template
  );
};

const handleApiError = (
  error: any,
  operation: string,
  context?: any
): never => {
  const apiError = new Error(
    error.response?.data?.message || error.message || `Failed to ${operation}`
  );

  // Add additional context to the error
  (apiError as any).status = error.response?.status;
  (apiError as any).code = error.response?.data?.code;
  (apiError as any).context = context;

  // Report the error
  reportApiError(apiError, operation, undefined, { extra: context });

  throw apiError;
};

// ==========================================
// API SERVICE FUNCTIONS
// ==========================================

/**
 * Fetch paginated list of example items
 */
export const fetchExampleItems = async (
  params: PaginationParams & ExampleFilters = {}
): Promise<PaginatedResponse<ExampleItem>> => {
  try {
    const cacheKey = `examples_list_${JSON.stringify(params)}`;
    const cached = ExampleCache.get<PaginatedResponse<ExampleItem>>(cacheKey);

    if (cached) {
      return cached;
    }

    // For demo purposes, return mock data instead of making real API calls
    // This prevents 405 errors when the API endpoint doesn't exist
    const mockData = generateMockExampleItems(params);
    ExampleCache.set(cacheKey, mockData);

    return mockData;
  } catch (error) {
    return handleApiError(error, 'fetch example items', { params });
  }
};

/**
 * Fetch single example item by ID
 */
export const fetchExampleItemById = async (
  id: string
): Promise<ExampleItem> => {
  try {
    const cacheKey = `example_${id}`;
    const cached = ExampleCache.get<ExampleItem>(cacheKey);

    if (cached) {
      return cached;
    }

    // For demo purposes, return mock data
    const mockItem: ExampleItem = {
      id,
      name: `Example Item ${id}`,
      description: `This is a detailed description for example item ${id}. It demonstrates the API integration patterns.`,
      category: 'example',
      status: 'active',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      metadata: {
        priority: 'medium',
        tags: ['demo', 'example'],
      },
    };

    ExampleCache.set(cacheKey, mockItem);
    return mockItem;
  } catch (error) {
    return handleApiError(error, 'fetch example item', { id });
  }
};

/**
 * Create new example item
 */
export const createExampleItem = async (
  data: CreateExampleRequest
): Promise<ExampleItem> => {
  try {
    // For demo purposes, simulate API call with delay and return mock data
    await new Promise(resolve => setTimeout(resolve, 500)); // Simulate network delay

    const mockItem: ExampleItem = {
      id: `item-${Date.now()}`,
      name: data.name,
      description: data.description,
      category: data.category,
      status: 'active',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      metadata: data.metadata,
    };

    // Invalidate related cache entries
    ExampleCache.clear('examples_list');
    ExampleCache.clear('examples_categories');

    return mockItem;
  } catch (error) {
    return handleApiError(error, 'create example item', { data });
  }
};

/**
 * Update existing example item
 */
export const updateExampleItem = async (
  id: string,
  data: UpdateExampleRequest
): Promise<ExampleItem> => {
  try {
    const url = buildUrl(ENDPOINTS.ITEM_BY_ID, { id });
    const response = await axiosServices.put<ApiResponse<ExampleItem>>(
      url,
      data
    );

    const result = response.data.data;

    // Update cache and invalidate related entries
    ExampleCache.set(`example_${id}`, result);
    ExampleCache.clear('examples_list');

    return result;
  } catch (error) {
    return handleApiError(error, 'update example item', { id, data });
  }
};

/**
 * Delete example item
 */
export const deleteExampleItem = async (id: string): Promise<void> => {
  try {
    // For demo purposes, simulate API call with delay
    await new Promise(resolve => setTimeout(resolve, 300)); // Simulate network delay

    // Invalidate cache
    ExampleCache.invalidate(id);
  } catch (error) {
    handleApiError(error, 'delete example item', { id });
  }
};

/**
 * Bulk operations on example items
 */
export const bulkOperateExampleItems = async (
  operation: 'delete' | 'activate' | 'deactivate',
  ids: string[]
): Promise<{
  successful: string[];
  failed: Array<{ id: string; error: string }>;
}> => {
  try {
    const response = await axiosServices.post(ENDPOINTS.BULK_OPERATIONS, {
      operation,
      ids,
    });

    // Invalidate cache for affected items
    ids.forEach(id => ExampleCache.invalidate(id));

    return response.data.data;
  } catch (error) {
    return handleApiError(error, 'bulk operate example items', {
      operation,
      ids,
    });
  }
};

/**
 * Fetch available categories
 */
export const fetchExampleCategories = async (): Promise<string[]> => {
  try {
    const cacheKey = 'examples_categories';
    const cached = ExampleCache.get<string[]>(cacheKey);

    if (cached) {
      return cached;
    }

    const response = await axiosServices.get<ApiResponse<string[]>>(
      ENDPOINTS.CATEGORIES
    );

    const result = response.data.data;
    ExampleCache.set(cacheKey, result, 10 * 60 * 1000); // Cache for 10 minutes

    return result;
  } catch (error) {
    return handleApiError(error, 'fetch example categories');
  }
};

/**
 * Export example items
 */
export const exportExampleItems = async (
  format: 'csv' | 'xlsx' | 'json',
  filters: ExampleFilters = {}
): Promise<{ downloadUrl: string; filename: string }> => {
  try {
    const response = await axiosServices.post(ENDPOINTS.EXPORT, {
      format,
      filters,
    });

    return response.data.data;
  } catch (error) {
    return handleApiError(error, 'export example items', { format, filters });
  }
};

// ==========================================
// ADVANCED FEATURES
// ==========================================

/**
 * Search example items with debouncing
 */
let searchTimeout: number | null = null;

export const searchExampleItems = async (
  query: string,
  debounceMs = 300
): Promise<ExampleItem[]> => {
  return new Promise((resolve, reject) => {
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }

    searchTimeout = window.setTimeout(async () => {
      try {
        const response = await fetchExampleItems({
          search: query,
          size: 20, // Limit search results
        });
        resolve(response.content);
      } catch (error) {
        reject(error);
      }
    }, debounceMs);
  });
};

/**
 * Optimistic update helper
 */
export const optimisticUpdateExampleItem = async (
  id: string,
  updates: UpdateExampleRequest,
  optimisticData: ExampleItem
): Promise<ExampleItem> => {
  // Apply optimistic update to cache
  ExampleCache.set(`example_${id}`, optimisticData);

  try {
    // Perform actual update
    const result = await updateExampleItem(id, updates);
    return result;
  } catch (error) {
    // Revert optimistic update on error
    ExampleCache.clear(`example_${id}`);
    throw error;
  }
};

/**
 * Prefetch related data
 */
export const prefetchExampleData = async (id: string): Promise<void> => {
  try {
    // Prefetch item details and categories in parallel
    await Promise.all([fetchExampleItemById(id), fetchExampleCategories()]);
  } catch (error) {
    // Prefetch errors are non-critical
    console.warn('Failed to prefetch example data:', error);
  }
};

// ==========================================
// SERVICE CLASS (Alternative Pattern)
// ==========================================

export class ExampleService {
  private static instance: ExampleService;

  static getInstance(): ExampleService {
    if (!ExampleService.instance) {
      ExampleService.instance = new ExampleService();
    }
    return ExampleService.instance;
  }

  async getItems(params?: PaginationParams & ExampleFilters) {
    return fetchExampleItems(params);
  }

  async getById(id: string) {
    return fetchExampleItemById(id);
  }

  async create(data: CreateExampleRequest) {
    return createExampleItem(data);
  }

  async update(id: string, data: UpdateExampleRequest) {
    return updateExampleItem(id, data);
  }

  async delete(id: string) {
    return deleteExampleItem(id);
  }

  async search(query: string) {
    return searchExampleItems(query);
  }

  async getCategories() {
    return fetchExampleCategories();
  }

  async export(format: 'csv' | 'xlsx' | 'json', filters?: ExampleFilters) {
    return exportExampleItems(format, filters);
  }

  clearCache(pattern?: string) {
    ExampleCache.clear(pattern);
  }
}

// ==========================================
// DEFAULT EXPORT
// ==========================================

export default {
  // CRUD operations
  fetchExampleItems,
  fetchExampleItemById,
  createExampleItem,
  updateExampleItem,
  deleteExampleItem,

  // Bulk operations
  bulkOperateExampleItems,

  // Utility functions
  fetchExampleCategories,
  exportExampleItems,
  searchExampleItems,
  optimisticUpdateExampleItem,
  prefetchExampleData,

  // Service class
  ExampleService,

  // Cache management
  clearCache: ExampleCache.clear.bind(ExampleCache),
};

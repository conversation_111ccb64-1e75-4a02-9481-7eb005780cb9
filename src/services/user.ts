/**
 * User Service
 *
 * This service handles user-related API calls and provides fallback to mock data
 * when the API is not available or returns errors.
 */

import axiosServices from '@/utils/api/axios';
import { SystemService } from './system';
import type {
  UserProfileData,
  NotificationData,
  NotificationSettings,
  SecurityData,
  UserSettingsData,
} from '@/types/user';

// Import mock data as fallback
import {
  mockUserProfile,
  mockNotifications,
  mockNotificationSettings,
  mockSecurityData,
  mockUserSettings,
} from '@/data/user';

/**
 * User API Service
 * Provides methods to fetch user data with automatic fallback to mock data
 */
export class UserService {
  /**
   * Get current user profile
   * Tries /api/account/me first, falls back to mock data
   */
  static async getUserProfile(): Promise<UserProfileData> {
    try {
      // Check API availability using SystemService
      const isApiAvailable = await SystemService.getOrCheckApiAvailability();

      // If API is available, try to fetch real data
      if (isApiAvailable) {
        const response = await axiosServices.get('/api/account/me');

        // Transform API response to match our UserProfileData interface
        const userData = response.data;
        return {
          firstName: userData.firstName || userData.first_name || 'User',
          lastName: userData.lastName || userData.last_name || '',
          email: userData.email,
          phone: userData.phone || '',
          jobTitle: userData.jobTitle || userData.job_title || '',
          company: userData.company || '',
          bio: userData.bio || '',
          location: userData.location || '',
          avatarUrl:
            userData.avatarUrl ||
            userData.avatar_url ||
            userData.avatar ||
            null,
          joinDate:
            userData.createdAt || userData.created_at || userData.joinDate,
          lastLogin: userData.lastLogin || userData.last_login || 'Recently',
        };
      }
    } catch (error) {
      console.warn(
        'Failed to fetch user profile from API, using mock data:',
        error
      );
    }

    // Return mock data as fallback
    return mockUserProfile;
  }

  /**
   * Get user notifications
   * Tries /api/notifications first, falls back to mock data
   */
  static async getUserNotifications(): Promise<NotificationData[]> {
    try {
      // Check API availability using SystemService
      const isApiAvailable = await SystemService.getOrCheckApiAvailability();

      if (isApiAvailable) {
        const response = await axiosServices.get('/api/notifications');

        // Transform API response to match our NotificationData interface
        return response.data.map((notification: any) => ({
          id: notification.id,
          type: notification.type || 'system',
          title: notification.title,
          message: notification.message || notification.content,
          date:
            notification.createdAt ||
            notification.created_at ||
            notification.date,
          read: notification.read || notification.is_read || false,
          actionUrl: notification.actionUrl || notification.action_url || '#',
          user: notification.user
            ? {
                name:
                  notification.user.name ||
                  `${notification.user.firstName} ${notification.user.lastName}`,
                avatar:
                  notification.user.avatar || notification.user.avatarUrl || '',
              }
            : undefined,
        }));
      }
    } catch (error) {
      console.warn(
        'Failed to fetch notifications from API, using mock data:',
        error
      );
    }

    return mockNotifications;
  }

  /**
   * Get notification settings
   * Tries /api/settings/notifications first, falls back to mock data
   */
  static async getNotificationSettings(): Promise<NotificationSettings> {
    try {
      // Check API availability using SystemService
      const isApiAvailable = await SystemService.getOrCheckApiAvailability();

      if (isApiAvailable) {
        const response = await axiosServices.get('/api/settings/notifications');

        return {
          email: response.data.email ?? true,
          browser: response.data.browser ?? true,
          mobile: response.data.mobile ?? true,
        };
      }
    } catch (error) {
      console.warn(
        'Failed to fetch notification settings from API, using mock data:',
        error
      );
    }

    return mockNotificationSettings;
  }

  /**
   * Get security data
   * Tries /api/security first, falls back to mock data
   */
  static async getSecurityData(): Promise<SecurityData> {
    try {
      // Check API availability using SystemService
      const isApiAvailable = await SystemService.getOrCheckApiAvailability();

      if (isApiAvailable) {
        const response = await axiosServices.get('/api/security');
        const data = response.data;

        return {
          email: data.email,
          passwordLastChanged:
            data.passwordLastChanged || data.password_last_changed,
          twoFactorEnabled:
            data.twoFactorEnabled || data.two_factor_enabled || false,
          recoveryCodesRemaining:
            data.recoveryCodesRemaining || data.recovery_codes_remaining || 0,
          activeSessions: data.activeSessions || data.active_sessions || [],
          securityEvents: data.securityEvents || data.security_events || [],
        };
      }
    } catch (error) {
      console.warn(
        'Failed to fetch security data from API, using mock data:',
        error
      );
    }

    return mockSecurityData;
  }

  /**
   * Get user settings
   * Tries /api/settings first, falls back to mock data
   */
  static async getUserSettings(): Promise<UserSettingsData> {
    try {
      // Check API availability using SystemService
      const isApiAvailable = await SystemService.getOrCheckApiAvailability();

      if (isApiAvailable) {
        const response = await axiosServices.get('/api/settings');
        const data = response.data;

        return {
          name: data.name || `${data.firstName} ${data.lastName}`,
          email: data.email,
          avatar: data.avatar || data.avatarUrl || '',
          jobTitle: data.jobTitle || data.job_title || '',
          company: data.company || '',
          bio: data.bio || '',
          location: data.location || '',
          timezone: data.timezone || 'UTC',
          language: data.language || 'en',
          theme: data.theme || 'system',
          notifications: data.notifications || mockUserSettings.notifications,
          connectedAccounts:
            data.connectedAccounts ||
            data.connected_accounts ||
            mockUserSettings.connectedAccounts,
        };
      }
    } catch (error) {
      console.warn(
        'Failed to fetch user settings from API, using mock data:',
        error
      );
    }

    return mockUserSettings;
  }

  /**
   * Update user profile
   * Tries to send to API first, falls back to local state update
   */
  static async updateUserProfile(
    profileData: Partial<UserProfileData>
  ): Promise<UserProfileData> {
    try {
      // Check API availability using SystemService
      const isApiAvailable = await SystemService.getOrCheckApiAvailability();

      if (isApiAvailable) {
        const response = await axiosServices.put(
          '/api/account/me',
          profileData
        );
        return response.data;
      }
    } catch (error) {
      console.warn(
        'Failed to update user profile via API, returning updated mock data:',
        error
      );
    }

    // Return updated mock data (in a real implementation, you might want to update a store)
    return { ...mockUserProfile, ...profileData };
  }

  /**
   * Reset API availability check
   * Useful for retrying API connection
   * @deprecated Use SystemService.resetApiStatus() instead
   */
  static resetApiCheck(): void {
    SystemService.resetApiStatus();
  }

  /**
   * Get current API status
   * @deprecated Use SystemService.isApiAvailable() instead
   */
  static isApiAvailable(): boolean | null {
    return SystemService.isApiAvailable();
  }
}

// Export individual functions for easier usage
export const getUserProfile = () => UserService.getUserProfile();
export const getUserNotifications = () => UserService.getUserNotifications();
export const getNotificationSettings = () =>
  UserService.getNotificationSettings();
export const getSecurityData = () => UserService.getSecurityData();
export const getUserSettings = () => UserService.getUserSettings();
export const updateUserProfile = (data: Partial<UserProfileData>) =>
  UserService.updateUserProfile(data);

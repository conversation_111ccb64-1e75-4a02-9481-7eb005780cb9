import { Navigate } from 'react-router-dom';
import { lazy } from 'react';
import { RouteErrorBoundary } from '@/components/base/boundary/route-error-boundary';

// project imports
import MainLayout from '@/layout/main-layout/index';
import Loadable from '@/components/base/loadable';
import { AuthGuard } from '@/utils';

// dashboard routing - simple lazy loading
const DashboardDefault = Loadable(lazy(() => import('@/pages/dashboard/page')));

// support pages
const SupportPage = Loadable(lazy(() => import('@/pages/public/support/page')));
const SupportTickets = Loadable(
  lazy(() => import('@/pages/public/support/tickets/page'))
);

// demo pages
const ErrorBoundariesDemo = Loadable(
  lazy(() => import('@/pages/demo/boundary/page'))
);
const ToastDemo = Loadable(lazy(() => import('@/pages/demo/toast/page')));
const ApiIntegrationDemo = Loadable(
  lazy(() => import('@/pages/demo/api-integration/page'))
);

// ==============================|| MAIN ROUTING ||============================== //

/**
 * Main routes with standard layout
 * Handles dashboard and general application pages
 */
const MainRoutes = {
  path: '/',
  element: (
    <AuthGuard>
      <MainLayout />
    </AuthGuard>
  ),
  errorElement: <RouteErrorBoundary />,
  children: [
    {
      path: '',
      element: <Navigate to="dashboard" replace />,
    },
    {
      path: 'dashboard',
      element: <DashboardDefault />,
    },

    // Support Pages
    {
      path: 'support',
      element: <SupportPage />,
    },
    {
      path: 'support/tickets',
      element: <SupportTickets />,
    },

    // Demo Pages
    {
      path: 'demo/error-boundaries',
      element: <ErrorBoundariesDemo />,
    },
    {
      path: 'demo/toast',
      element: <ToastDemo />,
    },
    {
      path: 'demo/api-integration',
      element: <ApiIntegrationDemo />,
    },
  ],
};

export default MainRoutes;

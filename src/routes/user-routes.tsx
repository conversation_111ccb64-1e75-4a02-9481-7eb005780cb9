import { Navigate } from 'react-router-dom';
import { lazy } from 'react';
import { RouteErrorBoundary } from '@/components/base/boundary/route-error-boundary';

// project imports
import UserLayout from '@/layout/user-layout';
import Loadable from '@/components/base/loadable';
import { AuthGuard } from '@/utils';
import { SidebarProvider } from '@/components/ui/shadcn/sidebar';

// user profile pages - simple lazy loading
const UserProfile = Loadable(lazy(() => import('@/pages/user/profile/page')));
const UserSettings = Loadable(lazy(() => import('@/pages/user/settings/page')));
const UserNotifications = Loadable(
  lazy(() => import('@/pages/user/notifications/page'))
);
const UserSecurity = Loadable(lazy(() => import('@/pages/user/security/page')));

// ==============================|| USER ROUTING ||============================== //

/**
 * User routes with dedicated layout
 * Handles all user-related pages with specialized sidebar navigation
 */
const UserRoutes = {
  path: '/user',
  element: (
    <AuthGuard>
      <SidebarProvider>
        <UserLayout />
      </SidebarProvider>
    </AuthGuard>
  ),
  errorElement: <RouteErrorBoundary />,
  children: [
    {
      path: '',
      element: <Navigate to="profile" replace />,
    },
    {
      path: 'profile',
      element: <UserProfile />,
    },
    {
      path: 'settings',
      element: <UserSettings />,
    },
    {
      path: 'notifications',
      element: <UserNotifications />,
    },
    {
      path: 'security',
      element: <UserSecurity />,
    },
  ],
};

export default UserRoutes;

// ==============================|| PUBLIC ROUTES ||============================== //
// This route configuration is specifically for public pages that don't require authentication:
// - Legal pages (Privacy Policy, Terms of Service, etc.)
// - Information pages (About Us, Contact Us, etc.)
// - Error pages (404, 500, Maintenance)
// These pages use a minimal layout without requiring user authentication

import { lazy } from 'react';
import { RouteErrorBoundary } from '@/components/base/boundary/route-error-boundary';

// project imports
import Loadable from '@/components/base/loadable';
import PublicLayout from '@/layout/public-layout';

// Home page and public pages - simple lazy loading
const HomePage = Loadable(lazy(() => import('@/pages/public/info/home/<USER>')));

// Legal pages
const PrivacyPolicy = Loadable(
  lazy(() => import('@/pages/public/policies/privacy/page'))
);
const TermsOfService = Loadable(
  lazy(() => import('@/pages/public/policies/terms/page'))
);
const CookiePolicy = Loadable(
  lazy(() => import('@/pages/public/policies/cookies/page'))
);
const GDPRCompliance = Loadable(
  lazy(() => import('@/pages/public/policies/gdpr/page'))
);

// Information pages
const AboutUs = Loadable(lazy(() => import('@/pages/public/info/about/page')));
const ContactUs = Loadable(
  lazy(() => import('@/pages/public/info/contact/page'))
);
const TeamPage = Loadable(lazy(() => import('@/pages/public/info/team/page')));
const CareersPage = Loadable(
  lazy(() => import('@/pages/public/info/careers/page'))
);
const JobApplicationPage = Loadable(
  lazy(() => import('@/pages/public/info/careers/apply/page'))
);
const FAQPage = Loadable(lazy(() => import('@/pages/public/info/faq/page')));
const BlogPage = Loadable(lazy(() => import('@/pages/public/info/blog/page')));
const BlogDetailPage = Loadable(
  lazy(() => import('@/pages/public/info/blog/detail/page'))
);
const HelpPage = Loadable(lazy(() => import('@/pages/public/info/help/page')));
const HelpArticlePage = Loadable(
  lazy(() => import('@/pages/public/info/help/article/page'))
);
const SupportArticlePage = Loadable(
  lazy(() => import('@/pages/public/support/article/page'))
);
const SupportPage = Loadable(lazy(() => import('@/pages/public/support/page')));
const SupportTicketsPage = Loadable(
  lazy(() => import('@/pages/public/support/tickets/page'))
);
const SupportTicketCreatePage = Loadable(
  lazy(() => import('@/pages/public/support/tickets/create/page'))
);
const ResourcesPage = Loadable(
  lazy(() => import('@/pages/public/info/resources/page'))
);
const ResourceDetailPage = Loadable(
  lazy(() => import('@/pages/public/info/resources/detail/page'))
);

// TODO: New Enterprise Pages - Add these imports as pages are created
const ServicesPage = Loadable(
  lazy(() => import('@/pages/public/info/services/page'))
);
const TestimonialsPage = Loadable(
  lazy(() => import('@/pages/public/info/testimonials/page'))
);
const PricingPage = Loadable(
  lazy(() => import('@/pages/public/info/pricing/page'))
);
const NewsPage = Loadable(lazy(() => import('@/pages/public/info/news/page')));
const NewsDetailPage = Loadable(
  lazy(() => import('@/pages/public/info/news/detail/page'))
);
const PartnersPage = Loadable(
  lazy(() => import('@/pages/public/info/partners/page'))
);
const CaseStudiesPage = Loadable(
  lazy(() => import('@/pages/public/info/case-studies/page'))
);
const CaseStudyDetailPage = Loadable(
  lazy(() => import('@/pages/public/info/case-studies/detail/page'))
);
const EventsPage = Loadable(
  lazy(() => import('@/pages/public/info/events/page'))
);
const EventDetailPage = Loadable(
  lazy(() => import('@/pages/public/info/events/detail/page'))
);
const SecurityPage = Loadable(
  lazy(() => import('@/pages/public/info/security/page'))
);
const IntegrationsPage = Loadable(
  lazy(() => import('@/pages/public/info/integrations/page'))
);
const ChangelogPage = Loadable(
  lazy(() => import('@/pages/public/info/changelog/page'))
);

// Error and maintenance pages
const NotFoundPage = Loadable(lazy(() => import('@/pages/error/404/page')));
const ServerErrorPage = Loadable(lazy(() => import('@/pages/error/500/page')));
const MaintenancePage = Loadable(
  lazy(() => import('@/pages/error/maintenance/page'))
);

// ==============================|| PUBLIC ROUTING ||============================== //

const AuthenticationRoutes = {
  path: '/',
  element: <PublicLayout />,
  errorElement: <RouteErrorBoundary />,
  children: [
    // Home Page (root)
    {
      path: '/',
      element: <HomePage />,
    },
    // Alternative route for /home
    {
      path: '/home',
      element: <HomePage />,
    },

    // Legal Pages
    {
      path: '/privacy',
      element: <PrivacyPolicy />,
    },
    {
      path: '/terms',
      element: <TermsOfService />,
    },
    {
      path: '/cookies',
      element: <CookiePolicy />,
    },
    {
      path: '/gdpr',
      element: <GDPRCompliance />,
    },

    // Information Pages
    {
      path: '/about',
      element: <AboutUs />,
    },
    {
      path: '/contact',
      element: <ContactUs />,
    },
    {
      path: '/team',
      element: <TeamPage />,
    },
    {
      path: '/careers',
      element: <CareersPage />,
    },
    {
      path: '/careers/apply/:id',
      element: <JobApplicationPage />,
    },
    {
      path: '/faq',
      element: <FAQPage />,
    },
    {
      path: '/blog',
      element: <BlogPage />,
    },
    {
      path: '/blog/:slug',
      element: <BlogDetailPage />,
    },
    {
      path: '/help',
      element: <HelpPage />,
    },
    {
      path: '/help/:id',
      element: <HelpArticlePage />,
    },
    {
      path: '/support/article/:id',
      element: <SupportArticlePage />,
    },
    {
      path: '/support',
      element: <SupportPage />,
    },
    {
      path: '/support/tickets',
      element: <SupportTicketsPage />,
    },
    {
      path: '/support/tickets/create',
      element: <SupportTicketCreatePage />,
    },
    {
      path: '/resources',
      element: <ResourcesPage />,
    },
    {
      path: '/resources/:slug',
      element: <ResourceDetailPage />,
    },
    {
      path: '/services',
      element: <ServicesPage />,
    },
    {
      path: '/testimonials',
      element: <TestimonialsPage />,
    },
    {
      path: '/pricing',
      element: <PricingPage />,
    },
    {
      path: '/news',
      element: <NewsPage />,
    },
    {
      path: '/news/:slug',
      element: <NewsDetailPage />,
    },
    {
      path: '/partners',
      element: <PartnersPage />,
    },
    {
      path: '/case-studies',
      element: <CaseStudiesPage />,
    },
    {
      path: '/case-studies/:slug',
      element: <CaseStudyDetailPage />,
    },
    {
      path: '/events',
      element: <EventsPage />,
    },
    {
      path: '/events/:slug',
      element: <EventDetailPage />,
    },
    {
      path: '/security',
      element: <SecurityPage />,
    },
    {
      path: '/integrations',
      element: <IntegrationsPage />,
    },
    {
      path: '/changelog',
      element: <ChangelogPage />,
    },

    // Error Pages
    {
      path: '/404',
      element: <NotFoundPage />,
    },
    {
      path: '/500',
      element: <ServerErrorPage />,
    },
    {
      path: '/maintenance',
      element: <MaintenancePage />,
    },
    {
      path: '*',
      element: <NotFoundPage />,
    },
  ],
};

export default AuthenticationRoutes;

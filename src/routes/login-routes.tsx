// ==============================|| LOGIN ROUTES ||============================== //
// This route configuration is specifically for non-authenticated users, including:
// - Login page
// - Registration page
// - Forgot password page
// - Signup page (redirects to register)
// These pages use a minimal layout and are protected by GuestGuard to prevent
// access by already authenticated users

import { lazy } from 'react';
import { RouteErrorBoundary } from '@/components/base/boundary/route-error-boundary';

// project imports
import { GuestGuard } from '@/utils';
import NavMotion from '@/layout/nav-motion';
import MinimalLayout from '@/layout/minimal-layout';
import Loadable from '@/components/base/loadable';

// login routing - simple lazy loading
const AuthLogin = Loadable(lazy(() => import('@/pages/login/page')));
const AuthLogin2 = Loadable(lazy(() => import('@/pages/login/page2')));
const AuthRegister = Loadable(lazy(() => import('@/pages/register/page')));
const AuthSignup = Loadable(lazy(() => import('@/pages/signup/page')));
const AuthForgotPassword = Loadable(lazy(() => import('@/pages/forgot/page')));

const LoginRoutes = {
  path: '/',
  element: (
    <NavMotion>
      <GuestGuard>
        <MinimalLayout />
      </GuestGuard>
    </NavMotion>
  ),
  errorElement: <RouteErrorBoundary />,
  children: [
    {
      path: '/login',
      element: <AuthLogin />,
    },
    {
      path: '/login2',
      element: <AuthLogin2 />,
    },
    {
      path: '/register',
      element: <AuthRegister />,
    },
    {
      path: '/signup',
      element: <AuthSignup />,
    },
    {
      path: '/forgot',
      element: <AuthForgotPassword />,
    },
  ],
};

export default LoginRoutes;

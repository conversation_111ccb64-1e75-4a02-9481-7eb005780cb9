// action - state management
import { LOGIN, LOGOUT } from './actions';
import { InitialLoginContextProps } from '@/types/auth';

// ==============================|| ACCOUNT REDUCER ||============================== //

type ActionType =
  | { type: typeof LOGIN; payload: { isLoggedIn: boolean; user: any } }
  | { type: typeof LOGOUT };

const accountReducer = (
  state: InitialLoginContextProps,
  action: ActionType
): InitialLoginContextProps => {
  switch (action.type) {
    case LOGIN:
      return {
        ...state,
        isLoggedIn: true,
        isInitialized: true,
        user: action.payload.user,
      };
    case LOGOUT:
      return {
        ...state,
        isInitialized: true,
        isLoggedIn: false,
        user: null,
      };
    default:
      return state;
  }
};

export default accountReducer;
